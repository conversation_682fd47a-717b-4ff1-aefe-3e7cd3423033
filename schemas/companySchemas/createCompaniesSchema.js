'use strict';
const joi = require('../Joi');
const { implicitSupportAnswersSchema } = require('../constants');

module.exports = joi.array().items(
  joi.object({
    parentCompanyName: joi.string().required().allow(null),
    name: joi.string().max(255).required(),
    industry: joi.string().industry().required(),
    country: joi.string().country().required(),
    creditRating: joi
      .object({
        rating: joi.string().rating().required().allow(null),
        ratingAdj: joi.any().valid(null).required(),
        probabilityOfDefault: joi.number().probabilityOfDefault().required().allow(null),
        probabilityOfDefaultAdj: joi.any().valid(null).required(),
      })
      .required(),
    assessment: joi.object({ answers: implicitSupportAnswersSchema }).required().allow(null),
  }),
);
