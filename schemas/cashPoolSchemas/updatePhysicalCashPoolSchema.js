'use strict';
const joi = require('../Joi');
const { getNordicPhysicalRiskAnalysisAnswers } = require('./riskAnalysisAnswers');

module.exports = joi.object({
  name: joi.string().required(),
  creditInterestRate: joi.number().required(),
  debitInterestRate: joi.number().required(),
  interestType: joi.string().rateType().required(),
  operatingCost: joi.number().allow(null),
  operatingCostMarkup: joi.number().allow(null),
  overnightRate: joi.string().cashPoolOvernightRate().allow(null),
  riskAnalysisAnswers: getNordicPhysicalRiskAnalysisAnswers(joi),
  totalRisk: joi.number().required(),
  assessment: joi.string().functionalAnalysisAssessment().required(),
  estimateRatesCalculationLog: joi.any().allow(null),
  accounts: joi.array().items(
    joi.object({
      companyId: joi.number().required(),
      creditInterestRate: joi.number().required(),
      debitInterestRate: joi.number().required(),
      cashPoolAccountId: joi.number(),
      balance: joi.number().required().allow(null),
      externalIds: joi.array().items(joi.string()),
      generateInterestStatementData: joi.boolean().required(),
    }),
  ),
  shouldCreateAuditTrail: joi.boolean().required(),
});
