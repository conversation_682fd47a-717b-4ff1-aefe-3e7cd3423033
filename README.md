# Nord Client

Requires Node version 14.17.6 to run.

## Setup

### Install Postgres

Easiest way to do that is to use [PostgresApp](https://postgresapp.com/downloads.html). After the install is done create a database to be used in the project. Ask one of the colleagues to provide you with a backup of the database. It's recommended to use [DBeaver](https://dbeaver.io/) for user friendly interaction with the database. Select the database you created, right click it followed by <PERSON>ls and Restore. Select the file you were provided and click Start.

### Install Redis

Easiest way to do that is to use `Homebrew`. If Homebrew is not installed, install it first using [Homebrew website](https://brew.sh/).
After Homebrew is installed use `brew install redis` to install Redis. To stop the Redis server use the command `brew services stop redis` and to start it up again use `brew services start redis`.

### Postman setup

Create a [Postman](https://www.postman.com/) account using your Ars Futura email. You will be invited to the Accurate workspace.

### Backend Setup

1. Install dependencies with `npm install`
2. Add the `.env` file to the root of the project
3. Copy secrets from `.env Development API` in 1Password to the `.env` file
4. Setup the database credentials in the `.env` according to the database you created
5. Same database credentials can be used for both `DEV_` and `TEST_` database secrets
6. Run `npm run dev`
7. Application should start on port 4000

## Cron Jobs

There are several cron jobs that are run on a daily basis and new ones will probably be added. The jobs are run only on the production server. The following line ensures the cron jobs are run only on the production server.

```javascript
if (process.env.name === 'production-api-main') {
  require('./cron');
}
```

Process name `production-api-main` is defined in the `pm2.prod.json` file which is used to run the backend on the production server.
There is also `pm2.stage.json` which is used on the staging server.
