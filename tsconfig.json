{"compilerOptions": {"module": "commonjs", "baseUrl": "./", "outDir": "./dist", "allowJs": true, "target": "ES2020", "esModuleInterop": true, "strict": true, "allowSyntheticDefaultImports": true, "incremental": true, "types": ["node", "jest"], "moduleResolution": "node", "noErrorTruncation": true, "skipLibCheck": true, "resolveJsonModule": true}, "exclude": ["node_modules", "dist"], "ts-node": {"files": true, "swc": true}}