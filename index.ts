/* eslint-disable no-process-exit */
/* eslint-disable no-console */

import chalk from 'chalk';
import * as dotenv from 'dotenv';
dotenv.config();

import application from './app';
import http from 'http';

if (typeof global.AbortController === 'undefined') {
  global.AbortController = require('abort-controller');
}

/** Get port from environment and store in Express. */
const port = normalizePort(process.env.PORT || '3001');
application.set('port', port);

/** Create HTTP server. */
const server = http.createServer(application);

/** Listen on provided port, on all network interfaces. */
server.listen(port);
server.on('error', onError);
server.on('listening', onListening);

/** Normalize a port into a number, string, or false. */
function normalizePort(val: string) {
  const port = parseInt(val, 10);

  if (isNaN(port)) return val;

  if (port >= 0) return port;

  return false;
}

/** Event listener for HTTP server "error" event. */
function onError(error: any) {
  if (error.syscall !== 'listen') {
    throw error;
  }

  const bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;

  switch (error.code) {
    case 'EACCES':
      console.error(bind + ' requires elevated privileges');
      process.exit(1);
    case 'EADDRINUSE':
      console.error(bind + ' is already in use');
      process.exit(1);
    default:
      throw error;
  }
}

/** Event listener for HTTP server "listening" event. */
function onListening() {
  const addr = server.address();
  const bind = typeof addr === 'string' ? `PIPE ${addr}` : `PORT ${addr?.port}`;
  console.log(chalk.green(`\n====LISTENING ON ${bind}====`));
}
