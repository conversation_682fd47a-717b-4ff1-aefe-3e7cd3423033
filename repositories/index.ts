import betaRepository from './betaRepository';
import b2bLoanRepository from './b2bLoanRepository';
import * as b2bLoanFileRepository from './b2bLoanFileRepository';
import * as b2bLoanLegRepository from './b2bLoanLegRepository';
import cashPoolBatchFileRepository from './cashPoolBatchFileRepository';
import cashPoolBatchPaymentsRepository from './cashPoolBatchPaymentsRepository';
import cashPoolBatchRepository from './cashPoolBatchRepository';
import cashPoolFileRepository from './cashPoolFileRepository';
import * as cashPoolLeaderBenefitRepository from './cashPoolLeaderBenefitRepository';
import cashPoolOperatingExpensesRepository from './cashPoolOperatingExpensesRepository';
import cashPoolParticipantAccountRepository from './cashPoolParticipantAccountRepository';
import cashPoolParticipantsRepository from './cashPoolParticipantsRepository';
import cashPoolParticipantTrailRepository from './cashPoolParticipantTrailRepository';
import cashPoolAuditTrailRepository from './cashPoolAuditTrailRepository';
import cashPoolStatementDataRepository from './cashPoolStatementDataRepository';
import cashPoolStatementDataFileRepository from './cashPoolStatementDataFileRepository';
import * as cashPoolParticipantAccountIdsRepository from './cashPoolParticipantAccountIdsRepository';
import cuftDataRepository from './cuftDataRepository';
import cuftDataFileRepository from './cuftDataFileRepository';
import countryRepository from './countryRepository';
import topCurrencyAccountAuditTrailRepository from './topCurrencyAccountAuditTrailRepository';
import cashPoolRepository from './cashPoolRepository';
import clientRepository from './clientRepository';
import companyAuditTrailRepository from './companyAuditTrailRepository';
import companyRepository from './companyRepository';
import creditRatingFileRepository from './creditRatingFileRepository';
import creditRatingRepository from './creditRatingRepository';
import creditRatingDraftRepository from './creditRatingDraftRepository';
import failedLoginAttemptRepository from './failedLoginAttemptRepository';
import featureRepository from './featureRepository';
import equityRiskPremiumRepository from './equityRiskPremiumRepository';
import regionalEquityRiskPremiumRepository from './regionalEquityRiskPremiumRepository';
import guaranteeFileRepository from './guaranteeFileRepository';
import guaranteeRepository from './guaranteeRepository';
import loanFileRepository from './loanFileRepository';
import loanRepository from './loanRepository';
import notificationRepository from './notificationRepository';
import participantAccountTrailsRepository from './participantAccountTrailsRepository';
import paymentRepository from './paymentRepository';
import providerDataRepository from './providerDataRepository';
import clientTemplateRepository from './clientTemplateRepository';
import tokenRepository from './tokenRepository';
import topCurrencyAccountRepository from './topCurrencyAccountRepository';
import userRepository from './userRepository';
import whtOriginRepository from './whtOriginRepository';
import whtRecipientRepository from './whtRecipientRepository';
import whtPaymentRepository from './whtPaymentRepository';
import tableColumnRepository from './tableColumnRepository';

export {
  betaRepository,
  b2bLoanRepository,
  b2bLoanFileRepository,
  b2bLoanLegRepository,
  cashPoolBatchFileRepository,
  cashPoolBatchPaymentsRepository,
  cashPoolBatchRepository,
  cashPoolFileRepository,
  cashPoolLeaderBenefitRepository,
  cashPoolOperatingExpensesRepository,
  cashPoolParticipantsRepository,
  cashPoolParticipantAccountRepository,
  cashPoolParticipantTrailRepository,
  cashPoolAuditTrailRepository,
  cashPoolStatementDataRepository,
  cashPoolStatementDataFileRepository,
  cashPoolParticipantAccountIdsRepository,
  cuftDataRepository,
  cuftDataFileRepository,
  countryRepository,
  topCurrencyAccountAuditTrailRepository,
  cashPoolRepository,
  clientRepository,
  companyAuditTrailRepository,
  companyRepository,
  creditRatingFileRepository,
  creditRatingRepository,
  creditRatingDraftRepository,
  featureRepository,
  failedLoginAttemptRepository,
  equityRiskPremiumRepository,
  regionalEquityRiskPremiumRepository,
  guaranteeFileRepository,
  guaranteeRepository,
  loanFileRepository,
  loanRepository,
  notificationRepository,
  participantAccountTrailsRepository,
  paymentRepository,
  providerDataRepository,
  clientTemplateRepository,
  tokenRepository,
  userRepository,
  topCurrencyAccountRepository,
  whtOriginRepository,
  whtRecipientRepository,
  whtPaymentRepository,
  tableColumnRepository,
};
