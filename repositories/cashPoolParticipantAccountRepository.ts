import models from '../models';
import {
  CashPoolParticipantAccountByClientIdType,
  GetCashPoolParticipantAccountsType,
  CashPoolParticipantAccountType,
} from '../types';
const {
  Company,
  CashPoolParticipantAccounts,
  Cash_Pool_Participants,
  ParticipantAccountTrails,
  CashPool,
  Client,
  TopCurrencyAccounts,
  ParticipantAccountIds,
} = models;

function createCashPoolParticipantAccount(participantAccount: any) {
  return CashPoolParticipantAccounts.create(participantAccount);
}

function createCashPoolParticipantAccounts(participantAccounts: any) {
  return CashPoolParticipantAccounts.bulkCreate(participantAccounts);
}

function getCashPoolParticipantAccounts({
  whereAccount,
  whereCashPool,
  whereParticipant,
  whereTopCurrencyAccount,
  paranoidAccount,
}: {
  whereAccount?: any;
  whereCashPool?: any;
  whereParticipant?: any;
  whereTopCurrencyAccount?: any;
  paranoidAccount?: boolean;
}): Promise<Array<GetCashPoolParticipantAccountsType>> {
  return CashPoolParticipantAccounts.findAll({
    where: whereAccount,
    paranoid: paranoidAccount,
    include: [
      {
        model: Cash_Pool_Participants,
        as: 'participant',
        where: whereParticipant,
        attributes: ['id'],
        required: true,
        include: [
          { model: Company, as: 'company', attributes: ['id', 'name'] },
          { model: CashPool, as: 'cashPool', where: whereCashPool, attributes: ['name'], required: true },
        ],
      },
      {
        model: TopCurrencyAccounts,
        as: 'topCurrencyAccount',
        where: whereTopCurrencyAccount,
        attributes: ['id', 'name', 'currency', 'creditInterestRate', 'debitInterestRate', 'interestType'],
        required: true,
      },
      {
        model: ParticipantAccountIds,
        as: 'externalIds',
        attributes: ['externalId'],
      },
    ],
  });
}

function getCashPoolParticipantAccount(where: any): Promise<CashPoolParticipantAccountType> {
  return CashPoolParticipantAccounts.findOne({ where });
}

function getCashPoolParticipantAccountsByTopCurrencyAccount({
  whereAccount,
  whereTopCurrencyAccount,
  whereParticipant,
  paranoidAccount,
}: any) {
  return CashPoolParticipantAccounts.findAll({
    where: whereAccount,
    paranoid: paranoidAccount,
    attributes: ['id', 'cashPoolParticipantId', 'topCurrencyAccountId'],
    include: [
      {
        model: TopCurrencyAccounts,
        as: 'topCurrencyAccount',
        where: whereTopCurrencyAccount,
        attributes: ['id'],
        required: true,
      },
      {
        model: Cash_Pool_Participants,
        where: whereParticipant,
        as: 'participant',
        attributes: ['id', 'companyId', 'cashPoolId'],
        include: {
          model: Company,
          as: 'company',
          attributes: ['id', 'name'],
        },
      },
    ],
  });
}

/**
 * Used for table/graph on the main cash pool screen,
 * and for structural positions card.
 */
function getCashPoolTrails({ cashPoolId, whereCompany, whereTrails, whereTopCurrencyAccount, trailAttributes }: any) {
  return CashPoolParticipantAccounts.findAll({
    attributes: ['id'],
    paranoid: false,
    include: [
      {
        model: ParticipantAccountTrails,
        as: 'accountTrails',
        attributes: trailAttributes,
        where: whereTrails,
        required: true,
      },
      {
        model: Cash_Pool_Participants,
        as: 'participant',
        attributes: ['companyId', 'isLeader'],
        where: { cashPoolId },
        required: true,
        include: {
          model: Company,
          as: 'company',
          where: whereCompany,
          attributes: ['id', 'name'],
        },
      },
      {
        model: TopCurrencyAccounts,
        as: 'topCurrencyAccount',
        attributes: ['name', 'interestType', 'currency'],
        where: whereTopCurrencyAccount,
        required: true,
      },
    ],
    order: [
      [{ model: ParticipantAccountTrails, as: 'accountTrails' }, 'date', 'ASC'],
      [{ model: Cash_Pool_Participants, as: 'participant' }, { model: Company, as: 'company' }, 'name', 'ASC'],
    ],
  });
}

function restoreCashPoolParticipantAccounts(where: any) {
  return CashPoolParticipantAccounts.restore({ where });
}

function updateCashPoolParticipantAccount({ where, attributesToUpdate, returning = true }: any) {
  return CashPoolParticipantAccounts.update(attributesToUpdate, {
    where,
    returning,
  });
}

function getCashPoolParticipantAccountsByClientId(
  clientId: number,
  cashPoolId: number,
): Promise<Array<CashPoolParticipantAccountByClientIdType>> {
  return CashPoolParticipantAccounts.findAll({
    attributes: ['id', 'balance'],
    include: [
      {
        model: ParticipantAccountIds,
        as: 'externalIds',
      },
      {
        model: Cash_Pool_Participants,
        as: 'participant',
        attributes: ['id'],
        where: { cashPoolId },
        required: true,
        include: {
          model: Company,
          as: 'company',
          attributes: ['id'],
          required: true,
          include: {
            model: Client,
            attributes: ['id'],
            where: { id: clientId },
            required: true,
          },
        },
      },
    ],
  });
}

export = {
  createCashPoolParticipantAccount,
  createCashPoolParticipantAccounts,
  getCashPoolParticipantAccount,
  getCashPoolParticipantAccounts,
  getCashPoolParticipantAccountsByTopCurrencyAccount,
  restoreCashPoolParticipantAccounts,
  updateCashPoolParticipantAccount,
  getCashPoolTrails,
  getCashPoolParticipantAccountsByClientId,
};
