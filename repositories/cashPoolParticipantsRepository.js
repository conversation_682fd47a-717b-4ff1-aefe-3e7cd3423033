const { Cash_Pool_Participants, CashPoolParticipantAccounts } = require('../models');

function getCashPoolParticipant({ cashPoolId, companyId }) {
  return Cash_Pool_Participants.findOne({ where: { cashPoolId, companyId } });
}

function getCashPoolParticipantsWithAccounts({ where }) {
  return Cash_Pool_Participants.findAll({
    where,
    include: { model: CashPoolParticipantAccounts, as: 'accounts', attributes: ['id'] },
  });
}

function deleteCashPoolParticipant(where) {
  return Cash_Pool_Participants.destroy({ where });
}

module.exports = { getCashPoolParticipant, getCashPoolParticipantsWithAccounts, deleteCashPoolParticipant };
