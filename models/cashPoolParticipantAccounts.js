'use strict';

module.exports = (sequelize, DataTypes) => {
  const CashPoolParticipantAccounts = sequelize.define(
    'CashPoolParticipantAccounts',
    {
      cashPoolParticipantId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      topCurrencyAccountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      balance: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      currency: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      creditInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      adjustedCreditInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      debitInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      adjustedDebitInterestRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      adjustedCreditInterestReceived: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      adjustedDebitInterestPaid: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      netInterestBenefit: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      cirWithOvernightRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      dirWithOvernightRate: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      generateInterestStatementData: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    { paranoid: true },
  );

  CashPoolParticipantAccounts.associate = function (models) {
    CashPoolParticipantAccounts.belongsTo(models.Cash_Pool_Participants, {
      foreignKey: 'cashPoolParticipantId',
      targetKey: 'id',
      as: 'participant',
    });
    CashPoolParticipantAccounts.hasMany(models.ParticipantAccountTrails, {
      foreignKey: 'participantAccountId',
      sourceKey: 'id',
      as: 'accountTrails',
    });
    CashPoolParticipantAccounts.belongsTo(models.TopCurrencyAccounts, {
      foreignKey: 'topCurrencyAccountId',
      targetKey: 'id',
      as: 'topCurrencyAccount',
    });
    CashPoolParticipantAccounts.hasMany(models.CashPoolBatch_ParticipantPayments, {
      foreignKey: 'cashPoolParticipantAccountId',
      sourceKey: 'id',
      as: 'payments',
    });
    CashPoolParticipantAccounts.hasMany(models.CashPoolStatementData, {
      foreignKey: 'cashPoolAccountId',
      sourceKey: 'id',
    });
    CashPoolParticipantAccounts.hasMany(models.ParticipantAccountIds, {
      foreignKey: 'cashPoolAccountId',
      sourceKey: 'id',
      as: 'externalIds',
    });
  };

  return CashPoolParticipantAccounts;
};
