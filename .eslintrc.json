{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "plugins": ["node", "prettier", "@typescript-eslint"], "extends": ["eslint:recommended", "plugin:node/recommended", "plugin:prettier/recommended", "plugin:@typescript-eslint/recommended", "prettier"], "env": {"node": true}, "rules": {"prettier/prettier": "error", "node/no-unpublished-import": "warn", "node/no-missing-import": "error", "node/no-unpublished-require": "warn", "node/no-unsupported-features/es-syntax": ["error", {"ignores": ["modules"]}], "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unused-vars": ["warn", {"varsIgnorePattern": "_"}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-non-null-asserted-optional-chain": "off", "@typescript-eslint/ban-types": "off", "@typescript-eslint/no-empty-function": "warn", "no-console": "warn", "no-useless-escape": "error", "no-dupe-keys": "warn", "no-sparse-arrays": "warn", "no-process-exit": "warn", "no-prototype-builtins": "warn", "no-useless-catch": "warn", "no-explicit-any": "off", "no-undef": "warn"}, "settings": {"node": {"tryExtensions": [".js", ".json", ".node", ".ts"]}}}