import express, { Request, Response } from 'express';
import hpp from 'hpp';

if (process.env.name === 'production-api-main') {
  require('./cron');
}
import { featureNames } from './enums';
import * as middlewares from './middlewares';
import * as routers from './routers';
import logger from './utils/logger';
import cookieParser from 'cookie-parser';

const app = express();

middlewares.sentry.sentryInit(app, { appName: 'nord-api' });

app.use(middlewares.helmet);
app.use(hpp());
app.use(middlewares.cors);
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());

app.use(middlewares.rateLimiter);

app.use(middlewares.xssMiddleware);

app.get('/', (_: Request, res: Response) => res.send('Hello Nord! 👋🤙'));
app.use('/api/oauth', routers.oauthRouter);
app.use('/api/user', routers.userRouter);
app.use('/api/contact', routers.contactRouter);
app.use('/api/setup-data', routers.setupDataRouter);

app.use(middlewares.authMethodMiddleware);
app.use(middlewares.sentry.sentrySetUser);

app.use('/api/cash-pool', middlewares.features(featureNames.CASH_POOL), routers.cashPoolRouter);
app.use('/api/cuft-data', middlewares.features(featureNames.CUFT_DATA), routers.cuftDataRouter);
app.use('/api/company', routers.companyRouter);
app.use('/api/feature', routers.featureRouter);
app.use('/api/loan', middlewares.features(featureNames.LOAN), routers.loanRouter);
app.use('/api/b2b-loan', middlewares.features(featureNames.BACK_TO_BACK_LOAN), routers.b2bLoanRouter);
app.use('/api/guarantee', middlewares.features(featureNames.GUARANTEE), routers.guaranteeRouter);
app.use('/api/credit-rating', middlewares.features(featureNames.CREDIT_RATING), routers.creditRatingRouter);
app.use('/api/notification', routers.notificationRouter);
app.use('/api/client', routers.clientRouter);
app.use('/api/client-template', routers.clientTemplateRouter);
app.use('/api/withholding-tax', routers.whtRouter);
app.use('/api/azure-storage', routers.azureStorageRouter);
app.use('/api/table-column', routers.tableColumnRouter);

app.use(middlewares.sentry.sentryErrorHandler());

app.use(middlewares.handleError);

process.on('unhandledRejection', (reason) => {
  throw reason;
});

process.on('uncaughtException', (error) => {
  logger.error({ message: error?.message, error });
  throw error;
});

export default app;
