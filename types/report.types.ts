import { seniorityEnum, industryAbbreviations } from '../utils/providerDataUtils';
import { reportEnums } from '../enums';
import type { CountryNameType } from './various.types';

export type LoanTypeType = 'Bullet' | 'Balloon';

export type PaymentFrequencyType = 'Monthly' | 'Quarterly' | 'Semi-annual' | 'Annual';

export type LoanPricingApproachType = 'implicit' | 'stand-alone' | 'implicit non-standard' | 'stand-alone non-standard';
export type GuaranteePricingApproachType = 'implicit' | 'stand-alone';

export type SeniorityType = typeof seniorityEnum[keyof typeof seniorityEnum];
export type IndustryType = keyof typeof industryAbbreviations;

export type ReportStatusType = 'Draft' | 'Final';

const { LOAN, B2B_LOAN, GUARANTEE, CREDIT_RATING, CASH_POOL } = reportEnums.REPORT_TYPES;
export type ReportValueType =
  | typeof LOAN
  | typeof GUARANTEE
  | typeof CREDIT_RATING
  | typeof CASH_POOL
  | typeof B2B_LOAN;

export type EmbeddedCompanyCreditRatingType = {
  rating: string;
  ratingAdj: string | null;
  probabilityOfDefault: number;
  probabilityOfDefaultAdj: number | null;
};

// columns `lender` and `borrower` in Loans and `guarantor` and `principal` in Guarantees
export type EmbeddedCompanyType = {
  id: number;
  parentCompanyId: number;
  name: string;
  industry: IndustryType;
  country: CountryNameType;
  creditRating: EmbeddedCompanyCreditRatingType;
};

export type PrincipalEmbeddedCompanyType = EmbeddedCompanyType & {
  creditRating: EmbeddedCompanyCreditRatingType &
    Partial<{
      cumulativeProbabilityOfDefault: number;
    }>;
};

export type LoanGuaranteeReportType = {
  finalInterestRate?: number;
  lowerBound?: number;
  midPoint?: number;
  upperBound?: number;
  isWhtEnabled?: boolean;
  whtInterestRate?: number;
  approach?: 'BORROWER_PAYS' | 'GROSS_UP';
};
