import { CreditRatingValueEnum } from '../enums/creditRating';
import { ReportStatusType, EmbeddedCompanyType } from './report.types';

export type CreditRatingValueType = keyof typeof CreditRatingValueEnum;

export type CreditRatingType = {
  id: number;
  clientId: number;
  createdAt: Date;
  updatedAt: Date;
  creditRating: { rating: CreditRatingValueType };
  isPortfolio: boolean;
  editable: boolean;
  company: EmbeddedCompanyType & { pseudonym?: string };
  status: ReportStatusType;
  note: string;
  closingDate: Date;
  createdBy: string;
  updatedBy: string;
  finalizedBy: string;
  probabilityOfDefault: number;
  deletedAt: Date;
};
