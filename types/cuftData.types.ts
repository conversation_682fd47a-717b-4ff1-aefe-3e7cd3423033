import { Op } from 'sequelize';

import {
  TrancheAssetClassEnum,
  TrancheTypeEnum,
  CountryToCuftCountryCodeEnum,
  CurrencyToCuftCurrencyEnum,
} from '../enums/cuftData';
import { OrderByType, DbAttributesType } from './various.types';
import { CreditRatingValueType } from './creditRating.types';
import { orderMapping } from '../utils/cuftDataUtils/constants';

export type TrancheAssetClassType = keyof typeof TrancheAssetClassEnum;
export type TrancheTypeType = keyof typeof TrancheTypeEnum;
export type FrontendCountryValueType = keyof typeof CountryToCuftCountryCodeEnum;
export type CuftDataCountryValueType = `${CountryToCuftCountryCodeEnum}`;
export type CurrencyToCuftCurrencyType = keyof typeof CurrencyToCuftCurrencyEnum;

export type CuftDataType = {
  cuftDataFileId: number;
  filingCompanyName: string;
  cuftBorrowerName: string;
  cuftBorrowerCountry: CuftDataCountryValueType;
  primarySic: number;
  allCurrencies: string;
  moodyPrincipalObligorCreditRating: string;
  spyPrincipalObligorCreditRating: string;
  cuftTrancheExecutionDate: Date;
  cuftTrancheMaturityDate: Date;
  cuftTrancheTenor: number;
  cuftTrancheAssetClass: TrancheAssetClassType;
  cuftTrancheType: TrancheTypeType;
  cuftTranchePrimaryReferenceRate: string;
  trancheOrderID: string;
  reviewCtrlrID: string;
  exhibitLink: string;
  deliveryDate: Date;
  createdAt: Date;
  updatedAt: Date;
};

export type DbCuftDataType = CuftDataType & { id: number };
export type DbCuftDataTypeWithCreditRatingsType = DbCuftDataType & {
  creditRatings: { id: number; creditRatingName: CreditRatingValueType; creditRatingValue: number };
};

export type DistinctBorrowerCountriesType = { distinctBorrowerCountries: CuftDataCountryValueType };
export type DistinctTrancheAssetClassType = { distinctTrancheAssetClasses: TrancheAssetClassType };
export type DistinctCurrenciesType = { currencies: { distinctCurrencies: CurrencyToCuftCurrencyType } };

export type CuftDataFilterBodyType = {
  limit: number;
  offset: number;
  issueDate: Date;
  dateMonthsBeforeIssueDate: Date;
  tenor: number | null;
  numberOfYearsBeforeAndAfterTenor: number;
  creditRatings: Array<CreditRatingValueType>;
  countries: Array<FrontendCountryValueType>;
  currencies: Array<CurrencyToCuftCurrencyType>;
  trancheAssetClasses: Array<TrancheAssetClassType>;
  excludedCreditRatingIds: Array<number>;
  sortBy: CuftOrderByBodyType;
};

export type CuftOrderMappingKeyType = keyof typeof orderMapping;
export type CuftOrderMappingValueType = typeof orderMapping[CuftOrderMappingKeyType];
export type CuftOrderByBodyType = CuftOrderMappingKeyType | `${CuftOrderMappingKeyType}+desc`;

export type CuftOrderByType =
  | [[CuftOrderMappingValueType, OrderByType]]
  | [[{ model: any; as: string }, string, OrderByType]];

export type CreateCuftDataFromCSVType = { cuftDataId: number; name: string };
export type DbCuftDataFromCSVType = CreateCuftDataFromCSVType & DbAttributesType;

export type CreateCuftDataCurrenciesType = { cuftDataId: number; currency: string };
export type DbCuftDataCurrenciesType = CreateCuftDataCurrenciesType & DbAttributesType;

export type CreateCuftDataCreditRatingsType = {
  cuftDataId: number;
  creditRatingName: CreditRatingValueType;
  creditRatingValue: number;
};
export type DbCuftDataCreditRatingsType = CreateCuftDataCreditRatingsType & DbAttributesType;

export type CuftDataSummaryStatisticsType = {
  maximum: number;
  upperQuartile: number;
  median: number;
  lowerQuartile: number;
  minimum: number;
  numberOfObservations: number;
};

export type CurrencyWhereType = { currency: { [Op.in]: CurrencyToCuftCurrencyType[] } } | {};

export type CuftDataSaveSearchBodyType = {
  issueDate: Date | null;
  numberOfMonthsBeforeIssueDate: number;
  tenor: number | null;
  numberOfYearsBeforeAndAfterTenor: number;
  creditRatings: Array<CreditRatingValueType>;
  currencies: Array<CurrencyToCuftCurrencyType>;
  countries: Array<string>;
  trancheAssetClasses: Array<TrancheAssetClassType>;
};

export type CuftDataSavedSearchType = {
  userId: number;
  name: string;
  search: CuftDataSaveSearchBodyType;
};

export type DbCuftDataSavedSearchType = CuftDataSavedSearchType & DbAttributesType;
