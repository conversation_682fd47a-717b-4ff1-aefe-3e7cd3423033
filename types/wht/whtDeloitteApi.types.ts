export type DeloitteApiResponseObject = {
  status: number;
  data: {
    id: number;
    longDesc: string;
    domesticRates: {
      withholdingTax: [DeloitteOriginType];
    };
    treatyRates: DeloitteRecipientType[] | [];
  };
};

export type DeloitteApiWHTOriginType = {
  date: Date;
  countryId: number;
  dividend: string | null;
  interest: string | null;
  royalty: string | null;
};

export type DeloitteApiWHTRecipientType = {
  originId: number;
  countryId: number | null;
  dividend: string | null;
  interest: string | null;
  royalty: string | null;
};

export type DeloitteOriginType = {
  jurisdictionName: string | null;
  dividends: string | null;
  interest: string | null;
  royalties: string | null;
  modifyDate: Date | null;
};

export type DeloitteRecipientType = {
  longDesc: string | null;
  dividends: string | null;
  interests: string | null;
  royalties: string | null;
  modifyDate: Date | null;
};

export type DeloitteWHTOriginType = {
  id: number;
  date: Date;
  countryId: number;
  dividend: string | null;
  interest: string | null;
  royalty: string | null;
  createdAt: Date;
  updatedAt: Date;
};

export type DeloitteOriginMailDataType = {
  country: string;
  previousInterestRate?: string;
  currentInterestRate: string | null;
};

export type DeloitteRecipientMailDataType = {
  originCountry: string;
  country: string;
  previousInterestRate?: string;
  currentInterestRate: string | null;
};
