import { WHTRecipientType, WHTRecipientWithCountry } from './whtRecipient.types';
import { WHTOriginType } from './whtOrigin.types';
import { whtEnums } from '../../enums';
import { LoanType } from '../loan.types';

export type WHTRecipientWithOriginType = WHTRecipientWithCountry & {
  origin?: WHTOriginType & { country: string };
};

export type WHTOriginWithRecipientType = WHTOriginType & {
  recipients: Array<WHTRecipientType> | [];
};

export type WHTApproachesType = keyof typeof whtEnums.WHT_APPROACHES;

export type WHTPayment = {
  id: number;
  loanId: number;
  paymentId: number;
  paymentNumber: number;
  isPaid: boolean;
  paymentAmount: number | null;
  loan?: LoanType;
  createdAt: Date;
  updatedAt: Date;
};
