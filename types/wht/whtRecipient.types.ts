export type WHTRecipientType = {
  id: number;
  originId: number;
  countryId: number;
  dividend?: string;
  interest?: string;
  royalty?: string;
  interestExplanation?: string;
  createdAt: Date;
  updatedAt: Date;
};

export type CreateWHTRecipientType = Omit<WHTRecipientType, 'id' | 'createdAt' | 'updatedAt'>;

export type ScrapedWHTRecipientType = Omit<CreateWHTRecipientType, 'originId'>;

export type WHTRecipientWithCountry = WHTRecipientType & { country: string };
