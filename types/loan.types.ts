import { dayCountEnums } from '../enums';

import {
  PaymentFrequencyType,
  ReportStatusType,
  LoanTypeType,
  SeniorityType,
  EmbeddedCompanyType,
  LoanGuaranteeReportType,
  LoanPricingApproachType,
} from './report.types';

type LoanRateType =
  | {
      type: 'fixed';
    }
  | {
      type: 'float';
      referenceRate: string;
      referenceRateMaturity: string;
    };

export type LoanType = {
  id: number;
  issueDate: Date;
  maturityDate: Date;
  currency: string;
  amount: number;
  paymentFrequency: PaymentFrequencyType;
  rateType: LoanRateType;
  seniority: SeniorityType;
  report: LoanGuaranteeReportType;
  createdAt: Date;
  updatedAt: Date;
  pricingApproach: LoanPricingApproachType;
  lender: EmbeddedCompanyType;
  borrower: EmbeddedCompanyType;
  clientId: number;
  editable: boolean;
  status: ReportStatusType;
  note: string;
  isPortfolio: boolean;
  isApproachCalculated: boolean;
  createdBy: string;
  updatedBy: string;
  finalizedBy: string | null;
  calculationLog: any;
  type: LoanTypeType;
  movedToAnalysesDate: Date;
  totalInterest: number;
  isThirdParty: boolean;
  dayCount: keyof typeof dayCountEnums.dayCountMapper;
  originalIssueDate: Date;
  deletedAt: Date;
  externalId?: string;
};

export type LoanFileMetadataType = {
  loanId: number;
  label: 'TP Report' | 'Agreement' | 'Credit Rating' | 'Other';
  name: string;
  extension: string;
  isGenerated: boolean;
  mimeType: string;
};

type B2BLoanCapmType = Partial<{
  requiredRateOfReturn: number;
  riskFreeRate: number;
  riskFreeRateSource: string;
  beta: number;
  betaSource: string;
  betaIndustrySector: string;
  betaRegion: string;
  betaType: 'Beta' | 'Unlevered Beta' | 'Unlevered Beta Corrected for Cash';
  equityRiskPremium: number;
  equityRiskPremiumSource: string;
  equityRiskPremiumCountry: string;
}>;

type B2BLoanCapmOverrideType = B2BLoanCapmType &
  Partial<{
    riskFreeRateIssueDate: Date;
    riskFreeRateTenor: number;
    riskFreeRateCurrency: string;
  }>;

type B2BLoanExpectedLossType = Partial<{
  expectedLoss: number;
  probabilityOfDefault: number;
  probabilityOfDefaultSource: string;
  probabilityOfDefaultType: 'Cumulative' | 'Annualized';
  lossGivenDefault: number;
  lossGivenDefaultSource: string;
}>;

type OverrideTogglesType = {
  riskFreeRate: boolean;
  beta: boolean;
  equityRiskPremium: boolean;
  probabilityOfDefault: boolean;
  lossGivenDefault: boolean;
};

export type StandardRemunerationOptionType =
  | {
      id: number;
      name: string;
      markup: number;
      operationalCost: number;
      type: 'Operating cost & Markup';
    }
  | { id: number; name: string; margin: number; type: '%' | 'basis points' };

type StandardRemunerationType = Record<string, StandardRemunerationOptionType>;

export type B2BLoanFileType = {
  id: number;
  loanId: number;
  label: 'TP Report' | 'Agreement' | 'Credit Rating' | 'Other';
  extension: string;
  status: 'Final' | 'Draft';
  mimeType: string;
  name: string;
  isGenerated: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type B2BLoanLegType = {
  id: number;
  loanId: number;
  lender: EmbeddedCompanyType;
  borrower: EmbeddedCompanyType;
  report: LoanGuaranteeReportType;
  calculationLog: any;
  ordinal: number;
};

export type B2BLoanType = {
  id: number;
  issueDate: Date;
  maturityDate: Date;
  currency: string;
  amount: number;
  paymentFrequency: PaymentFrequencyType;
  rateType: LoanRateType;
  seniority: SeniorityType;
  report: LoanGuaranteeReportType;
  createdAt: Date;
  updatedAt: Date;
  pricingApproach: LoanPricingApproachType;
  ultimateLender: EmbeddedCompanyType;
  ultimateBorrower: EmbeddedCompanyType;
  lenders: Array<EmbeddedCompanyType>;
  borrowers: Array<EmbeddedCompanyType>;
  riskTakerId: number;
  capm: B2BLoanCapmType;
  capmRecommendation: B2BLoanCapmType;
  capmOverride: B2BLoanCapmOverrideType;
  expectedLoss: B2BLoanExpectedLossType;
  expectedLossRecommendation: B2BLoanExpectedLossType;
  expectedLossOverride: B2BLoanExpectedLossType;
  overrideToggles: OverrideTogglesType;
  standardRemuneration: StandardRemunerationType;
  clientId: number;
  editable: boolean;
  status: ReportStatusType;
  note: string;
  files: Array<B2BLoanFileType>;
  legs: Array<B2BLoanLegType>;
  isPortfolio: boolean;
  createdBy: string;
  updatedBy: string | null;
  finalizedBy: string | null;
  calculationLog: any;
  type: LoanTypeType;
  movedToAnalysesDate: Date;
  totalInterest: number;
  isThirdParty: boolean;
  originalIssueDate: Date;
  deletedAt: Date;
  externalId?: string;
};

export type B2BLoanWithBorrowerAndLenderType = B2BLoanType & {
  borrower: EmbeddedCompanyType;
  lender: EmbeddedCompanyType;
};

export type LoanRateTypeType = 'fixed' | 'float';
