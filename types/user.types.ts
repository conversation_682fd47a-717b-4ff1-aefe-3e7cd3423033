import { DecimalPointType, DateFormatType } from './various.types';

export type RoleType = 'user' | 'admin' | 'superadmin';

export type UserDataAccessTokenType = {
  id: number;
  clientId: number;
  clientName: string;
  username: string;
  role: RoleType;
  exp?: number;
  iat?: number;
};

export type UserType = {
  id: number;
  clientId: number;
  username: string;
  password: string;
  email: string;
  fullName: string;
  role: RoleType;
  createdAt: Date;
  updatedAt: Date;
  areNotificationsMuted: boolean;
  dateFormat: DateFormatType;
  timezone: string;
  decimalPoint: DecimalPointType;
};

export type UserWithClientType = Omit<UserType, 'password'> & {
  client: { id: number; name: string };
};
