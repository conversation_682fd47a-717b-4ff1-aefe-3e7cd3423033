import { CompanyType } from './company.types';

export type ClientType = {
  id: number;
  name: string;
  emailDomains: Array<string>;
  industry: string;
  isLoanApproachCalculated: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type ClientWithNumberOfUsersType = ClientType & { numberOfUsers: number };

export type CreateClientType = Omit<ClientType, 'id' | 'isLoanApproachCalculated' | 'createdAt' | 'updatedAt'>;
export type CreateClientBodyType = CreateClientType & {
  companiesToCreate?: Array<CompanyType> | null;
  emailDomains: Array<string>;
};
