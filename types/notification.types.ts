import { NOTIFICATION_ACTIONS } from '../enums/notificationActions';
import { DbCreatedAttributes } from './various.types';

export type ReceivingUserNotificationType = {
  id: number;
  receivingUserId: number;
  notificationId: number;
  isHandled: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type NotificationType = {
  id: number;
  createdByUserId: number;
  url: string;
  action: keyof typeof NOTIFICATION_ACTIONS;
  note: string;
  createdAt: Date;
  updatedAt: Date;
};

export type CreateNotificationType = Omit<NotificationType, DbCreatedAttributes>;

export type CreateReceivingUserNotificationType = {
  receivingUserId: number;
  notificationId: number;
};

export type ReceivingUserNotificationWithNotificationType = {
  id: number;
  isHandled: boolean;
  createdAt: Date;
  updatedAt: Date;
  notification: NotificationType & {
    createdByUser: {
      id: number;
      email: string;
      fullName: string;
    };
  };
};
