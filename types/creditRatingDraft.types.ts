export type CreditRatingDraftType = {
  id: number;
  clientId: number;
  name: string;
  company: { id?: number; name?: string; country?: string };
  closingDate: Date | null;
  attributes: {
    naceSector: string | null;
    naceSectorGroup: string | null;
    option: 'Consolidated' | 'Unconsolidated';
    months: number | null;
    currency: string | null;
    exchangeRate: number | null;
    operatingRevenueTurnover: number | null;
    sales: number | null;
    costOfGoodSold: number | null;
    grossProfit: number | null;
    materialCosts: number | null;
    costOfEmployees: number | null;
    otherOperatingExpenses: number | null;
    depreciation: number | null;
    EBIT: number | null;
    EBITDA: number | null;
    financialPL: number | null;
    financialRevenue: number | null;
    financialExpenses: number | null;
    PLBeforeTax: number | null;
    taxation: number | null;
    PLAfterTax: number | null;
    extrAndOtherPL: number | null;
    extrAndOtherRevenue: number | null;
    extrAndOtherExpenses: number | null;
    PLForPeriod: number | null;
    fixedAssets: number | null;
    intangibleFixedAssets: number | null;
    tangibleFixedAssets: number | null;
    otherFixedAssets: number | null;
    currentAssets: number | null;
    stocks: number | null;
    debtors: number | null;
    otherCurrentAssets: number | null;
    cashAndCashEquivalent: number | null;
    totalAssets: number | null;
    shareholdersFunds: number | null;
    capital: number | null;
    otherShareholdersFunds: number | null;
    treasuryShares: number | null;
    nonCurrentLiabilities: number | null;
    longTermDebt: number | null;
    otherNonCurrentLiabilities: number | null;
    provisions: number | null;
    currentLiabilities: number | null;
    loans: number | null;
    creditors: number | null;
    otherCurrentLiabilities: number | null;
    totalShareFundsAndLiabilities: number | null;
    overriddenStatus: {
      fixedAssets: boolean;
      currentAssets: boolean;
      totalAssets: boolean;
      shareholdersFunds: boolean;
      nonCurrentLiabilities: boolean;
      currentLiabilities: boolean;
      totalShareFundsAndLiabilities: boolean;
      grossProfit: boolean;
      EBIT: boolean;
      EBITDA: boolean;
      financialPL: boolean;
      PLBeforeTax: boolean;
      PLAfterTax: boolean;
      extrAndOtherPL: boolean;
      PLForPeriod: boolean;
    };
  };
  createdAt: Date;
  updatedAt: Date;
};
