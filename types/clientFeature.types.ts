import { GeographyDataType } from './various.types';

export type ClientFeatureType = {
  id: number;
  clientId: number;
  featureId: number;
  isEnabled: boolean;
  values: any;
  createdAt: Date;
  updatedAt: Date;
};

export type ClientFeatureWithFeatureType = ClientFeatureType & { feature: FeatureType };

export type CreateClientFeatureType = Omit<ClientFeatureType, 'id'>;

export type ExactClientFeaturesType = {
  featureAvailability: {
    cashPool: boolean;
    cashPoolNumber: boolean;
    creditRating: boolean;
    creditRatingNumber: boolean;
    currency: boolean;
    financingAdvisory: boolean;
    geographyData: boolean;
    guarantee: boolean;
    guaranteeNumber: boolean;
    loan: boolean;
    loanNumber: boolean;
    backToBackLoan: boolean;
    loanGuaranteeNumber: boolean;
    payment: boolean;
    userNumber: boolean;
    cuftData: boolean;
    isTemplateCashPoolBatchUpload: boolean;
    physicalCashPool: boolean;
    notionalCashPool: boolean;
    nordicCashPool: boolean;
  };
  cashPoolNumber: number | null;
  creditRatingNumber: number | null;
  currency: {
    currencies: string[] | null;
  };
  geographyData: GeographyDataType;
  guaranteeNumber: number | null;
  loanNumber: number | null;
  loanGuaranteeNumber: number | null;
  userNumber: number | null;
};

export type FeatureNameType = keyof ExactClientFeaturesType['featureAvailability'];
export type FeatureNamesWithValues = keyof Omit<ExactClientFeaturesType, 'featureAvailability'>;

export type ClientFeatureByNameArgsType = { clientId: number; featureName: string };

export type FeatureType = {
  id: number;
  name: FeatureNameType;
  fullName: string;
  path?: string | null;
  isModule: boolean;
  note: string;
  createdAt: Date;
  updatedAt: Date;
};
