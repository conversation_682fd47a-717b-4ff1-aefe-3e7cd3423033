import { RegionsType } from './various.types';

export type EquityRiskPremiumsType = {
  countryId: number;
  moodyRating: string;
  ratingBasedDefaultSpread: number;
  totalEquityRiskPremium: number;
  countryRiskPremium: number;
  sovereignCds: number | null;
  totalEquityRiskPremium2: number | null;
  countryRiskPremium3: number | null;
  date: Date;
};

export type EquityRiskPremiumsRepoType = {
  id: number;
  countryId: number;
  date: Date;
  equityRiskPremium: number;
};

export type RegionalEquityRiskPremiumsType = {
  region: RegionsType;
  equityRiskPremium: number;
  date: Date;
};
