import { LoanRateTypeType } from './loan.types';
import { LoanTypeType } from './report.types';

export type PaymentType = {
  loanId: number | null;
  guaranteeId: number | null;
  interestRatePerInterestRepaymentFrequency: number;
  isPaid: boolean;
  paymentDueDate: Date;
  paymentNumber: number;
  paymentAmount: number;
  isPrincipalPayment: boolean;
  referenceRate: number | null;
  interestCalculationDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

export type BulletPaymentType = {
  paymentId: number;
  interestPayment: number | null;
  createdAt: Date;
  updatedAt: Date;
};

export type BalloonPaymentType = {
  paymentId: number;
  compoundedInterest: number | null;
  additionalInterest: number | null;
  compoundingPeriodEndDate: Date;
  createdAt: Date;
  updatedAt: Date;
};

export type LoanPaymentSheetData = {
  Lender: string;
  Borrower: string;
  'Loan type': LoanTypeType;
  'Rate type': LoanRateTypeType;
  Currency: string;
  'Interest amount': number | string;
  'Interest due date': Date;
  Status: 'Published' | 'Unpublished';
  'Interest number': string;
  'Interest frequency': string;
  'Issue date': Date;
  'Maturity date': Date;
  Rate: string;
  'Principal amount': number;
  Interest: number | string;
  'Reference rate': string;
};

export type GuaranteePaymentSheetData = {
  Guarantor: string;
  Principal: string;
  Currency: string;
  'Interest amount': number;
  'Interest due date': Date;
  Status: 'Published' | 'Unpublished';
  'Interest number': string;
  'Fee interest frequency': string;
  'Issue date': Date;
  'Termination date': Date;
  Rate: string;
  'Principal amount': number;
  'Guarantee fee': number;
};

export type CashPoolPaymentSheetData = {
  'Cash Pool': string;
  Creditor: string;
  Debtor: string;
  'Date Range': string;
  Currency: string;
  Interest: number;
  'Standalone Interest Rate': string;
  'Interest Due': Date;
  Status: 'Published' | 'Unpublished';
  'Rate Type': string;
  'Interest Added': 'Yes' | 'No';
  'External ID': string;
};
