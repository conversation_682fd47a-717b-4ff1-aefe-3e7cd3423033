import { industryAbbreviations, regionAbbreviations, seniorityAbbreviations } from '../utils/providerDataUtils';
import { countryToISOMapping } from '../utils/creditRatingUtils';

type regionAbbreviationsKeys = keyof typeof regionAbbreviations;
export type regionAbbreviationsValues = typeof regionAbbreviations[regionAbbreviationsKeys];

type industryAbbreviationsKeys = keyof typeof industryAbbreviations;
export type industryAbbreviationsValues = typeof industryAbbreviations[industryAbbreviationsKeys];

type seniorityAbbreviationsKeys = keyof typeof seniorityAbbreviations;
export type seniorityAbbreviationsValues = typeof seniorityAbbreviations[seniorityAbbreviationsKeys];

type countryToISOMappingKeys = keyof typeof countryToISOMapping;
export type countryToISOMappingValues = typeof countryToISOMapping[countryToISOMappingKeys];
