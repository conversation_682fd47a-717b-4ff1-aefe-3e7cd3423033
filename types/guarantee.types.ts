import {
  PaymentFrequencyType,
  ReportStatusType,
  SeniorityType,
  EmbeddedCompanyType,
  PrincipalEmbeddedCompanyType,
  LoanGuaranteeReportType,
  GuaranteePricingApproachType,
} from './report.types';

export type GuaranteeType = {
  id: number;
  issueDate: Date;
  terminationDate: Date;
  currency: string;
  amount: number;
  paymentFrequency: PaymentFrequencyType;
  seniority: SeniorityType;
  report: LoanGuaranteeReportType;
  createdAt: Date;
  updatedAt: Date;
  pricingApproach: GuaranteePricingApproachType;
  pricingMethodology: 'Yield - Expected Loss Approach' | 'Security Approach';
  guarantor: EmbeddedCompanyType;
  principal: PrincipalEmbeddedCompanyType;
  clientId: number;
  editable: boolean;
  status: ReportStatusType;
  note: string;
  isPortfolio: boolean;
  createdBy: string;
  updatedBy: string;
  finalizedBy: string;
  calculationLog: any;
  movedToAnalysesDate: Date;
  totalInterest: number;
  isThirdParty: boolean;
  originalIssueDate: Date;
  deletedAt: Date;
};

export type GuaranteeAlgorithmReturnType = {
  report: {
    lowerBound: number;
    midPoint: number;
    upperBound: number;
  };
  calculationLog: any;
  cumulativeProbabilityOfDefault: number;
};
