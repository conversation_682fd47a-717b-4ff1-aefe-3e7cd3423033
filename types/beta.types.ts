import { betaUtils } from '../utils/betaUtils';

export type BetaRegionType = keyof typeof betaUtils.regions;

export type BetaType = {
  industry: string;
  region: BetaRegionType;
  numberOfFirms: number | null;
  beta: number | null;
  deRatio: number | null;
  effectiveTaxRate: number | null;
  unleveredBeta: number | null;
  cashFirmValue: number | null;
  unleveredBetaCorrectedForCash: number | null;
  hiloRisk: number | null;
  standardDeviationOfEquity: number | null;
  standardDeviationInOperatingIncomeLast10Years: number | null;
  date: Date;
};
