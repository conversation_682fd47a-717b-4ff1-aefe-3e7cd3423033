import { regionAbbreviations } from '../utils/providerDataUtils/constants';
import { countryToISOMapping } from '../utils/creditRatingUtils';

export type DateFormatType = 'YYYY-MM-DD' | 'DD-MM-YYYY' | 'MM-DD-YYYY';

export type DecimalPointType = 'dot' | 'comma';

export type GeographyDataType = 'basic' | 'full';

export type FilterType = {
  [key: string]: any;
};

export type EnvironmentType = 'development' | 'production' | 'test';

export type OrderByType = 'asc' | 'desc';

export type RegionsType = keyof typeof regionAbbreviations;

export type DbAttributesType = {
  id: number;
  createdAt: Date;
  updatedAt: Date;
};
export type DbCreatedAttributes = 'id' | 'createdAt' | 'updatedAt';

export type CountryNameType = keyof typeof countryToISOMapping;
