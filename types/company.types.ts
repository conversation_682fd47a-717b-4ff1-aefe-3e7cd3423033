import { CreditRatingValueType } from './creditRating.types';
import { assessmentConstants } from '../utils/companyUtils/constants';

type CompanyAssessmentConstantsType = typeof assessmentConstants[keyof typeof assessmentConstants];

export type CompanyAssessmentType = {
  answers: {
    ringFencing: boolean;
    question1: boolean;
    question2: boolean;
    question3: boolean;
    question4: boolean;
    question5: boolean;
    question6: boolean;
    question7: boolean;
    question8: boolean;
    question9: boolean;
    question10: boolean;
  };
  name: CompanyAssessmentConstantsType;
};

export type CompanyType = {
  id: number;
  parentCompanyId: number;
  name: string;
  industry: string;
  country: string;
  note: string;
  creditRating: {
    rating: CreditRatingValueType;
    ratingAdj: CreditRatingValueType;
    probabilityOfDefault: number;
    probabilityOfDefaultAdj: number;
  };
  assessment: CompanyAssessmentType | null;
  createdBy: string;
  updatedBy: string;
  createdAt: Date;
  updatedAt: Date;
};
