import { FileContentTypeEnum } from '../enums/files';
import { TemplateFileLabelsEnum } from '../enums/templateFiles';

export type TemplateFileTypeType = 'loan' | 'b2bLoan' | 'guarantee';
export type TemplateFileTypeValuesType = Array<TemplateFileTypeType>;

export type TemplateFileType = {
  id: number;
  clientId: number;
  name: string;
  label: TemplateFileLabelsEnum;
  type: TemplateFileTypeType;
  extension: string;
  mimeType: FileContentTypeEnum;
  country: string;
  companyId: number;
  company?: any;
  createdAt: Date;
  updatedAt: Date;
};
