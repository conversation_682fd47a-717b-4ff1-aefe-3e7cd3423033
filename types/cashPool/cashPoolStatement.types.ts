import { CashPoolInterestTypeType } from './cashPool.types';
import { DbAttributesType } from '../various.types';

export type CashPoolStatementPositionType = 'C' | 'D';

export type CashPoolStatementBlockType = {
  cashPoolAccountId: number;
  transactionReferenceNumber: string;
  statementNumber: string;
  balanceChange: number;
  date: Date;
  statementDate: Date | null;
  comment?: string | null;
  cashPoolPaymentId?: number | null;
};

export type DbCashPoolStatementBlockType = CashPoolStatementBlockType & DbAttributesType;

export type StatementDataForTemplateFileType = {
  date: Date;
  statementDate: Date;
  balanceChange: number;
  statementNumber: string;
  comment: string | null;
  cashPoolBatchId: number | null;
  account: {
    id: number;
    topCurrencyAccount: { id: number; interestType: CashPoolInterestTypeType };
    participant: {
      id: number;
      cashPool: {
        id: number;
      };
      company: {
        id: number;
        name: string;
      };
    };
  };
};

export type StatementDataForTemplateFileType2 = {
  date: Date;
  cashPoolAccountId: number;
  totalBalanceChange: number;
  companyName: string;
};
