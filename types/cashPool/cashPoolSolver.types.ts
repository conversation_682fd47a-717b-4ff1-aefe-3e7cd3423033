export type CashPoolDataForSpecificDate = {
  cashPoolGrossBenefit: number;
  companies: Array<{
    accountId: number;
    adjCir: number;
    adjDir: number;
    adjustedCreditInterestReceived: number;
    adjustedDebitInterestPaid: number;
    adjustedInterestPaid: number;
    adjustedInterestReceived: number;
    balance: number;
    cir: number;
    companyId: number;
    creditBalance: number;
    debitBalance: number;
    dir: number;
    netInterestBenefit: number;
  }>;
  date: Date;
  totalOperatingCost: number;
};

export type LeaderPayableReceivableType = { payableToLeader: number; receivableFromLeader: number };

// string is the id of the company
export type ParticipantsPayableOrReceivableType = Record<string, number>;

export type CalculateCashPoolBatchReturnType = {
  batchGrossBenefit: number;
  companiesDataOverTime: Array<CashPoolDataForSpecificDate>;
  leaderPayableReceivable: LeaderPayableReceivableType;
  participantsPayableOrReceivable: ParticipantsPayableOrReceivableType;
};
