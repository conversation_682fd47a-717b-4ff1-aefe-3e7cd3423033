import { CashPoolParticipantType } from './cashPoolParticipant.types';
import { TopCurrencyAccountType } from './topCurrencyAccount.types';
import { CashPoolType } from './cashPool.types';
import { ParticipantAccountIdType } from './cashPoolParticipantAccountId.types';
import { CompanyType } from '../company.types';

export type CashPoolParticipantAccountType = {
  id: number;
  cashPoolParticipantId: number;
  balance: number | null;
  currency: string | null;
  creditInterestRate: number | null;
  debitInterestRate: number | null;
  adjustedCreditInterestRate: number | null;
  adjustedDebitInterestRate: number | null;
  adjustedCreditInterestReceived: number | null;
  adjustedDebitInterestPaid: number | null;
  netInterestBenefit: number | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  topCurrencyAccountId: number;
  cirWithOvernightRate: number | null;
  dirWithOvernightRate: number | null;
};

export type CashPoolParticipantAccountByClientIdType = {
  id: number;
  balance: number | null;
  externalIds: Array<ParticipantAccountIdType>;
  participant: {
    id: number;
    company: {
      id: number;
      Client: {
        id: number;
      };
    };
  };
};

export type GetCashPoolParticipantAccountsType = CashPoolParticipantAccountType & {
  participant: CashPoolParticipantType & { company: CompanyType; cashPool: CashPoolType };
  topCurrencyAccount: TopCurrencyAccountType;
  externalIds: ParticipantAccountIdType[];
};
