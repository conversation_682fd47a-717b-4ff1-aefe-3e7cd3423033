import { cashPoolEnums } from '../../enums';

import { CompanyType } from '../company.types';
import { TopCurrencyAccountType } from './topCurrencyAccount.types';
import { CashPoolParticipantAccountType } from './cashPoolParticipantAccount.types';
import { CashPoolParticipantType } from './cashPoolParticipant.types';
import { CashPoolFileType } from './cashPoolFile.types';
import models from '../../models';

export type CashPoolInterestTypeType = 'fixed' | 'float';

export type CashPoolType = {
  id: number;
  leaderId: number;
  clientId: number;
  name: string;
  country: string;
  currencies: string;
  riskAnalysisAnswers: {
    guarantee: boolean;
    liquidityRisk1: boolean;
    liquidityRisk2: boolean;
    creditRisk1: boolean;
    creditRisk2: boolean;
    functions1: boolean;
    functions2: boolean;
    functions3: boolean;
    functions4: boolean;
    functions5: boolean;
    functions6: boolean;
  };
  assessment: 'Low' | 'Medium' | 'High';
  creditInterestRate: number | null;
  debitInterestRate: number | null;
  grossBenefit: number | null;
  operatingCost: number | null;
  operatingCostMarkup: number | null;
  note: string | null;
  createdAt: Date;
  updatedAt: Date;
  type: 'Physical' | 'Notional' | 'Nordic';
  overnightRate: string | null;
  totalRisk: number;
  interestType: CashPoolInterestTypeType | null;
  externalId?: string;
};

export type GetCashPoolReturnType = CashPoolType & { leader: CompanyType } & {
  topCurrencyAccounts: Array<
    TopCurrencyAccountType & {
      accounts: Array<CashPoolParticipantAccountType> & {
        participant: CashPoolParticipantType & { company: CompanyType };
      };
    }
  > & {};
  participants: Array<
    CashPoolParticipantType & { company: CompanyType; accounts: Array<CashPoolParticipantAccountType> }
  >;
  files: Array<CashPoolFileType>;
  batches: Array<typeof models.CashPoolBatch>;
};

export type UploadBatchFileType = {
  id: number;
  cashPoolBatchId: number;
  name: string;
  extension: string;
  mimeType: string;
  updatedAt: Date;
  createdAt: Date;
  cashPoolId: number;
};

export type CashPoolBatchStatusType = typeof cashPoolEnums.batchStatus[keyof typeof cashPoolEnums.batchStatus];
