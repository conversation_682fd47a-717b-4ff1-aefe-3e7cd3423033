import {
  regionAbbreviationsValues,
  seniorityAbbreviationsValues,
  industryAbbreviationsValues,
  countryToISOMappingValues,
} from './constantsKeysValues.types';

export type ProviderSearchDataType = {
  issueDate: Date;
  region: regionAbbreviationsValues;
  country: false | countryToISOMappingValues | 'ALL';
  industryGroup: industryAbbreviationsValues;
  currency: string;
  seniority: seniorityAbbreviationsValues;
};
