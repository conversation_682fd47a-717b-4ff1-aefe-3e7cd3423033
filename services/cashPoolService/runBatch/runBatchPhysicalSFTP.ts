import _ from 'lodash';

import {
  cashPoolRepository,
  cashPoolParticipantAccountRepository,
  cashPoolStatementDataRepository,
} from '../../../repositories';
import * as azureService from '../../../services/azureService';
import * as solverService from '../../../services/solverService';
import cashPoolBatchUtils from '../../../utils/cashPoolBatch';
import { sheetToJson, jsonToSheet } from '../../../utils/documents';
import { BadRequestError, NotFoundError } from '../../../utils/ErrorHandler';

import * as batchService from './batchService';
import * as sftpBatchService from './sftpBatchService';

const runBatchPhysicalSFTP = async ({
  clientId,
  cashPoolId,
  userId,
  startDate,
  endDate,
}: {
  clientId: number;
  cashPoolId: number;
  userId: number;
  startDate: Date;
  endDate: Date;
}) => {
  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const cashPoolLeader = cashPool.participants.find(({ isLeader }: { isLeader: boolean }) => isLeader);
  if (!cashPoolLeader) throw new BadRequestError('Leader not defined.');

  const accounts = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
    whereCashPool: { id: cashPoolId, clientId },
  });
  const accountsByIdBalanceAccumulator = _.keyBy(accounts, 'id');

  const { cashPoolBatch, cashPoolBatchFile } = await sftpBatchService.createBatch(
    userId,
    cashPool,
    startDate,
    endDate,
    accounts,
  );
  const batchId = cashPoolBatch.id;
  const cashPoolBatchFileId = cashPoolBatchFile.id;

  const { sheetData, statementDataIds } = await sftpBatchService.getStatementDataForSheet(
    accounts,
    accountsByIdBalanceAccumulator,
    cashPool.id,
    new Date(startDate),
    new Date(endDate),
  );
  if (statementDataIds.length === 0) throw new BadRequestError('No statement data found for the given date range');

  const batchFile = jsonToSheet([{ sheetData, sheetName: 'Data' }]);

  const batchFileInJson = sheetToJson(batchFile);
  batchFileInJson.shift(); // remove header row

  const solverInput = cashPoolBatchUtils.getBatchSolverInput({
    cashPool,
    topCurrencyAccount: cashPool.topCurrencyAccounts[0],
    accounts,
    batchFileInJson,
    cashPoolLeader,
  });

  const solverInputSanitized = solverInput?.map((i) => {
    return {
      ...i,
      CPP: i.CPP.map((j) => {
        if (j.balance === 0) j.balance = 0.********;
        return j;
      }),
    };
  });

  const { data: solverData } = await solverService.calculateCashPoolBatch(solverInputSanitized);
  const { companiesDataOverTime, leaderPayableReceivable, participantsPayableOrReceivable, batchGrossBenefit } =
    solverData;

  for (const cashPoolDate of companiesDataOverTime) {
    await batchService.updateParticipantAccountsWithSolverData(cashPoolDate, cashPoolId, batchId);

    await batchService.createParticipantAccountTrails({
      whereCashPool: { id: cashPoolId },
      batchId,
      date: cashPoolDate.date,
    });
  }

  const statementDataBatchIdUpdatePromise = cashPoolStatementDataRepository.updateCashPoolBatchIdOfStatementData(
    batchId,
    statementDataIds.map(({ id }) => id),
  );
  const batchPromise = batchService.updateBatch({ batchId, leaderPayableReceivable, batchGrossBenefit });
  const batchParticipantPaymentsPromise = batchService.createBatchParticipantPayments({
    batchId,
    participantsPayableOrReceivable,
    cashPoolId,
    currency: cashPool.currencies,
    cashPoolLeaderId: cashPoolLeader.id,
  });
  const cashPoolPromise = cashPoolRepository.updateCashPool({
    where: { id: cashPoolId, clientId },
    attributesToUpdate: { grossBenefit: Number(cashPool.grossBenefit) + batchGrossBenefit },
  });
  await Promise.all([
    batchPromise,
    batchParticipantPaymentsPromise,
    cashPoolPromise,
    statementDataBatchIdUpdatePromise,
  ]);

  await azureService.uploadFile(`cashPoolBatch/${cashPoolBatchFileId}`, batchFile, Buffer.byteLength(batchFile));
};

export default runBatchPhysicalSFTP;
