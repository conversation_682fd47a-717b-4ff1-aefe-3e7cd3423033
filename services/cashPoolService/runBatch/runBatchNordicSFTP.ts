import { cashPoolRepository /* , cashPoolParticipantAccountRepository */ } from '../../../repositories';
// import * as azureService from '../../../services/azureService';
// import * as solverService from '../../../services/solverService';
// import cashPoolBatchUtils from '../../../utils/cashPoolBatch';
// import { sheetToJson } from '../../../utils/documents';
import { BadRequestError, NotFoundError } from '../../../utils/ErrorHandler';

// import * as batchService from './batchService';
// import * as sftpBatchService from './sftpBatchService';

const runBatchNordicSFTP = async ({
  clientId,
  cashPoolId,
}: // userId,
// startDate,
// endDate,
{
  clientId: number;
  cashPoolId: number;
  userId: number;
  startDate: Date;
  endDate: Date;
}) => {
  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const cashPoolLeader = cashPool.participants.find(({ isLeader }: { isLeader: boolean }) => isLeader);
  if (!cashPoolLeader) throw new BadRequestError('Leader not defined.');

  // TODO
  // const { batchId, cashPoolBatchFileId, batchFile } = await sftpBatchService.createCashPoolBatchAndFileFromSftpData(
  //   userId,
  //   cashPool,
  //   startDate,
  //   endDate,
  //   [],
  // );
  // const batchFileInJson = sheetToJson(batchFile);
  // batchFileInJson.shift(); // remove header row

  // let batchGrossBenefitSum = 0;
  // const leaderPayableReceivableSum = { payableToLeader: 0, receivableFromLeader: 0 };

  // const accountsByTopCurrencyAccount = cashPoolBatchUtils.groupAccountsByTopCurrencyAccountId(batchFileInJson);

  // for (const topCurrencyAccountId of Object.keys(accountsByTopCurrencyAccount)) {
  //   /** differs from the physical pool by passing currency to get only accounts with the given currency */
  //   const accounts = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
  //     whereTopCurrencyAccount: { id: topCurrencyAccountId },
  //     whereCashPool: { id: cashPoolId, clientId },
  //   });
  //   /** batchFileInJson contains all the rows with one top currency account, next iteration contains all rows with some other top currency account */
  //   const currentTopCurrencyAccount = accounts[0].topCurrencyAccount;

  //   const solverInput = cashPoolBatchUtils.getBatchSolverInput({
  //     cashPool,
  //     topCurrencyAccount: currentTopCurrencyAccount,
  //     accounts,
  //     batchFileInJson: accountsByTopCurrencyAccount[topCurrencyAccountId],
  //     cashPoolLeader,
  //   });

  //   const { data: solverData } = await solverService.calculateCashPoolBatch(solverInput);
  //   const { leaderPayableReceivable, participantsPayableOrReceivable, batchGrossBenefit } = solverData;

  //   batchGrossBenefitSum += batchGrossBenefit;
  //   leaderPayableReceivableSum.payableToLeader += leaderPayableReceivable.payableToLeader;
  //   leaderPayableReceivableSum.receivableFromLeader += leaderPayableReceivable.receivableFromLeader;

  //   await batchService.createBatchParticipantPayments({
  //     batchId,
  //     participantsPayableOrReceivable,
  //     cashPoolId,
  //     currency: currentTopCurrencyAccount.currency,
  //     cashPoolLeaderId: cashPoolLeader.id,
  //   });
  // }

  // await batchService.updateBatch({
  //   batchId,
  //   leaderPayableReceivable: leaderPayableReceivableSum,
  //   batchGrossBenefit: batchGrossBenefitSum,
  // });

  // await azureService.uploadFile(`cashPoolBatch/${cashPoolBatchFileId}`, batchFile, Buffer.byteLength(batchFile));
};

export default runBatchNordicSFTP;
