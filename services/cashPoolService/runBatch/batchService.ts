import _ from 'lodash';

import {
  cashPoolParticipantAccountRepository,
  cashPoolParticipantTrailRepository,
  cashPoolBatchPaymentsRepository,
  cashPoolBatchRepository,
  cashPoolLeaderBenefitRepository,
} from '../../../repositories';
import { transformSolverDataForDb } from '../../../utils/cashPool';
import { cashPoolEnums } from '../../../enums';
import {
  LeaderPayableReceivableType,
  ParticipantsPayableOrReceivableType,
  CashPoolDataForSpecificDate,
  CashPoolBatchStatusType,
} from '../../../types';

export const createBatchParticipantPayments = async ({
  participantsPayableOrReceivable,
  cashPoolId,
  batchId,
  currency,
  cashPoolLeaderId,
}: {
  participantsPayableOrReceivable: ParticipantsPayableOrReceivableType;
  cashPoolId: number;
  batchId: number;
  currency: string;
  cashPoolLeaderId: number;
}) => {
  const companyIds = Object.keys(participantsPayableOrReceivable);
  const accounts = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
    whereParticipant: { companyId: companyIds, cashPoolId },
  });

  const paymentPromises = [];
  for (const account of accounts) {
    let interest = participantsPayableOrReceivable[account.participant.company.id];

    // For companies with overridden negative debit rates, interest should be zero
    // This happens when adjustedDebitInterestRate is 0 and the original solver interest was negative
    if (account.adjustedDebitInterestRate === 0 && interest < 0) {
      interest = 0;
    }

    const data = { cashPoolBatchId: batchId, cashPoolParticipantAccountId: account.id };
    const creditorDebtorInterestData =
      interest < 0
        ? {
            interestPayable: Math.abs(interest),
            interestReceivable: null,
            creditorId: cashPoolLeaderId,
            debtorId: account.participant.id,
          }
        : interest > 0
        ? {
            interestPayable: null,
            interestReceivable: interest,
            creditorId: account.participant.id,
            debtorId: cashPoolLeaderId,
          }
        : {
            interestPayable: 0,
            interestReceivable: null,
            creditorId: cashPoolLeaderId,
            debtorId: account.participant.id,
          };

    paymentPromises.push(
      cashPoolBatchPaymentsRepository.createCashPoolPayment({ currency, ...data, ...creditorDebtorInterestData }),
    );
  }

  return Promise.all(paymentPromises);
};

export const updateParticipantAccountsWithSolverData = (
  cashPoolDate: CashPoolDataForSpecificDate,
  cashPoolId: number,
  cashPoolBatchId: number,
) => {
  const participantBenefit = cashPoolDate.companies.reduce((acc, curr) => acc + curr.netInterestBenefit, 0);
  const totalBenefit = cashPoolDate.cashPoolGrossBenefit;
  const leaderBenefit = totalBenefit - participantBenefit;

  return Promise.all([
    ...cashPoolDate.companies.map((account: any) =>
      cashPoolParticipantAccountRepository.updateCashPoolParticipantAccount({
        where: { id: account.accountId },
        attributesToUpdate: transformSolverDataForDb(account),
      }),
    ),
    cashPoolLeaderBenefitRepository.createLeaderBenefit({
      cashPoolId,
      cashPoolBatchId,
      leaderBenefit,
      date: cashPoolDate.date,
    }),
  ]);
};

export const createParticipantAccountTrails = async ({
  whereCashPool,
  whereParticipant,
  whereTopCurrencyAccount,
  batchId,
  date,
}: {
  batchId: number;
  date: Date;
  whereCashPool: any;
  whereTopCurrencyAccount?: any;
  whereParticipant?: any;
  paranoidAccount?: boolean;
}) => {
  const updatedParticipants = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
    whereTopCurrencyAccount,
    whereCashPool,
    whereParticipant,
    paranoidAccount: false,
  });

  return cashPoolParticipantTrailRepository.createCashPoolParticipantTrails(
    updatedParticipants.map((p: any) => ({
      ..._.omit(p.dataValues, 'id'), // needed so the id gets autogenerated
      creditInterestRate: p.cirWithOvernightRate,
      debitInterestRate: p.dirWithOvernightRate,
      participantAccountId: p.id,
      cashPoolBatchId: batchId,
      date,
    })),
  );
};

export const updateBatch = ({
  batchStatus = cashPoolEnums.batchStatus.UNPAID,
  batchId,
  leaderPayableReceivable,
  batchGrossBenefit,
}: {
  batchStatus?: CashPoolBatchStatusType;
  batchId: number;
  leaderPayableReceivable: LeaderPayableReceivableType;
  batchGrossBenefit: number;
}): Promise<any> => {
  return cashPoolBatchRepository.updateCashPoolBatch(batchId, {
    status: batchStatus,
    totalInterestPayableToLeader: leaderPayableReceivable.payableToLeader,
    totalInterestReceivableFromLeader: leaderPayableReceivable.receivableFromLeader,
    grossBenefit: batchGrossBenefit,
  });
};
