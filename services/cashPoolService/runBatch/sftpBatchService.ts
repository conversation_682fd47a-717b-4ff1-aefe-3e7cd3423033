import _ from 'lodash';
import { format } from 'date-fns';
import { Op } from 'sequelize';

import {
  cashPoolBatchRepository,
  cashPoolBatchFileRepository,
  participantAccountTrailsRepository,
  cashPoolStatementDataRepository,
} from '../../../repositories';
import cashPoolBatchUtils from '../../../utils/cashPoolBatch';
import dateUtils from '../../../utils/dates';
import { BadRequestError } from '../../../utils/ErrorHandler';
import { jsonToSheet } from '../../../utils/documents';
import { files as fileEnums, cashPoolEnums } from '../../../enums';
import { GetCashPoolReturnType, GetCashPoolParticipantAccountsType } from '../../../types';

type SheetRowType = {
  Date: Date;
  Participant: string;
  Balance: number;
  'Overnight Rate': 'n/a' | number;
  'Top Currency Account Id'?: number;
};

export const getStatementDataForSheet = async (
  accounts: Array<{ id: number; participant: { company: { name: string } } }>,
  accountsByIdBalanceAccumulator: any,
  cashPoolId: number,
  startDate: Date,
  endDate: Date,
) => {
  const statementQueryData = { startDate, endDate, cashPoolId };
  const [statementDataDb, statementDataIds] = await Promise.all([
    cashPoolStatementDataRepository.getStatementDataForTemplateFile2(statementQueryData),
    cashPoolStatementDataRepository.getStatementDataForTemplateIds(statementQueryData),
  ]);
  const statementDataGroupedByDate = _.groupBy(statementDataDb, 'date');

  const data: Array<{
    Date: Date;
    Participant: string;
    Balance: number;
    'Balance Change': number;
    'Overnight Rate': 'n/a';
  }> = [];

  for (const date of dateUtils.getDatesInRange(startDate, endDate)) {
    const statementDataForDate = statementDataGroupedByDate[date];
    const statementDataByAccountId = _.keyBy(statementDataForDate, 'cashPoolAccountId');

    for (const { id, participant } of accounts) {
      const statementData = statementDataByAccountId[id];
      const previousBalance = accountsByIdBalanceAccumulator[id].balance;

      if (!statementData) {
        data.push({
          Date: new Date(date),
          Participant: participant.company.name,
          //! Previous balance is just copied for this date, because there is no statement data.
          Balance: Number(previousBalance),
          'Balance Change': 0,
          'Overnight Rate': 'n/a',
        });
        continue;
      }

      accountsByIdBalanceAccumulator[id].balance += statementData?.totalBalanceChange;
      data.push({
        Date: new Date(date),
        Participant: participant.company.name,
        Balance: accountsByIdBalanceAccumulator[id].balance,
        'Balance Change': statementData?.totalBalanceChange,
        'Overnight Rate': 'n/a',
      });
    }
  }
  return { sheetData: data, statementDataIds };
};

export const createBatch = async (
  userId: number,
  cashPool: GetCashPoolReturnType,
  startDate: Date,
  endDate: Date,
  accounts: Array<GetCashPoolParticipantAccountsType>,
) => {
  const accountsSnapshot = JSON.parse(JSON.stringify(accounts)).map((account: GetCashPoolParticipantAccountsType) =>
    _.omit(account, ['participant', 'topCurrencyAccount', 'createdAt', 'updatedAt']),
  );
  const cashPoolBatch = await cashPoolBatchRepository.createCashPoolBatch({
    cashPoolId: cashPool.id,
    createdByUserId: userId,
    startDate,
    endDate,
    status: cashPoolEnums.batchStatus.UNPAID,
    accountsSnapshot,
  });

  const dateFnsFormat = 'yyyy-MM-dd';
  const batchFileData = cashPoolBatchUtils.getCashPoolBatchFileData({
    cashPoolBatchId: cashPoolBatch.id,
    originalname: `${cashPool.name}_${format(startDate, dateFnsFormat)}_${format(endDate, dateFnsFormat)}_batch.xlsx`,
    mimetype: fileEnums.FileContentTypeEnum.XLSX,
  });
  const cashPoolBatchFile = await cashPoolBatchFileRepository.createCashPoolBatchFile(batchFileData);

  return { cashPoolBatch, cashPoolBatchFile };
};

export const createCashPoolBatchAndFileFromSftpData = async (
  userId: number,
  cashPool: GetCashPoolReturnType,
  startDate: Date,
  endDate: Date,
  accounts: Array<GetCashPoolParticipantAccountsType>,
): Promise<{ batchId: number; cashPoolBatchFileId: number; batchFile: Buffer }> => {
  const { cashPoolBatch, cashPoolBatchFile } = await createBatch(userId, cashPool, startDate, endDate, accounts);

  const trails = await participantAccountTrailsRepository.getCashPoolTrails({
    cashPoolId: cashPool.id,
    whereTrails: { date: { [Op.between]: [startDate, endDate] } },
    whereCompany: {},
    whereTopCurrencyAccount: {},
  });

  if (trails.length === 0) throw new BadRequestError('No data for this date range.');

  const sheetData = trails.map((trail: any) => {
    const row: SheetRowType = {
      Date: trail.date,
      Participant: trail.account.participant.company.name,
      Balance: trail.balance,
      'Overnight Rate': 'n/a',
    };

    if (trail.account.topCurrencyAccount.interestType === 'float') {
      row['Overnight Rate'] = 3; // TODO Matej this needs to be handled
    }

    if (cashPool.type === cashPoolEnums.cashPoolTypes.NORDIC) {
      row['Top Currency Account Id'] = trail.account.topCurrencyAccount.id;
    }

    return row;
  });

  const batchFile = jsonToSheet([{ sheetData, sheetName: 'Data' }]);

  return { batchId: cashPoolBatch.id, cashPoolBatchFileId: cashPoolBatchFile.id, batchFile };
};
