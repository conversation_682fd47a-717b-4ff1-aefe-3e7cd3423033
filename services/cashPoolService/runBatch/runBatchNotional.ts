import { cashPoolRepository, cashPoolParticipantAccountRepository } from '../../../repositories';
import * as azureService from '../../../services/azureService';
import * as solverService from '../../../services/solverService';
import cashPoolBatchUtils from '../../../utils/cashPoolBatch';
import { sheetToJson } from '../../../utils/documents';
import { BadRequestError } from '../../../utils/ErrorHandler';

import * as batchService from './batchService';

const runBatchNotional = async ({
  clientId,
  cashPoolId,
  batchId,
}: {
  clientId: number;
  cashPoolId: number;
  batchId: number;
}) => {
  const cashPool = await cashPoolBatchUtils.runPoolCashGuards({ clientId, cashPoolId, batchId });
  const topCurrencyAccount = cashPool.topCurrencyAccounts[0];

  const cashPoolLeader = cashPool.participants.find(({ isLeader }: { isLeader: boolean }) => isLeader);
  if (!cashPoolLeader) {
    throw new BadRequestError('Leader not defined.');
  }

  const batchFileBuffer = await azureService.getFile(`cashPoolBatch/${batchId}`);
  const batchFileInJson = sheetToJson(batchFileBuffer);
  batchFileInJson.shift(); // remove header row

  const accounts = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
    whereCashPool: { id: cashPoolId, clientId },
  });

  const solverInput = cashPoolBatchUtils.getBatchSolverInput({
    cashPool,
    topCurrencyAccount,
    accounts,
    batchFileInJson,
    cashPoolLeader,
  });

  const { data: solverData } = await solverService.calculateCashPoolBatch(solverInput);
  const { companiesDataOverTime, leaderPayableReceivable, participantsPayableOrReceivable, batchGrossBenefit } =
    solverData;

  for (const cashPoolDate of companiesDataOverTime) {
    await batchService.updateParticipantAccountsWithSolverData(cashPoolDate, cashPoolId, batchId);

    await batchService.createParticipantAccountTrails({
      whereCashPool: { id: cashPoolId },
      batchId,
      date: cashPoolDate.date,
    });
  }

  const batchPromise = batchService.updateBatch({ batchId, leaderPayableReceivable, batchGrossBenefit });
  const paymentsPromise = batchService.createBatchParticipantPayments({
    batchId,
    participantsPayableOrReceivable,
    cashPoolId,
    currency: cashPool.currencies,
    cashPoolLeaderId: cashPoolLeader.id,
  });
  const cashPoolPromise = cashPoolRepository.updateCashPool({
    where: { id: cashPoolId, clientId },
    attributesToUpdate: { grossBenefit: Number(cashPool.grossBenefit) + batchGrossBenefit },
  });

  await Promise.all([batchPromise, paymentsPromise, cashPoolPromise]);
};

export default runBatchNotional;
