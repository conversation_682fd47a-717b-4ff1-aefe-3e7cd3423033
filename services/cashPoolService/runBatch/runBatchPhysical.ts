import { eachDayOfInterval, format } from 'date-fns';
import { uniq } from 'lodash';

import { cashPoolEnums, Client } from '../../../enums';
import {
  cashPoolRepository,
  cashPoolParticipantAccountRepository,
  clientRepository,
  cashPoolBatchRepository,
} from '../../../repositories';
import * as azureService from '../../../services/azureService';
import * as solverService from '../../../services/solverService';
import cashPoolBatchUtils from '../../../utils/cashPoolBatch';
import { mapHeaderToValue, sheetToJson } from '../../../utils/documents';
import { BadRequestError } from '../../../utils/ErrorHandler';
import { CashPoolBatchStatusType } from '../../../types';

import * as batchService from './batchService';
import { getPhysicalBatchSolverInputGunvor } from '../../../utils/cashPoolBatch/getBatchSolverInput/getBatchSolverInput';
import { checkClientFeatureFlags } from '../../../utils/clientUtils';

const runBatchPhysical = async ({
  batchStatus = cashPoolEnums.batchStatus.UNPAID,
  clientId,
  cashPoolId,
  batchId,
  shouldCreatePayments = true,
}: {
  batchStatus?: CashPoolBatchStatusType;
  clientId: number;
  cashPoolId: number;
  batchId: number;
  shouldCreatePayments?: boolean;
}) => {
  const cashPool = await cashPoolBatchUtils.runPoolCashGuards({ clientId, cashPoolId, batchId });
  const topCurrencyAccount = cashPool.topCurrencyAccounts[0];

  const cashPoolLeader = cashPool.participants.find(({ isLeader }: { isLeader: boolean }) => isLeader);
  if (!cashPoolLeader) {
    throw new BadRequestError('Leader not defined.');
  }

  const batchFileBuffer = await azureService.getFile(`cashPoolBatch/${batchId}`);
  const batchFileInJson = sheetToJson(batchFileBuffer);
  const headers = batchFileInJson[0];
  batchFileInJson.shift(); // remove header row

  const batchFileRecord = mapHeaderToValue(headers as Array<string>, batchFileInJson);

  const client = await clientRepository.getClient(clientId);
  const accounts = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
    whereCashPool: { id: cashPoolId, clientId },
  });

  let solverInput;

  // if (client?.name && checkClientFeatureFlags(Client.GUNVOR, client.name)) {
  //   const currentBatch = await cashPoolBatchRepository.getCashPoolBatch({
  //     where: { id: batchId },
  //     clientId,
  //     cashPoolId,
  //   });

  //   const currentBatchInterval = eachDayOfInterval({
  //     start: new Date(currentBatch.startDate),
  //     end: new Date(currentBatch.endDate),
  //   });

  //   const dateRange = uniq(currentBatchInterval).map((date) => format(date, 'dd/MM/yyyy'));

  //   solverInput = await getPhysicalBatchSolverInputGunvor({
  //     cashPool,
  //     topCurrencyAccount,
  //     accounts,
  //     batchFileInJson: batchFileRecord,
  //     cashPoolLeader,
  //     dateRange,
  //   });
  // }
  // else {
  solverInput = cashPoolBatchUtils.getBatchSolverInput({
    cashPool,
    topCurrencyAccount,
    accounts,
    batchFileInJson,
    cashPoolLeader,
  });
  // }

  const { data: solverData } = await solverService.calculateCashPoolBatch(solverInput);
  const { companiesDataOverTime, leaderPayableReceivable, participantsPayableOrReceivable, batchGrossBenefit } =
    solverData;

  for (const cashPoolDate of companiesDataOverTime) {
    await batchService.updateParticipantAccountsWithSolverData(cashPoolDate, cashPoolId, batchId);

    await batchService.createParticipantAccountTrails({
      whereCashPool: { id: cashPoolId },
      batchId,
      date: cashPoolDate.date,
    });
  }

  const batchPromise = batchService.updateBatch({ batchId, leaderPayableReceivable, batchGrossBenefit, batchStatus });
  if (shouldCreatePayments) {
    await batchService.createBatchParticipantPayments({
      batchId,
      participantsPayableOrReceivable,
      cashPoolId,
      currency: cashPool.currencies,
      cashPoolLeaderId: cashPoolLeader.id,
    });
  }
  const cashPoolPromise = cashPoolRepository.updateCashPool({
    where: { id: cashPoolId, clientId },
    attributesToUpdate: { grossBenefit: Number(cashPool.grossBenefit) + batchGrossBenefit },
  });
  await Promise.all([batchPromise, cashPoolPromise]);
};

export default runBatchPhysical;
