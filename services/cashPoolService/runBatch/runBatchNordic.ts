import { cashPoolRepository, cashPoolParticipantAccountRepository } from '../../../repositories';
import * as azureService from '../../azureService';
import * as solverService from '../../solverService';
import cashPoolBatchUtils from '../../../utils/cashPoolBatch';
import { sheetToJson } from '../../../utils/documents';
import { BadRequestError } from '../../../utils/ErrorHandler';

import * as batchService from './batchService';

const runBatchNordic = async ({
  clientId,
  cashPoolId,
  batchId,
}: {
  clientId: number;
  cashPoolId: number;
  batchId: number;
}) => {
  const cashPool = await cashPoolBatchUtils.runPoolCashGuards({ clientId, cashPoolId, batchId });
  let grossBenefitAccumulator = Number(cashPool.grossBenefit);

  const cashPoolLeader = cashPool.participants.find(({ isLeader }: { isLeader: boolean }) => isLeader);
  if (!cashPoolLeader) {
    throw new BadRequestError('Leader not defined.');
  }

  const batchFileBuffer = await azureService.getFile(`cashPoolBatch/${batchId}`);
  const batchFileInJson = sheetToJson(batchFileBuffer);
  batchFileInJson.shift(); // remove header row

  let batchGrossBenefitSum = 0;
  const leaderPayableReceivableSum = { payableToLeader: 0, receivableFromLeader: 0 };

  const accountsByTopCurrencyAccount = cashPoolBatchUtils.groupAccountsByTopCurrencyAccountId(batchFileInJson);

  for (const topCurrencyAccountId of Object.keys(accountsByTopCurrencyAccount)) {
    // differs from the physical pool by passing currency to get only accounts with the given currency
    const accounts = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
      whereTopCurrencyAccount: { id: topCurrencyAccountId },
      whereCashPool: { id: cashPoolId, clientId },
    });
    if (accounts.length === 0) {
      throw new BadRequestError(`No accounts associated with top currency account id: ${topCurrencyAccountId}`);
    }
    const currentTopCurrencyAccount = accounts[0].topCurrencyAccount;

    /** batchFileInJson contains all the rows with one currency, next iteration contains all rows with some other currency */
    const solverInput = cashPoolBatchUtils.getBatchSolverInput({
      cashPool,
      topCurrencyAccount: accounts[0].topCurrencyAccount,
      accounts,
      batchFileInJson: accountsByTopCurrencyAccount[topCurrencyAccountId],
      cashPoolLeader,
    });
    const { data: solverData } = await solverService.calculateCashPoolBatch(solverInput);
    const { companiesDataOverTime, leaderPayableReceivable, participantsPayableOrReceivable, batchGrossBenefit } =
      solverData;

    grossBenefitAccumulator += batchGrossBenefit;
    batchGrossBenefitSum += batchGrossBenefit;
    leaderPayableReceivableSum.payableToLeader += leaderPayableReceivable.payableToLeader;
    leaderPayableReceivableSum.receivableFromLeader += leaderPayableReceivable.receivableFromLeader;

    // Collect companies with overridden negative debit rates for this currency
    const companiesWithOverriddenNegativeDebitRates = new Set<number>();

    for (const cashPoolDate of companiesDataOverTime) {
      const { updatePromise, companiesWithOverriddenNegativeDebitRates: overriddenCompanies } =
        batchService.updateParticipantAccountsWithSolverData(cashPoolDate, cashPoolId, batchId);
      await updatePromise;

      // Add companies with overridden rates to the set
      overriddenCompanies.forEach((companyId) => companiesWithOverriddenNegativeDebitRates.add(companyId));

      const companyIds = cashPoolDate.companies.map(({ companyId }: { companyId: number }) => companyId);
      // differs from the physical pool by passing currency to create trails for only accounts with the given currency
      await batchService.createParticipantAccountTrails({
        whereCashPool: { id: cashPoolId },
        whereParticipant: { companyId: companyIds },
        whereTopCurrencyAccount: { id: topCurrencyAccountId },
        batchId,
        date: cashPoolDate.date,
      });
    }

    await batchService.createBatchParticipantPayments({
      batchId,
      participantsPayableOrReceivable,
      cashPoolId,
      currency: currentTopCurrencyAccount.currency,
      cashPoolLeaderId: cashPoolLeader.id,
      companiesWithOverriddenNegativeDebitRates,
    });
  }

  await Promise.all([
    batchService.updateBatch({
      batchId,
      leaderPayableReceivable: leaderPayableReceivableSum,
      batchGrossBenefit: batchGrossBenefitSum,
    }),
    cashPoolRepository.updateCashPool({
      where: { id: cashPoolId, clientId },
      attributesToUpdate: { grossBenefit: grossBenefitAccumulator },
    }),
  ]);
};

export default runBatchNordic;
