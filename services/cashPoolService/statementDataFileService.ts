import _ from 'lodash';
import { max, min, parse } from 'date-fns';

import {
  cashPoolRepository,
  cashPoolParticipantAccountRepository,
  cashPoolStatementDataRepository,
  cashPoolStatementDataFileRepository,
} from '../../repositories';
import * as azureService from '../azureService';
import { mapHeaderToValue, sheetToJson } from '../../utils/documents';
import { BadRequestError, NotFoundError } from '../../utils/ErrorHandler';
import * as statementUtils from '../../utils/statementUtils';

type UploadStatementDataFileParams = {
  clientId: number;
  cashPoolId: number;
  userId: number;
  file: {
    buffer: Buffer;
    size: number;
    originalname: string;
    mimetype: string;
  };
};

type DeleteStatementDataFileParams = {
  fileId: number;
  clientId: number;
  cashPoolId: number;
};

export const uploadStatementDataFile = async ({
  clientId,
  cashPoolId,
  file,
  userId,
}: UploadStatementDataFileParams) => {
  const { buffer, size, originalname } = file;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

  const accounts = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
    whereCashPool: { id: cashPoolId, clientId },
  });

  if (!cashPool) throw new NotFoundError('Cash pool');

  const fileData = sheetToJson(buffer, 1, { defval: null });
  const headers = fileData[0];
  fileData.shift(); // remove header row

  const transactions = mapHeaderToValue(headers as Array<string | number>, fileData);

  for (const transaction of transactions) {
    const accountIndex = accounts.findIndex((acc) => {
      return (acc.externalIds || []).some((externalId: any) => externalId.externalId === transaction.account);
    });

    if (accountIndex < 0) {
      continue;
    }

    const date = parse(String(transaction.valueDate), 'dd/MM/yyyy', new Date());
    const isOverlap = statementUtils.validateDate(cashPool, date);

    if (isOverlap) throw new BadRequestError('Some dates overlap with previous batches.');

    const modifier = String(transaction.accountCurrency)[0] === '+' ? 1 : -1;

    await cashPoolStatementDataRepository.addStatementData({
      cashPoolAccountId: accounts[accountIndex].id,
      balanceChange: modifier * Number(transaction.accountAmount),
      date,
      statementDate: date,
      transactionReferenceNumber: statementUtils.consts.TEMPLATE_UPLOAD,
      statementNumber: statementUtils.consts.TEMPLATE_UPLOAD,
    });
  }

  const startDate = min(
    transactions.map((transaction) => parse(String(transaction.valueDate), 'dd/MM/yyyy', new Date())),
  ).toString();
  const endDate = max(
    transactions.map((transaction) => parse(String(transaction.valueDate), 'dd/MM/yyyy', new Date())),
  ).toString();

  const createdFile = await cashPoolStatementDataFileRepository.createStatementDataFile(
    file,
    cashPool.id,
    startDate,
    endDate,
    userId,
  );
  await azureService.uploadFile(`statement/${clientId}/${createdFile.id}_${originalname}`, buffer, size);
};

export const deleteStatementDataFile = async ({ fileId, clientId, cashPoolId }: DeleteStatementDataFileParams) => {
  try {
    const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
    if (!cashPool) throw new NotFoundError('Cash pool');

    const file = await cashPoolStatementDataFileRepository.getStatementDataFile(fileId);
    if (!file) throw new NotFoundError('Cash Pool Statement File');

    await cashPoolStatementDataFileRepository.deleteStatementDataFile(fileId, cashPoolId);

    await azureService.deleteFile(`statement/${clientId}/${file.id}_${file.name}${file.extension}`);
  } catch (err) {
    console.log(err);
    throw err;
  }
};
