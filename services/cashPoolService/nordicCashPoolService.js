const _ = require('lodash');

const {
  cashPoolRepository,
  cashPoolParticipantsRepository,
  cashPoolParticipantAccountRepository,
  topCurrencyAccountRepository,
  participantAccountTrailsRepository,
  cashPoolParticipantTrailRepository,
} = require('../../repositories');

/**
 * If the `requestTopCurrencyAccount` is an existing top currency account (has id), it's just returned, with isCreated: false.
 * If it doesn't exist it's created and along with the accounts passed back so participants and accounts get created.
 */
const getExistingOrCreateTopCurrencyAccount = async ({
  currentTopCurrencyAccounts,
  requestTopCurrencyAccount,
  cashPoolId,
}) => {
  const existingTopCurrencyAccount = currentTopCurrencyAccounts.find((c) => c.id === requestTopCurrencyAccount.id);
  if (existingTopCurrencyAccount) {
    return { currentTopCurrencyAccount: existingTopCurrencyAccount, isCreated: false };
  }

  const createdTopCurrencyAccount = await topCurrencyAccountRepository.createTopCurrencyAccount({
    ...requestTopCurrencyAccount,
    cashPoolId,
  });
  return {
    currentTopCurrencyAccount: {
      ...createdTopCurrencyAccount.dataValues,
      accounts: requestTopCurrencyAccount.accounts,
    },
    isCreated: true,
  };
};

/**
 * If isCreated that means a new top currency account was added. To complete the creation, participants and accounts
 * must be created. Therefore firstly participants that may not exist yet are created. After that all the accounts are
 * created since it's a new top currency account. Accounts depend on participant ids so participants are created first
 * or existing participant ids are used.
 */
const createNewTopCurrencyAccountParticipantsAndAccounts = async ({
  currentTopCurrencyAccount,
  existingParticipantsByCompanyId,
  cashPoolId,
}) => {
  const participantsToCreate = [];
  for (const { companyId } of currentTopCurrencyAccount.accounts) {
    if (!existingParticipantsByCompanyId[companyId]) {
      participantsToCreate.push({ companyId, cashPoolId });
    }
  }
  const createdParticipants = await cashPoolRepository.createCashPoolParticipants(participantsToCreate);
  const createdParticipantsByCompanyId = _.keyBy(
    createdParticipants.map(({ id, companyId }) => ({ cashPoolParticipantId: id, companyId })),
    'companyId',
  );

  const accountsToCreate = [];
  for (const account of currentTopCurrencyAccount.accounts) {
    accountsToCreate.push({
      ...account,
      cashPoolParticipantId:
        createdParticipantsByCompanyId[account.companyId]?.cashPoolParticipantId ??
        existingParticipantsByCompanyId[account.companyId]?.cashPoolParticipantId,
      topCurrencyAccountId: currentTopCurrencyAccount.id,
      currency: currentTopCurrencyAccount.currency,
    });
  }
  const newAccounts = await cashPoolParticipantAccountRepository.createCashPoolParticipantAccounts(accountsToCreate);

  const trailDates = await participantAccountTrailsRepository.getCashPoolParticipantAccountTrailDates({ cashPoolId });

  // TODO matej Same code as in cashPoolAccountService.js addNewAccounts (except currency: currentTopCurrencyAccount.currency)
  // We create trails with everything set to null for all the cash pool calculations new participants weren't there for
  // This is to allow easier display of data on the frontend
  const trailCreatePromises = newAccounts.map((newAccount) => {
    // this for loop is necessary to set date to the timestamp when the missed trails were created
    const trailsMissed = trailDates.map(({ date }) => ({
      participantAccountId: newAccount.id,
      date: new Date(date.toDateString()),
      currency: currentTopCurrencyAccount.currency,
    }));
    return cashPoolParticipantTrailRepository.createCashPoolParticipantTrails(trailsMissed);
  });
  return Promise.all(trailCreatePromises);
};

/**
 * Deletes the top currency accounts which deletes cash pool accounts. After that participants that have no accounts are deleted too.
 */
const deleteTopCurrencyAccounts = async ({ cashPoolId, currentTopCurrencyAccounts, requestTopCurrencyAccounts }) => {
  const topCurrencyAccountsToDelete = _.differenceBy(currentTopCurrencyAccounts, requestTopCurrencyAccounts, 'id');
  const topCurrencyAccountIdsToDelete = topCurrencyAccountsToDelete.map(({ id }) => id);
  await topCurrencyAccountRepository.deleteTopCurrencyAccount({ id: topCurrencyAccountIdsToDelete });

  // leader shouldn't be deleted even if it has not accounts
  const where = { cashPoolId, isLeader: false };
  const participants = await cashPoolParticipantsRepository.getCashPoolParticipantsWithAccounts({ where });
  const participantIdsToDelete = participants.filter((p) => p.accounts.length === 0).map(({ id }) => id);
  await cashPoolParticipantsRepository.deleteCashPoolParticipant({ id: participantIdsToDelete });
};

module.exports = {
  getExistingOrCreateTopCurrencyAccount,
  createNewTopCurrencyAccountParticipantsAndAccounts,
  deleteTopCurrencyAccounts,
};
