import _ from 'lodash';
import { isDate, parse, isBefore, isEqual, startOfMonth, endOfMonth } from 'date-fns';

import {
  cashPoolRepository,
  cashPoolBatchRepository,
  cashPoolBatchFileRepository,
  cashPoolParticipantAccountRepository,
  clientRepository,
} from '../../repositories';
import { cashPoolEnums, Client } from '../../enums';
import * as azureService from '../../services/azureService';
import cashPoolBatchUtils from '../../utils/cashPoolBatch';
import { mapHeaderToValue, sheetToJson } from '../../utils/documents';
import { BadRequestError, NotFoundError } from '../../utils/ErrorHandler';
import { UploadBatchFileType, CashPoolBatchStatusType, GetCashPoolParticipantAccountsType } from '../../types';
import { checkClientFeatureFlags } from '../../utils/clientUtils/clientFeatureFlags';

type UploadBatchFileParams = {
  batchStatus?: CashPoolBatchStatusType;
  clientId: number;
  userId: number;
  cashPoolId: number;
  file: {
    buffer: Buffer;
    size: number;
    originalname: string;
    mimetype: string;
  };
};

export const uploadBatchFile = async ({
  batchStatus = cashPoolEnums.batchStatus.UNPOOLED,
  clientId,
  userId,
  cashPoolId,
  file,
}: UploadBatchFileParams): Promise<UploadBatchFileType> => {
  const { buffer, size, originalname, mimetype } = file;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

  if (!cashPool) throw new NotFoundError('Cash pool');

  const client = await clientRepository.getClient(clientId);

  let startDate;
  let endDate;

  const fileData = sheetToJson(buffer, 1, { defval: null });
  const headers = fileData[0];
  fileData.shift(); // remove header row

  if (client?.name && checkClientFeatureFlags(Client.GUNVOR, client.name)) {
    const transactions = mapHeaderToValue(headers as Array<string | number>, fileData);
    startDate = startOfMonth(parse(String(transactions[0].valueDate), 'dd/MM/yyyy', new Date()));
    endDate = endOfMonth(parse(String(transactions[transactions.length - 1].valueDate), 'dd/MM/yyyy', new Date()));

    if (
      !_.isEmpty(cashPool.batches) &&
      (isBefore(startDate, new Date(cashPool.batches[0].endDate)) ||
        isEqual(startDate, new Date(cashPool.batches[0].endDate)))
    ) {
      throw new BadRequestError('Some dates overlap with previous batches.');
    }
  } else {
    startDate = fileData[0][0]; // first date in the file
    endDate = fileData[fileData.length - 1][0]; // last date in the file
  }

  if (!isDate(startDate)) {
    throw new BadRequestError('Start date is not a valid date.');
  }
  if (!isDate(endDate)) {
    throw new BadRequestError('End date is not a valid date.');
  }

  const accounts = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
    whereCashPool: { id: cashPoolId, clientId },
  });
  const accountsSnapshot = JSON.parse(JSON.stringify(accounts)).map((account: GetCashPoolParticipantAccountsType) =>
    _.omit(account, ['participant', 'topCurrencyAccount', 'createdAt', 'updatedAt']),
  );

  const batchData = {
    cashPoolId,
    createdByUserId: userId,
    startDate,
    endDate,
    status: batchStatus,
    accountsSnapshot,
  };

  const cashPoolBatch = await cashPoolBatchRepository.createCashPoolBatch(batchData);

  const batchFileData = cashPoolBatchUtils.getCashPoolBatchFileData({
    cashPoolBatchId: cashPoolBatch.id,
    originalname,
    mimetype,
  });
  const cashPoolBatchFile = await cashPoolBatchFileRepository.createCashPoolBatchFile(batchFileData);

  await azureService.uploadFile(`cashPoolBatch/${cashPoolBatchFile.id}`, buffer, size);

  return { ...cashPoolBatchFile.dataValues, cashPoolId };
};
