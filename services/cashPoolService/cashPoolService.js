const _ = require('lodash');

const {
  cashPoolRepository,
  topCurrencyAccountRepository,
  cashPoolAuditTrailRepository,
  cashPoolParticipantAccountRepository,
  topCurrencyAccountAuditTrailRepository,
  companyRepository,
} = require('../../repositories');

const updateCashPoolAndTopCurrencyAccount = async ({ cashPoolId, clientId, topCurrencyAccountId, body }) => {
  const updateCashPoolPromise = cashPoolRepository.updateCashPool({
    where: { id: cashPoolId, clientId },
    attributesToUpdate: _.omit(body, ['interestType']),
  });
  const updateTopCurrencyAccountPromise = topCurrencyAccountRepository.updateTopCurrencyAccount(topCurrencyAccountId, {
    creditInterestRate: body.creditInterestRate,
    debitInterestRate: body.debitInterestRate,
  });

  const [[, [updatedCashPool]]] = await Promise.all([updateCashPoolPromise, updateTopCurrencyAccountPromise]);

  return updatedCashPool;
};

const createPhysicalNotionalCashPoolAuditTrail = async (cashPool, allAccounts, clientId) => {
  const auditTrail = { ...cashPool, cashPoolId: cashPool.id };
  delete auditTrail.id;

  const companiesIds = allAccounts.map((participant) => participant.companyId);
  const [companies, participantsAccounts] = await Promise.all([
    companyRepository.getCompanies({ id: companiesIds, clientId }),
    cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts({
      whereParticipant: { companyId: companiesIds, cashPoolId: cashPool.id },
    }),
  ]);

  auditTrail.participants = companies.map((company, index) => {
    const participantAccount = participantsAccounts[index];
    const { name, country, industry, creditRating } = company;
    const { creditInterestRate, debitInterestRate, currency } = participantAccount;
    return { name, country, industry, creditRating, creditInterestRate, debitInterestRate, currency };
  });

  await cashPoolAuditTrailRepository.createCashPoolAuditTrail(auditTrail);
};

const createNordicCashPoolAuditTrail = async (cashPool, allTopCurrencyAccounts, clientId) => {
  const auditTrail = { ...cashPool, cashPoolId: cashPool.id };
  delete auditTrail.id;

  const createdCashPoolAuditTrail = await cashPoolAuditTrailRepository.createCashPoolAuditTrail(auditTrail);

  const topCurrencyAccounts = [];
  for (const topCurrencyAccount of allTopCurrencyAccounts) {
    const { name, currency, interestType, overnightRate, creditInterestRate, debitInterestRate, accounts } =
      topCurrencyAccount;
    const companiesIds = accounts.map((participant) => participant.dataValues.companyId);
    const companies = await companyRepository.getCompanies({ id: companiesIds, clientId });

    const participants = accounts.map((participant, index) => {
      const { name, country, industry, creditRating } = companies[index];
      return {
        name,
        country,
        industry,
        creditRating,
        creditInterestRate: participant.dataValues.creditInterestRate,
        debitInterestRate: participant.dataValues.debitInterestRate,
      };
    });
    topCurrencyAccounts.push({
      cashPoolAuditTrailId: createdCashPoolAuditTrail.id,
      name,
      currency,
      interestType,
      overnightRate,
      creditInterestRate,
      debitInterestRate,
      participants,
    });
  }
  await topCurrencyAccountAuditTrailRepository.createTopCurrencyAccountAuditTrails(topCurrencyAccounts);
};

module.exports = {
  updateCashPoolAndTopCurrencyAccount,
  createPhysicalNotionalCashPoolAuditTrail,
  createNordicCashPoolAuditTrail,
};
