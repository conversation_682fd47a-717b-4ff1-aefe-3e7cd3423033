const _ = require('lodash');

const { cashPoolEnums } = require('../../enums');
const { roundToXDecimals } = require('../../utils/numbers');
const { format } = require('date-fns');

const getGeneralData = ({ cashPool, topCurrencyAccount, user, dateRange }) => {
  const { startDate, endDate } = dateRange;
  const isPhysical = cashPool.type === cashPoolEnums.cashPoolTypes.PHYSICAL;
  const isNordic = cashPool.type === cashPoolEnums.cashPoolTypes.NORDIC;
  const interestType = isNordic ? topCurrencyAccount.interestType : cashPool.interestType;
  const [word, wordType] = interestType === 'fixed' ? ['Rate', '%'] : ['Spread', 'bps'];

  const now = new Date();
  const startDateLocale = _.isDate(startDate) ? format(new Date(startDate), 'dd.MM.yyyy') : 'n/a';
  const endDateLocale = _.isDate(endDate) ? format(new Date(endDate), 'dd.MM.yyyy') : 'n/a';

  const cashPoolData = [
    ['Cash Pool', cashPool.name],
    ['    Type', cashPool.type],
    ['    Base Currency', cashPool.currencies],
    ['    Country', cashPool.country],
    ['    Interest Type', cashPool.interestType || 'n/a'],
  ];
  const exportData = [
    ['Date Range Exported', `${startDateLocale} - ${endDateLocale}`],
    ['Export Date', format(new Date(now), 'dd.MM.yyyy')],
    ['Export Time', now.toLocaleTimeString()],
    ['Exported By User', user.fullName],
  ];

  if (isNordic) {
    return [
      ...cashPoolData,
      [],
      ['Currency Top Account', topCurrencyAccount.name],
      ['    Holder', cashPool.leader.name],
      ['    Currency', topCurrencyAccount.currency],
      ['    Overnight Rate', topCurrencyAccount.overnightRate ?? 'n/a'],
      [`    Credit Interest ${word}`, `${topCurrencyAccount.creditInterestRate}${wordType}`],
      [`    Debit Interest ${word}`, `${topCurrencyAccount.debitInterestRate}${wordType}`],
      [],
      ...exportData,
    ];
  }

  if (isPhysical) {
    return [
      ...cashPoolData,
      ['    Overnight Rate', cashPool.overnightRate ?? 'n/a'],
      [`    Credit Interest ${word}`, `${cashPool.creditInterestRate}${wordType}`],
      [`    Debit Interest ${word}`, `${cashPool.debitInterestRate}${wordType}`],
      [],
      ...exportData,
    ];
  }
};

const totalColumn = 'Total';
const dateColumn = 'Date';

const adjustedCreditInterestRateColumnName = 'adjustedCreditInterestRate';
const adjustedDebitInterestRateColumnName = 'adjustedDebitInterestRate';
const balanceColumnName = 'balance';

const handleDataDisplay = (trail, dbAttribute, unit) => {
  if (unit) {
    const multiplier = unit === '%' ? 100 : 1;

    if (dbAttribute === adjustedCreditInterestRateColumnName && trail[balanceColumnName] < 0) return '';
    if (dbAttribute === adjustedDebitInterestRateColumnName && trail[balanceColumnName] >= 0) return '';

    return `${roundToXDecimals(trail[dbAttribute] * multiplier, 7)}${unit}`;
  }

  return trail[dbAttribute];
};

/**
 * Return data in format
 * Date             CPP1            CPP2            CPP3
 * 30/07/2022       data            data            data
 * 31/07/2022       data            data            data
 * Data for participants depends on the dbAttribute which is the name
 * of the column for that sheet. For example there is a Balances sheet
 * and the column in `ParticipantAccountTrails` is `balance`
 */
const getParticipantDataWithDate = ({ trails, dbAttribute, includeTotal, unit }) => {
  const sheetData = [];
  let row = {};

  let rowAccumulator = 0;
  let columnAccumulator = { Date: totalColumn };
  for (const trail of trails) {
    const date = format(new Date(trail.date), 'dd.MM.yyyy');
    if (row[dateColumn] && row[dateColumn] !== date) {
      if (includeTotal) row[totalColumn] = rowAccumulator;

      sheetData.push(row);
      row = {};
      rowAccumulator = 0;
    }
    row[dateColumn] = date;
    row[trail.account.participant.company.name] = handleDataDisplay(trail, dbAttribute, unit);
    if (includeTotal) {
      if (columnAccumulator[trail.account.participant.company.name] === undefined) {
        columnAccumulator[trail.account.participant.company.name] = trail[dbAttribute];
      } else {
        columnAccumulator[trail.account.participant.company.name] += trail[dbAttribute];
      }
      rowAccumulator += trail[dbAttribute];
    }
  }

  if (includeTotal) row[totalColumn] = rowAccumulator;
  sheetData.push(row);

  if (includeTotal) {
    const totalSum = Object.values(columnAccumulator).reduce((acc, curr) => {
      if (typeof curr === 'number') return acc + curr;
      return acc;
    }, 0);
    sheetData.push({ ...columnAccumulator, Total: totalSum });
  }

  return sheetData;
};

const getSheetData = ({ cashPool, topCurrencyAccount, dateRange, trails, user }) => {
  const isNordic = cashPool.type === cashPoolEnums.cashPoolTypes.NORDIC;
  const interestType = isNordic ? topCurrencyAccount.interestType : cashPool.interestType;
  const [word, unit] = interestType === 'fixed' ? ['Rate', '%'] : ['Spread', 'bps'];

  return [
    { sheetData: getGeneralData({ cashPool, topCurrencyAccount, dateRange, user }), sheetName: 'Cash Pool Summary' },
    {
      sheetData: getParticipantDataWithDate({ trails, dbAttribute: balanceColumnName }),
      sheetName: 'Balances',
    },
    {
      sheetData: getParticipantDataWithDate({ trails, dbAttribute: 'creditInterestRate', unit }),
      sheetName: `Credit ${word}`,
    },
    {
      sheetData: getParticipantDataWithDate({ trails, dbAttribute: 'debitInterestRate', unit }),
      sheetName: `Debit ${word}`,
    },
    {
      sheetData: getParticipantDataWithDate({ trails, dbAttribute: adjustedCreditInterestRateColumnName, unit }),
      sheetName: `Credit ${word} (adj.)`,
    },
    {
      sheetData: getParticipantDataWithDate({ trails, dbAttribute: adjustedDebitInterestRateColumnName, unit }),
      sheetName: `Debit ${word} (adj.)`,
    },
    {
      sheetData: getParticipantDataWithDate({
        trails,
        dbAttribute: 'adjustedCreditInterestReceived',
        includeTotal: true,
      }),
      sheetName: 'Interest Receivable',
    },
    {
      sheetData: getParticipantDataWithDate({ trails, dbAttribute: 'adjustedDebitInterestPaid', includeTotal: true }),
      sheetName: 'Interest Payable',
    },
    {
      sheetData: getParticipantDataWithDate({ trails, dbAttribute: 'netInterestBenefit', includeTotal: true }),
      sheetName: 'Participant Benefit',
    },
  ];
};

const getParticipantDataWithDateConsolidated = ({ trails, dbAttributes, unit, cashPoolLeaderName }) => {
  const sheetData = [];

  for (const trail of trails) {
    let row = {};
    const date = format(new Date(trail.date), 'dd.MM.yyyy');
    row[dateColumn] = date;
    row['Participant'] = trail.account.participant.company.name;
    row['Counterparty'] = cashPoolLeaderName;
    row['External ID'] = trail.account.externalIds.map(({ externalId }) => externalId).join(', ');
    row['Currency'] = trail.currency;
    for (const attribute of dbAttributes) {
      row[attribute.columnName] = handleDataDisplay(trail, attribute.name, attribute.unit ? unit : undefined);
    }

    sheetData.push(row);
  }

  return sheetData;
};

const getConsolidatedSheetData = ({ cashPool, topCurrencyAccount, dateRange, trails, user, cashPoolLeaderName }) => {
  const isNordic = cashPool.type === cashPoolEnums.cashPoolTypes.NORDIC;
  const interestType = isNordic ? topCurrencyAccount.interestType : cashPool.interestType;
  const [word, unit] = interestType === 'fixed' ? ['Rate', '%'] : ['Spread', 'bps'];

  const dbAttributes = [
    { name: balanceColumnName, unit: false, columnName: 'Balance' },
    { name: 'creditInterestRate', unit: true, columnName: `Credit ${word}` },
    { name: 'debitInterestRate', unit: true, columnName: `Debit ${word}` },
    { name: adjustedCreditInterestRateColumnName, unit: true, columnName: `Credit ${word} (adj.)` },
    { name: adjustedDebitInterestRateColumnName, unit: true, columnName: `Debit ${word} (adj.)` },
    { name: 'adjustedCreditInterestReceived', unit: false, columnName: 'Interest Receivable' },
    { name: 'adjustedDebitInterestPaid', unit: false, columnName: 'Interest Payable' },
    { name: 'netInterestBenefit', unit: false, columnName: 'Participant Benefit' },
  ];

  return [
    { sheetData: getGeneralData({ cashPool, topCurrencyAccount, dateRange, user }), sheetName: 'Cash Pool Summary' },
    {
      sheetData: getParticipantDataWithDateConsolidated({ trails, dbAttributes, unit, cashPoolLeaderName }),
      sheetName: 'Data',
    },
  ];
};

const getAllCashPoolsSheetData = ({ trails }) => {
  const sheetData = [];

  for (const trail of trails) {
    const interestType = trail.account.topCurrencyAccount.interestType;
    const [word, unit] = interestType === 'fixed' ? ['Rate', '%'] : ['Spread', 'bps'];

    const dbAttributes = [
      { name: balanceColumnName, unit: false, columnName: 'Balance' },
      { name: 'creditInterestRate', unit: true, columnName: `Credit ${word}` },
      { name: 'debitInterestRate', unit: true, columnName: `Debit ${word}` },
      { name: adjustedCreditInterestRateColumnName, unit: true, columnName: `Credit ${word} (adj.)` },
      { name: adjustedDebitInterestRateColumnName, unit: true, columnName: `Debit ${word} (adj.)` },
      { name: 'adjustedCreditInterestReceived', unit: false, columnName: 'Interest Receivable' },
      { name: 'adjustedDebitInterestPaid', unit: false, columnName: 'Interest Payable' },
      { name: 'netInterestBenefit', unit: false, columnName: 'Participant Benefit' },
    ];

    let row = {};
    const date = new Date(trail.date);
    row[dateColumn] = date;
    row['Cash Pool'] = trail.cashPoolName;
    row['Participant'] = trail.account.participant.company.name;
    row['Counterparty'] = trail.cashPoolLeaderName;
    row['External ID'] = trail.account.externalIds.map(({ externalId }) => externalId).join(', ');
    row['Currency'] = trail.currency;
    for (const attribute of dbAttributes) {
      row[attribute.columnName] = handleDataDisplay(trail, attribute.name, attribute.unit ? unit : undefined);
    }

    sheetData.push(row);
  }

  return [
    {
      sheetData,
      sheetName: 'Data',
    },
  ];
};

const getLeaderBenefitSheetData = (leaderBenefit, cashPoolLeaderName) => {
  const trails = leaderBenefit.map((leaderBenefit) => ({
    ...leaderBenefit.dataValues,
    account: { participant: { company: { name: cashPoolLeaderName } } },
  }));

  return [
    {
      sheetData: getParticipantDataWithDate({ trails, dbAttribute: 'leaderBenefit', includeTotal: true }),
      sheetName: 'Leader Benefit',
    },
  ];
};

module.exports = { getSheetData, getLeaderBenefitSheetData, getConsolidatedSheetData, getAllCashPoolsSheetData };
