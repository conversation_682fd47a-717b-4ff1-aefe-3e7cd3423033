const _ = require('lodash');
const { Op } = require('sequelize');

const {
  cashPoolParticipantsRepository,
  cashPoolParticipantTrailRepository,
  cashPoolRepository,
  cashPoolParticipantAccountRepository,
  participantAccountTrailsRepository,
  cashPoolParticipantAccountIdsRepository,
} = require('../../repositories');
const { getDeletedParticipantAccountFields } = require('../../utils/cashPool');

/** Sets the fields of accounts to null and marks accounts as deleted (deletedAt)  */
const deleteAccounts = async ({ requestAccounts, allAccounts }) => {
  const accountsToDelete = _.differenceBy(allAccounts, requestAccounts, 'companyId');
  if (accountsToDelete.length > 0) {
    // Sets all the fields of the accounts of to null and sets deletedAt to current timestamp (soft delete)
    const deleteAccountsPromises = accountsToDelete.map(({ accountId }) =>
      cashPoolParticipantAccountRepository.updateCashPoolParticipantAccount({
        where: { id: accountId },
        attributesToUpdate: getDeletedParticipantAccountFields(),
      }),
    );
    return Promise.all(deleteAccountsPromises);
  }
};

/**
 * Restores accounts which were marked as deleted.
 * Trails are created for all accounts (including deleted ones) when
 * doing cash pool calculations which means nothing else is needed to restore them.
 */
const restoreAccounts = async ({ topCurrencyAccountId, requestAccounts }) => {
  const softDeletedAccounts =
    await cashPoolParticipantAccountRepository.getCashPoolParticipantAccountsByTopCurrencyAccount({
      whereAccount: { deletedAt: { [Op.ne]: null } },
      whereTopCurrencyAccount: { id: topCurrencyAccountId },
      paranoidAccount: false,
    });
  const softDeletedAccountsWithCompanyId = softDeletedAccounts.map(({ dataValues }) => ({
    ...dataValues,
    companyId: dataValues.participant.companyId,
  }));
  const toRestore = _.intersectionBy(softDeletedAccountsWithCompanyId, requestAccounts, 'companyId');
  await Promise.all(
    toRestore.map(({ id }) => cashPoolParticipantAccountRepository.restoreCashPoolParticipantAccounts({ id })),
  );
  return toRestore;
};

/**
 * Cash pool leader is added in `Cash_Pool_Participants` table when cash pool is created. For nordic the same company
 * that is the leader of the cash pool can be added as an account of a top currency account. So the `Cash_Pool_Participants` row
 * already exists and it's necessary to just add the `CashPoolParticipantAccount`. It's done by filtering out the leader in
 * `participantsToAddWithoutLeader` and the `leaderAccount` returned from this function is used in `createAccountPromises`.
 * Chained .filter(Boolean) is there to remove undefined this function returns when the leader is not added as a top currency account.
 * `leaderCompanyId` is undefined when updating the Physical cash pool.
 *
 * Requirements changed since the above doc was written. Now physical cash pool can have a leader as an account. Only change made to make
 * this work was to provide `cashPool.leaderId` to the `handleAccounts` function.
 */
const handleLeaderAddedAsAccount = async ({ participantsToAdd, cashPoolId, leaderCompanyId }) => {
  if (!leaderCompanyId) return;

  const leaderAccount = participantsToAdd.find((p) => p.companyId === leaderCompanyId);

  if (!leaderAccount) return;

  const newLeaderAccount = await cashPoolParticipantsRepository.getCashPoolParticipant({
    cashPoolId,
    companyId: leaderCompanyId,
  });
  return newLeaderAccount.dataValues;
};

/** Adds new participants and accounts. Also creates trails which were missed (trails that were created before) */
const addNewAccounts = async ({
  cashPoolId,
  topCurrencyAccountId,
  topCurrencyAccountCurrency,
  restoredAccounts,
  requestAccounts,
  allAccounts,
  leaderCompanyId,
}) => {
  const withoutRestored = _.differenceBy(requestAccounts, restoredAccounts, 'companyId');
  const onlyNewParticipants = _.differenceBy(withoutRestored, allAccounts, 'companyId');
  const participantsToAdd = onlyNewParticipants.map((p) => ({ ...p, cashPoolId }));
  const participantsToAddByCompanyId = _.keyBy(participantsToAdd, 'companyId');

  const leaderAccount = await handleLeaderAddedAsAccount({ participantsToAdd, cashPoolId, leaderCompanyId });

  if (participantsToAdd.length > 0) {
    const participantsToAddWithoutLeader = participantsToAdd.filter((p) => p.companyId !== leaderCompanyId);
    const newParticipants = await cashPoolRepository.createCashPoolParticipants(participantsToAddWithoutLeader);
    const newAccounts = await Promise.all(
      [...newParticipants, leaderAccount].filter(Boolean).map(({ id, companyId }) =>
        cashPoolParticipantAccountRepository.createCashPoolParticipantAccount({
          cashPoolParticipantId: id,
          topCurrencyAccountId,
          currency: participantsToAddByCompanyId[companyId].currency ?? topCurrencyAccountCurrency,
          creditInterestRate: participantsToAddByCompanyId[companyId].creditInterestRate,
          debitInterestRate: participantsToAddByCompanyId[companyId].debitInterestRate,
          balance: participantsToAddByCompanyId[companyId].balance,
        }),
      ),
    );

    // Creates external ids to the new accounts
    for (let i = 0; i < newAccounts.length; i++) {
      const { externalIds } = participantsToAdd[i];
      const { id } = newAccounts[i];
      if (Array.isArray(externalIds)) {
        await cashPoolParticipantAccountIdsRepository.bulkCreate(
          externalIds.map((externalId) => ({ externalId, cashPoolAccountId: id })),
        );
      }
    }

    const trailDates = await participantAccountTrailsRepository.getCashPoolParticipantAccountTrailDates({ cashPoolId });

    // We create trails with everything set to null for all the cash pool calculations new participants weren't there for
    // This is to allow easier display of data on the frontend
    const trailCreatePromises = newAccounts.map((newAccount) => {
      // this for loop is necessary to set date to the timestamp when the missed trails were created
      const trailsMissed = trailDates.map(({ date }) => ({
        participantAccountId: newAccount.id,
        date: new Date(date.toDateString()),
        currency: topCurrencyAccountCurrency,
      }));
      return cashPoolParticipantTrailRepository.createCashPoolParticipantTrails(trailsMissed);
    });
    await Promise.all(trailCreatePromises);
  }
};

const updateAccounts = async ({ topCurrencyAccountId, restoredAccounts, requestAccounts, allAccounts }) => {
  const withoutRestored = _.differenceBy(requestAccounts, restoredAccounts, 'companyId');
  const existingAccounts = _.intersectionBy(withoutRestored, allAccounts, 'companyId');
  const existingAccountsByCompanyId = _.keyBy(existingAccounts, 'companyId');

  // Gets all the accounts of the participants that should be updated (in that cashpool)
  const accountsToUpdatePromises = existingAccounts.map(({ companyId }) =>
    cashPoolParticipantAccountRepository.getCashPoolParticipantAccountsByTopCurrencyAccount({
      whereTopCurrencyAccount: { id: topCurrencyAccountId },
      whereParticipant: { companyId },
    }),
  );
  const accountsToUpdate = _.flatten(await Promise.all(accountsToUpdatePromises));

  // Updates CIR and DIR of accounts of participants
  await Promise.all(
    accountsToUpdate.map((accountToUpdate) =>
      cashPoolParticipantAccountRepository.updateCashPoolParticipantAccount({
        where: { id: accountToUpdate.id },
        attributesToUpdate: {
          creditInterestRate: existingAccountsByCompanyId[accountToUpdate.participant.company.id].creditInterestRate,
          debitInterestRate: existingAccountsByCompanyId[accountToUpdate.participant.company.id].debitInterestRate,
          currency: existingAccountsByCompanyId[accountToUpdate.participant.company.id].currency, // Notional only otherwise undefined
          balance: existingAccountsByCompanyId[accountToUpdate.participant.company.id].balance,
          generateInterestStatementData:
            existingAccountsByCompanyId[accountToUpdate.participant.company.id].generateInterestStatementData,
        },
      }),
    ),
  );

  const externalIdsWithAccountId = _.flatten(
    requestAccounts
      .filter((account) => account.cashPoolAccountId != null) // cashPoolAccountId is undefined for new accounts. Their external ids are inserted in addNewAccounts function
      .map((account) => ({ externalId: account.externalIds, cashPoolAccountId: account.cashPoolAccountId })),
  );

  // Deletes all the external ids of the accounts that are being updated and creates new ones
  await cashPoolParticipantAccountIdsRepository.deleteByCashPoolAccountId(
    externalIdsWithAccountId.map((e) => e.cashPoolAccountId),
  );

  await cashPoolParticipantAccountIdsRepository.bulkCreate(
    externalIdsWithAccountId.flatMap((e) =>
      e.externalId.map((id) => ({ externalId: id, cashPoolAccountId: e.cashPoolAccountId })),
    ),
  );
};

/** Deletes accounts, restores deleted accounts, adds new participants and accounts, updates existing accounts */
const handleAccounts = async ({
  cashPoolId,
  requestAccounts,
  allAccounts,
  topCurrencyAccountId,
  topCurrencyAccountCurrency,
  leaderCompanyId,
}) => {
  await deleteAccounts({ requestAccounts, allAccounts });
  const restoredAccounts = await restoreAccounts({ requestAccounts, topCurrencyAccountId });
  await addNewAccounts({
    cashPoolId,
    topCurrencyAccountId,
    topCurrencyAccountCurrency,
    restoredAccounts,
    requestAccounts,
    allAccounts,
    leaderCompanyId,
  });
  await updateAccounts({
    topCurrencyAccountId,
    restoredAccounts,
    requestAccounts,
    allAccounts,
  });
};

module.exports = { handleAccounts };
