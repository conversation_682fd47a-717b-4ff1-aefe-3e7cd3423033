import creditRatingRepository from '../../repositories/creditRatingRepository';
import * as creditRatingService from '../../services/creditRatingService';
import * as azureService from '../../services/azureService';
import { initializeGeneratedFileData } from '../../utils/creditRatingUtils';
import { UserType } from '../../types';

const createCreditRating = async (creditRating: any, user: UserType) => {
  const { rating, probabilityOfDefault, pdf } = await creditRatingService.createCreditRatingReport(
    creditRating,
    user.clientId,
  );

  creditRating.creditRating = { rating };
  creditRating.probabilityOfDefault = probabilityOfDefault;
  creditRating.files = [initializeGeneratedFileData(creditRating.company)];

  const { dataValues } = await creditRatingRepository.createCreditRating(creditRating, user);

  const fileId = dataValues.files[0].dataValues.id;
  await azureService.uploadFile(`creditRating/${fileId}`, pdf, Buffer.byteLength(pdf));

  return dataValues;
};

export default createCreditRating;
