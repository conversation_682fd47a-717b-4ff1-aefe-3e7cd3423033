import _ from 'lodash';

import { fredOlsenClientIds } from '../../enums';
import oplonRiskModels from '../../models/oplonRiskModels';
import { BadRequestError } from '../../utils/ErrorHandler';
import logger from '../../utils/logger';
import oplonRiskAPI from './oplonRiskAPI';
import { CreditRatingType } from '../../types';

/**
 * <PERSON><PERSON><PERSON><PERSON> uses three accounts, but since they are the same client
 * we only send the id of one of them. Otherwise we would be
 * charged for three different clients.
 */
const mapClientId = (clientId: number) => {
  if (fredOlsenClientIds.includes(clientId)) {
    return fredOlsenClientIds[0];
  }
  return clientId;
};

async function createCreditRatingReport(creditRating: CreditRatingType, clientId: number) {
  try {
    const user_email = process.env.OPLON_RISK_EMAIL;

    const creditRatingCopy = _.cloneDeep(creditRating);

    if (creditRatingCopy.company.pseudonym) {
      // We make a copy so we don't change the original credit rating
      creditRatingCopy.company.name = creditRatingCopy.company.pseudonym;
      delete creditRatingCopy.company.pseudonym;
    }

    const subject = await oplonRiskAPI.post('subjects', oplonRiskModels.subject(creditRatingCopy));

    const folder = await oplonRiskAPI.post(`subjects/${subject.data.id}/dossiers`, {
      user_email,
      dossier_type_name: 'Norgility',
    });

    const analysis = await oplonRiskAPI.post(`dossiers/${folder.data.id}/analyses`, {
      user_email,
      ref_id: mapClientId(clientId),
      analysis_type_name: 'Quick Analysis',
    });

    const analysisId = analysis.data.id;

    await oplonRiskAPI.post(`analyses/${analysisId}/steps/1`, oplonRiskModels.accountStep(creditRatingCopy));
    await oplonRiskAPI.post(`analyses/${analysisId}/steps/1/approve`, { user_email });
    await oplonRiskAPI.post(`analyses/${analysisId}/steps/2`, oplonRiskModels.moreStep(creditRatingCopy));
    await oplonRiskAPI.post(`analyses/${analysisId}/steps/2/approve`, { user_email });
    await oplonRiskAPI.post(`analyses/${analysisId}/steps/3/approve`, { user_email });

    const { data } = await oplonRiskAPI.get(`/analyses/${analysisId}`);
    const pdf = await oplonRiskAPI.get(`/analyses/${analysisId}/pdf?lang=en`, { responseType: 'arraybuffer' });

    const response = {
      rating: data?.final_rating,
      probabilityOfDefault: data?.final_probability_of_default,
      pdf: pdf.data,
    };

    return response;
  } catch (error: any) {
    logger.error({ message: error?.message, error });
    throw new BadRequestError('Credit Rating API is down. Please contact TP Accurate support.');
  }
}

export default createCreditRatingReport;
