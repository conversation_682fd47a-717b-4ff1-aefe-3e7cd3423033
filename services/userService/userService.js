const { contact } = require('../../config/config');
const sendgridAPI = require('../sendgridAPI');
const resetPasswordEmail = require('./resetPasswordEmail');

const sendResetPasswordEmail = ({ toEmail, url }) => {
  return sendgridAPI.post('', {
    personalizations: [
      {
        to: [{ email: toEmail }],
        subject: 'Accurate - Reset password',
      },
    ],
    content: [
      {
        type: 'text/html',
        value: resetPasswordEmail({ url }),
      },
    ],
    from: {
      email: contact.fromEmail,
    },
  });
};

module.exports = { sendResetPasswordEmail };
