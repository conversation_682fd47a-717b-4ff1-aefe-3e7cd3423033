const formatEmail = ({ url }) =>
  `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset password email</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,400;0,500;1,400&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div
      style="
        position: relative;
        max-width: 700px;
        background: #f7fafc;
        border-radius: 4px;
        padding: 48px;
        margin: 0 auto;
      "
    >
      <div style="text-align: center; margin-bottom: 48px">
        <img
          alt="logo"
          src="https://nordadvisorystorage.blob.core.windows.net/production/logo-dark.png?sv=2020-08-04&st=2021-11-10T13%3A29%3A42Z&se=2050-12-31T13%3A29%3A00Z&sr=b&sp=r&sig=bB1DRhtVFzl8VB700BJ%2BFBgp%2FihiTV0qdINQBy4byro%3D"
        />
      </div>
      <div style="text-align: center">
        <a
          href="${url}"
          style="
            font-family: 'Roboto', sans-serif;
            padding: 8px 16px;
            color: #ffffff;
            background-color: #12246c;
            border-radius: 4px;
            font-size: 14px;
            text-decoration: none;
          "
          >RESET PASSWORD</a
        >
        <p
          style="
            font-family: 'Roboto', sans-serif;
            margin-top: 40px;
            margin-bottom: 0;
            color: #12246c;
            border-radius: 4px;
            font-size: 14px;
            text-decoration: none;
          "
        >
          Reset link is valid for 10 minutes
        </p>
      </div>
    </div>
  </body>
</html>
`;

module.exports = formatEmail;
