import { AxiosResponse } from 'axios';

import { InternalServerError, SolverInternalError, BadRequestError } from '../../utils/ErrorHandler';
import logger from '../../utils/logger';
import solverAPI from './solverAPI';
import { CalculateCashPoolBatchReturnType, LoanTypeType, LoanRateTypeType } from '../../types';

export async function findRoot(rateType: LoanRateTypeType, type: LoanTypeType, data: any) {
  try {
    const response = await solverAPI.post(`/${rateType}/${type}`, data);
    return response.data?.root;
  } catch (err) {
    throw new InternalServerError('Something went wrong in solver.');
  }
}

export function calculateRepayments(rateType: LoanRateTypeType, type: LoanTypeType, data: any) {
  return solverAPI.post(`/${rateType}/${type}/payment`, data);
}

export async function calculateCashPoolBatch(data: any): Promise<AxiosResponse<CalculateCashPoolBatchReturnType>> {
  try {
    const response = await solverAPI.post<CalculateCashPoolBatchReturnType>('/cash-pool/batch', data);
    return response;
  } catch (error: any) {
    logger.error({
      message: 'Cash Pool Batch Calculate Error',
      error: new SolverInternalError(error.response.status, 'Cash Pool Batch Calculate Error'),
      payload: { status: error.response.status, data: error.response.data },
    });
    if (error.response.status < 500) {
      throw new BadRequestError(error.response.data.message);
    }
    throw new InternalServerError('Something went wrong in solver.');
  }
}

/**
 * Takes in multiple batch files and combines them into one.
 * Each batch is grouped by the date and the balances are summed.
 * Each batch/cash pool then becomes a participant in the combined batch.
 * See the test for this function for an example.
 */
export function getCombinedCashPoolBatchData(data: any) {
  return solverAPI.post('/cash-pool/combine-batches', data);
}
