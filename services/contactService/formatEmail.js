const getPhoneMessage = (phone) => {
  if (!phone) {
    return 'Phone not provided';
  }
  return `Phone: ${phone}`;
};

const formatEmail = (name, email, company, phone, message) =>
  `<html lang="en">
    <head>
      <meta charset="UTF-8" />
      <meta http-equiv="X-UA-Compatible" content="IE=edge" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Contact form email</title>
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
      <link
        href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,400;0,500;1,400&display=swap"
        rel="stylesheet"
      />
    </head>
    <body>
      <p>Mail sent by: ${email}</p>
      <p>Name: ${name}</p>
      <p>Company: ${company}</p>
      <p>${getPhoneMessage(phone)}</p>
      <p>${message}</p>
    </body>
  </html>`;

module.exports = formatEmail;
