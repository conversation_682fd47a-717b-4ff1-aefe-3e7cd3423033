const axios = require('axios');

const { contact } = require('../../config/config');
const sendgridAPI = require('../sendgridAPI');
const formatEmail = require('./formatEmail');

const { toEmail, fromEmail, recaptchaSecret, recaptchaVerifyUrl } = contact;

const verifyRecaptcha = async (recaptchaResponse) => {
  const { data } = await axios({
    url: recaptchaVerifyUrl,
    method: 'POST',
    params: {
      secret: recaptchaSecret,
      response: recaptchaResponse,
    },
  });

  return data?.success;
};

const sendgridContactFormSendEmail = async ({ name, email, company, phone, message }) => {
  return sendgridAPI.post('', {
    personalizations: [
      {
        to: [{ email: toEmail }],
        subject: `Contact form - ${email}`,
      },
    ],
    content: [
      {
        type: 'text/html',
        value: formatEmail(name, email, company, phone, message),
      },
    ],
    from: {
      email: fromEmail,
    },
  });
};

module.exports = {
  verifyRecaptcha,
  sendgridContactFormSendEmail,
};
