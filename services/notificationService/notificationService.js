const { contact } = require('../../config/config');
const notificationActions = require('../../enums/notificationActions');
const logger = require('../../utils/logger');
const sendgridAPI = require('../sendgridAPI');
const formatMessage = require('./formatEmail');

const sendgridNewNotificationSendEmail = (adminEmail, action, url, note, notifiedByUserEmail, notifiedByFullName) => {
  return sendgridAPI
    .post('', {
      personalizations: [
        {
          to: [{ email: adminEmail }],
          subject: 'Accurate - Action requested',
        },
      ],
      content: [
        {
          type: 'text/html',
          value: formatMessage(
            notificationActions.NOTIFICATION_ACTION_TEXT[action],
            url,
            note,
            notifiedByUserEmail,
            notifiedByFullName,
          ),
        },
      ],
      from: {
        email: contact.fromEmail,
      },
    })
    .catch((error) => {
      logger.error({
        message: 'Sengrid notification mail failed',
        error,
        payload: error.response.data.errors,
      });
      throw error;
    });
};

module.exports = {
  sendgridNewNotificationSendEmail,
};
