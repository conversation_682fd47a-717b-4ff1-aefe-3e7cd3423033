const formatMessage = (action, url, note, notifiedByUserEmail, notifiedByFullName) =>
  `<!DOCTYPE html>
  <html lang="en">
    <head>
      <meta charset="UTF-8" />
      <meta http-equiv="X-UA-Compatible" content="IE=edge" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Notification email</title>
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
      <link
        href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,400;0,500;1,400&display=swap"
        rel="stylesheet"
      />
    </head>
    <body>
      <div style="position: relative; max-width: 700px; background: #f7fafc; border-radius: 4px; padding: 48px; margin: 0 auto;">
      <div style="text-align: center; margin-bottom: 48px">
      <img alt="logo" src="https://nordadvisorystorage.blob.core.windows.net/production/logo-dark.png?sv=2020-08-04&st=2021-11-10T13%3A29%3A42Z&se=2050-12-31T13%3A29%3A00Z&sr=b&sp=r&sig=bB1DRhtVFzl8VB700BJ%2BFBgp%2FihiTV0qdINQBy4byro%3D" />
    </div>
        <div style="text-align: center">
          <span
            style="
              font-weight: 400;
              font-size: 18px;
              line-height: 24px;
              color: #12246c;
              font-family: 'Roboto', sans-serif;
              margin: 0 6px 0 0;
            "
          >
            ${notifiedByFullName}
          </span>
          <a
            href="mailto:${notifiedByUserEmail}"
            style="
              font-weight: 400;
              font-size: 18px;
              line-height: 24px;
              color: #12246c;
              font-family: 'Roboto', sans-serif;
              text-decoration: none;
            "
            >(${notifiedByUserEmail})</a
          >
        </div>
        <h2
          style="
            color: #48a6c9;
            font-size: 18px;
            font-family: 'Roboto', sans-serif;
            margin: 0;
            margin-bottom: 12px;
            text-align: center;
            font-weight: 400;
          "
        >
          ${action}
        </h2>
        <p
          style="
            color: #8797ac;
            font-family: 'Roboto', sans-serif;
            font-style: italic;
            font-weight: normal;
            font-size: 16px;
            line-height: 21px;
            margin: 0;
            max-width: 500px;
            text-align: center;
            margin: 0 auto;
            margin-bottom: 30px;
            font-weight: 400;
          "
        >
          ${note}
        </p>
        <div style="text-align: center">
          <a
            href="${url}"
            style="
              font-family: 'Roboto', sans-serif;
              padding: 8px 16px;
              color: #ffffff;
              background: #12246c;
              border-radius: 4px;
              font-size: 14px;
              text-decoration: none;
            "
            >GO TO SEE THE REPORT</a
          >
        </div>
      </div>
    </body>
  </html>
  `;

module.exports = formatMessage;
