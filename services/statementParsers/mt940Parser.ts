import { mt940Extractor } from './extractors';
import { getReadInterface } from './statementParser.utils';
import { CashPoolStatementBlockType } from '../../types';

export const parse = async (
  path: string | Buffer,
  accountIdMapper: Record<string, number>,
): Promise<Array<CashPoolStatementBlockType>> => {
  const readInterface = getReadInterface(path);

  const bankAccountBlock = [];
  for await (const line of readInterface) {
    bankAccountBlock.push(line.trim());
  }

  const statementDataForDb = mt940Extractor.extract(bankAccountBlock, accountIdMapper);

  return [statementDataForDb];
};
