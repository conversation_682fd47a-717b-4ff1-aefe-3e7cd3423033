export const getCashPoolAccountId = (
  accountIdentification: string,
  accountIdMapper: Record<string, number | undefined>,
) => {
  const cashPoolAccountId = accountIdMapper[accountIdentification];

  if (!cashPoolAccountId) {
    throw new Error(
      `Account identification ${accountIdentification} does not exist in the mapper: ${JSON.stringify(
        accountIdMapper,
      )}`,
    );
  }

  return cashPoolAccountId;
};

export const expectedBlockKeys = [
  'transactionReferenceNumber',
  'cashPoolAccountId',
  'statementNumber',
  'balanceChange',
  'date',
] as const;
