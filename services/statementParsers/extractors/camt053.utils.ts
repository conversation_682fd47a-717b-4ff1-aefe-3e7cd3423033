import { formatISO } from 'date-fns';

import { Camt053Type, Camt053EntryType } from '../../../schemas/statementSchemas';

export const getStatementDate = (camtJson: Camt053Type): string => {
  return formatISO(
    new Date(
      String(
        camtJson.Document.BkToCstmrStmt[0].Stmt[0].Bal.find(
          (bal) => bal.Tp[0].CdOrPrtry[0].Cd[0] === 'CLBD',
        )?.Dt[0].Dt[0].split('T')[0],
      ),
    ),
    { representation: 'date' },
  );
};

export const getValueDate = (entry: Camt053EntryType): string => {
  return formatISO(new Date(entry.ValDt[0].Dt[0]), { representation: 'date' });
};

export const getIban = (camtJson: Camt053Type): string => {
  return camtJson.Document.BkToCstmrStmt[0].Stmt[0].Acct[0].Id[0].IBAN[0];
};

export const getCd = (entry: Camt053EntryType): string => {
  return entry.BkTxCd[0].Domn[0].Fmly[0].Cd[0];
};

export const getAmount = (entry: Camt053EntryType): number => {
  return Number(entry.Amt[0]._);
};

export const getPosition = (entry: Camt053EntryType): 'CRDT' | 'DBIT' => {
  return entry.CdtDbtInd[0];
};

export const getEntries = (camtJson: Camt053Type): Array<Camt053EntryType> => {
  return camtJson.Document.BkToCstmrStmt[0].Stmt[0].Ntry;
};

export const getTransactionReferenceNumber = (camtJson: Camt053Type): string => {
  return camtJson.Document.BkToCstmrStmt[0].GrpHdr[0].MsgId[0];
};

export const getStatementNumber = (camtJson: Camt053Type): string => {
  return camtJson.Document.BkToCstmrStmt[0].Stmt[0].Id[0];
};
