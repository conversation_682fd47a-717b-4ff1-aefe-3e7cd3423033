import _ from 'lodash';

import { getCashPoolAccountId, expectedBlockKeys } from './extractor.utils';
import { CashPoolStatementBlockType, CashPoolStatementPositionType } from '../../../types';

const parseClosingBalance = (
  line: string,
): {
  position: CashPoolStatementPositionType;
  balance: number;
  date: Date;
} => {
  const position = line.charAt(5) as CashPoolStatementPositionType;

  const [year, month, day] = line.substring(6, 12).match(/.{1,2}/g)!;
  const date = new Date(Number(`20${year}`), Number(month) - 1, Number(day));

  const currency = line.match(/[A-Z]{3}/g)![0];
  const balance = Number(line.split(currency)[1].replace(',', '.'));

  return { position, balance, date };
};

const formatSpecification = {
  TRANSACTION_REFERENCE_NUMBER: ':20:',
  ACCOUNT_IDENTIFICATION: ':25:',
  STATEMENT_NUMBER: ':28C:',
  FINAL_CLOSING_BALANCE: ':62F:',
} as const;

export const extract = (
  bankAccountBlock: Array<string>,
  accountIdMapper: Record<string, number | undefined>,
): CashPoolStatementBlockType => {
  const block = {} as CashPoolStatementBlockType;

  for (const line of bankAccountBlock) {
    if (line.includes(formatSpecification.TRANSACTION_REFERENCE_NUMBER)) {
      block.transactionReferenceNumber = line.split(formatSpecification.TRANSACTION_REFERENCE_NUMBER)[1];
    }
    if (line.includes(formatSpecification.ACCOUNT_IDENTIFICATION)) {
      const accountIdentification = line.split(formatSpecification.ACCOUNT_IDENTIFICATION)[1];
      block.cashPoolAccountId = getCashPoolAccountId(accountIdentification, accountIdMapper);
    }
    if (line.includes(formatSpecification.STATEMENT_NUMBER)) {
      block.statementNumber = line.split(formatSpecification.STATEMENT_NUMBER)[1];
    }
    if (line.includes(formatSpecification.FINAL_CLOSING_BALANCE)) {
      const { position, balance, date } = parseClosingBalance(line);

      block.balanceChange = position === 'C' ? balance : -balance;
      block.date = date;
    }
  }

  for (const key of expectedBlockKeys) {
    if (_.isNil(block[key])) throw new Error(`Missing key "${key}" in block: ${JSON.stringify(block)}`);
  }

  return block;
};
