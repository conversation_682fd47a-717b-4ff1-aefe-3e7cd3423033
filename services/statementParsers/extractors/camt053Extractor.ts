/* eslint-disable no-console */
import _ from 'lodash';

import { cashPoolBatchRepository, cashPoolParticipantAccountIdsRepository } from '../../../repositories';
import { camt053Schema, Camt053Type, Camt053EntryType } from '../../../schemas/statementSchemas';
import * as camtExtractorUtils from './camt053.utils';
import { CashPoolStatementBlockType } from '../../../types';
import * as config from '../../../cron/glsStatementCron/helpers/config';
import { isAfter, isBefore, isSameDay } from 'date-fns';

export type BalanceAccumulatorKeyType = `${number}_${string}`;

// This should be added to CashPool model or a separate model
const cdValuesMapper: Record<number, Array<string>> = {
  5000: ['ICDT', 'RCDT', 'ICCN'],
  6000: ['CAPL'],
  7000: ['CAPL'],
  9000: ['ACCB'],
};

const getExternalId = async (
  concatenatedString: string,
  externalIds: Array<{ externalId: string }>,
): Promise<string | null> => {
  for (const { externalId } of externalIds) {
    const accountIdentificationMatch = concatenatedString.match(externalId);
    if (Array.isArray(accountIdentificationMatch)) return accountIdentificationMatch[0];
  }

  return null;
};

const getConcatenatedString = (entry: Camt053EntryType) => {
  let concatenatedString = entry.AddtlNtryInf ? entry.AddtlNtryInf[0] : '';

  if (entry.NtryDtls && entry.NtryDtls[0]?.TxDtls[0]?.RmtInf) {
    const rmtInf = entry.NtryDtls[0]?.TxDtls[0]?.RmtInf;
    if (typeof rmtInf[0] === 'string') {
      concatenatedString += ` ${rmtInf[0]}`;
    } else if (rmtInf[0]?.Ustrd) {
      concatenatedString += ` ${rmtInf[0]?.Ustrd[0]}`;
    }
  }

  return concatenatedString;
};

const updateBalance = (
  balanceAccumulator: Record<BalanceAccumulatorKeyType, number>,
  accountKey: BalanceAccumulatorKeyType,
  balanceChange: number,
) => {
  const previousBalance = balanceAccumulator[accountKey] ?? 0;
  balanceAccumulator[accountKey] = previousBalance + balanceChange;
};

export const extract = async (
  parsedXml: unknown,
  accountIdMapper: Record<string, number | undefined>,
  cashPoolId: number,
): Promise<Array<CashPoolStatementBlockType>> => {
  const leaderAccountExternalId = await cashPoolParticipantAccountIdsRepository.getLeaderParticipantExternalId(
    cashPoolId,
  );

  const latestBatch = await cashPoolBatchRepository.getCashPoolBatch({
    where: { cashPoolId },
    cashPoolId,
    clientId: config.userData.clientId,
    order: [['createdAt', 'DESC']] as any,
  });

  const leaderAccountId = accountIdMapper[leaderAccountExternalId];
  if (!leaderAccountId) throw new Error(`Leader account with external id ${leaderAccountExternalId} not found.`);

  const balanceAccumulator: Record<BalanceAccumulatorKeyType, number> = {};

  const camtJson: Camt053Type = await camt053Schema.parseAsync(parsedXml);

  const externalIds = await cashPoolParticipantAccountIdsRepository.getExternalIdsByCashPoolId(cashPoolId);

  const transactionReferenceNumber = camtExtractorUtils.getTransactionReferenceNumber(camtJson);
  const statementNumber = camtExtractorUtils.getStatementNumber(camtJson);
  const statementDate = camtExtractorUtils.getStatementDate(camtJson);
  const entries = camtExtractorUtils.getEntries(camtJson);

  for (const entry of entries) {
    let valueDate = camtExtractorUtils.getValueDate(entry);

    if (
      latestBatch &&
      (isBefore(new Date(valueDate), new Date(latestBatch.endDate)) ||
        isSameDay(new Date(valueDate), new Date(latestBatch.endDate)) ||
        isAfter(new Date(valueDate), new Date(statementDate)))
    ) {
      valueDate = statementDate;
    }

    const concatenatedString = getConcatenatedString(entry);
    const cd = camtExtractorUtils.getCd(entry);
    const amount = camtExtractorUtils.getAmount(entry);
    const position = camtExtractorUtils.getPosition(entry);
    const balanceChange = position === 'CRDT' ? amount : -amount;
    const externalId = await getExternalId(concatenatedString, externalIds);

    if (!cdValuesMapper[cashPoolId].includes(cd)) {
      updateBalance(balanceAccumulator, `${leaderAccountId}_${valueDate}`, balanceChange);
      continue;
    }

    const accountId = accountIdMapper[externalId as keyof typeof accountIdMapper];
    if (!accountId) {
      // logger.toFile(`Skipped entry: ${entry.AcctSvcrRef[0]}. Concatenated String: ${concatenatedString}`);
      continue;
    }

    updateBalance(balanceAccumulator, `${accountId}_${valueDate}`, balanceChange);
  }

  const blocks: Array<CashPoolStatementBlockType> = [];
  for (const [accountKey, balanceChange] of Object.entries(balanceAccumulator)) {
    const [accountId, valueDate] = accountKey.split('_');

    if (accountId == null) {
      // logger.toFile(`Missing cash pool account id with accountId: ${accountId}`);
      continue;
    }

    const accountIdNum = Number(accountId);
    if (!_.isFinite(accountIdNum)) throw new Error('accountId is not a number.');

    blocks.push({
      cashPoolAccountId: accountIdNum,
      transactionReferenceNumber,
      statementNumber,
      balanceChange,
      date: new Date(valueDate),
      statementDate: new Date(statementDate),
    });
  }

  return blocks;
};
