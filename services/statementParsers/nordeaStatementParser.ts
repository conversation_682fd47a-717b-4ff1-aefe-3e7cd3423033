import { mt940Extractor } from './extractors';
import { getReadInterface } from './statementParser.utils';
import { CashPoolStatementBlockType } from '../../types';

/** Nordea's file covers all bank accounts in one file, while SEB's file covers only one bank account */
export const parse = async (
  path: string | Buffer,
  accountIdMapper: Record<string, number>,
): Promise<Array<CashPoolStatementBlockType>> => {
  const readInterface = getReadInterface(path);

  /**
   * Since Nordea covers all banks this part of the code separates the accounts and
   * saves each as an array of strings so `bankAccountBlocks` in the end contains array of
   * arrays. Each item in the top array is a bank account and each item in inner array
   * is a line from the file.
   */
  const bankAccountBlocks: Array<Array<string>> = [];
  const setupBlock: Array<string> = [];
  let bankAccountBlockStarted = false;
  for await (const line of readInterface) {
    if (line.includes('{4:')) {
      bankAccountBlockStarted = true;
      continue;
    }

    if (line.includes('-}')) {
      bankAccountBlockStarted = false;
      bankAccountBlocks.push([...setupBlock]);
      setupBlock.length = 0;
      continue;
    }

    if (bankAccountBlockStarted) {
      setupBlock.push(line.trim());
      continue;
    }
  }

  const statementsDataForDb: Array<CashPoolStatementBlockType> = bankAccountBlocks.map((bankAccountBlock) =>
    mt940Extractor.extract(bankAccountBlock, accountIdMapper),
  );

  return statementsDataForDb;
};
