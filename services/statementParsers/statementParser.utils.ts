import fs from 'fs';
import readline from 'readline';
import stream from 'stream';
import _ from 'lodash';

import { InternalServerError } from '../../utils/ErrorHandler';

export const getReadInterface = (path: string | Buffer): readline.Interface => {
  if (typeof path === 'string') return readline.createInterface({ input: fs.createReadStream(path) });
  if (path instanceof Buffer) return readline.createInterface({ input: stream.Readable.from(path) });

  throw new InternalServerError('Path must be a string or a buffer');
};
