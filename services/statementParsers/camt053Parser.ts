import fs from 'fs/promises';
import { Parser } from 'xml2js';

import { camt053Extractor } from './extractors';
import { InternalServerError } from '../../utils/ErrorHandler';
import { CashPoolStatementBlockType } from '../../types';

const validCamt053Versions = ['02', '03', '04', '08'];

export const parse = async (
  path: string | Buffer,
  accountIdMapper: Record<string, number>,
  cashPoolId: number,
): Promise<Array<CashPoolStatementBlockType>> => {
  const buffer = path instanceof Buffer ? path : await fs.readFile(path);

  const xmlParser = new Parser();
  const json = await xmlParser.parseStringPromise(buffer);
  const camtVersion = json?.Document['$']?.xmlns?.slice(-2);

  if (!validCamt053Versions.includes(camtVersion)) {
    throw new InternalServerError(`Invalid camt053 version: ${camtVersion}`);
  }

  const statementDataForDb = camt053Extractor.extract(json, accountIdMapper, cashPoolId);

  return statementDataForDb;
};
