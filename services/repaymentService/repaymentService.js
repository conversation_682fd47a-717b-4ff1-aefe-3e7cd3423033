const { getTypes } = require('../../utils/reportUtils');
const { calculateRepayments } = require('../solverService');

/**
 * For Loans last paymentAmount is equal to the last interestPayment + the principal amount (amount)
 * For Guarantees the principal amount is not included in the last payment
 */
const getBulletPayments = (report, repaymentData, reportIdKey, isWhtEnabled) => {
  const payments = repaymentData.paymentSchedule.map(({ interestPayment, paymentDueDate }, index) => ({
    [reportIdKey]: report.id,
    interestRatePerInterestRepaymentFrequency: repaymentData.interestRatePerInterestRepaymentFrequency,
    paymentDueDate,
    isPaid: false,
    interestPayment,
    paymentNumber: index + 1,
    paymentAmount:
      repaymentData.paymentSchedule.length === index + 1 && reportIdKey === 'loanId'
        ? report.amount + interestPayment
        : interestPayment,
  }));

  let whtPayments = [];
  if (isWhtEnabled) {
    whtPayments = repaymentData.whtPaymentSchedule.map(({ paymentAmount }, index) => ({
      loanId: report.id,
      paymentId: null,
      isPaid: false,
      paymentNumber: index + 1,
      paymentAmount,
    }));
  }

  return { payments, whtPayments: whtPayments || null };
};

const getBalloonPayments = (report, repaymentData, isWhtEnabled) => {
  const payments = repaymentData.compoundingSchedule.map(
    ({ compoundedInterest, additionalInterest, compoundingPeriodEndDate }, index) => ({
      loanId: report.id,
      interestRatePerInterestRepaymentFrequency: repaymentData.interestRatePerInterestRepaymentFrequency,
      compoundingPeriodEndDate,
      /* paymentDueDate is same as last compoundingPeriodEndDate, that is maturityDate of loan */
      paymentDueDate: repaymentData.compoundingSchedule.length === index + 1 ? compoundingPeriodEndDate : null,
      isPaid: false,
      compoundedInterest,
      additionalInterest,
      paymentNumber: index + 1,
      paymentAmount: repaymentData.compoundingSchedule.length === index + 1 ? report.amount + compoundedInterest : 0,
    }),
  );

  const whtPayments = [];
  if (isWhtEnabled) {
    whtPayments.push({
      loanId: report.id,
      paymentId: null,
      isPaid: false,
      paymentNumber: 1,
      paymentAmount: repaymentData.whtPayment.paymentAmount,
    });
  }

  return { payments, whtPayments };
};

/**
 * Either `finalInterestRate` or `basisPoints and estimationOfReferenceRate` are present.
 * `finalInterestRate` is for fixed. `basisPoints and estimationOfReferenceRate` are for float
 */
const getPayments = async ({
  report,
  finalInterestRate,
  reportIdKey,
  basisPoints,
  estimationOfReferenceRate,
  whtInterestRate = 0,
  approach = null,
  isWhtEnabled = false,
}) => {
  const [rateType, type] = getTypes(report);
  const { data: repaymentData } = await calculateRepayments(rateType, type.toLowerCase(), {
    issueDate: new Date(report.issueDate).toISOString().split('T')[0],
    maturityDate: new Date(report.maturityDate || report.terminationDate).toISOString().split('T')[0],
    principalAmount: report.amount,
    paymentFrequency: report.paymentFrequency,
    finalInterestRate,
    basisPoints,
    estimationOfReferenceRate,
    isWhtEnabled,
    whtInterestRate: Number(whtInterestRate),
    approach,
  }).catch((err) => {
    throw new Error(err?.response?.data?.message);
  });

  if (type === 'Bullet') {
    const { payments, whtPayments } = getBulletPayments(report, repaymentData, reportIdKey, isWhtEnabled);
    return [repaymentData.totalInterest, payments, whtPayments];
  }

  if (type === 'Balloon') {
    const { payments, whtPayments } = getBalloonPayments(report, repaymentData, isWhtEnabled);
    return [repaymentData.totalInterest, payments, whtPayments];
  }

  throw new Error('Not supported type for a payment schedule');
};

module.exports = { getPayments };
