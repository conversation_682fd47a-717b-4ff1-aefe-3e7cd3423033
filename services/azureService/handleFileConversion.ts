import * as gotenbergService from '../gotenbergService';
import { FileContentTypeEnum } from '../../enums/files';
import { BadRequestError } from '../../utils/ErrorHandler';

const handleFileConversion = async (
  type: 'pdf' | undefined,
  fileBuffer: Buffer,
  reportFile: any,
): Promise<{
  buffer: Buffer;
  contentType: string;
  contentDisposition: string;
}> => {
  const filename = `${reportFile.name}${reportFile.extension}`;
  if (type == null) {
    return {
      buffer: fileBuffer,
      contentType: String(reportFile.mimeType),
      contentDisposition: `attachment; filename="${filename}"`,
    };
  }

  if (type === 'pdf') {
    const response = await gotenbergService.convertOfficeDocumentToPDF(fileBuffer, filename);
    return {
      buffer: response.data as Buffer,
      contentType: FileContentTypeEnum.PDF,
      contentDisposition: `attachment; filename="${reportFile.name}.pdf"`,
    };
  }

  throw new BadRequestError('File type not supported');
};

export default handleFileConversion;
