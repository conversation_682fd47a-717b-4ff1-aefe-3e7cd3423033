import { Express } from 'express';

import {
  b2bLoanFileRepository,
  b2bLoanRepository,
  cashPoolFileRepository,
  cashPoolRepository,
  creditRatingFileRepository,
  creditRatingRepository,
  guaranteeFileRepository,
  guaranteeRepository,
  loanFileRepository,
  loanRepository,
} from '../../repositories';
import { reportEnums } from '../../enums';
import { BadRequestError } from '../../utils/ErrorHandler';
import { LoanType, GuaranteeType, CreditRatingType, CashPoolType, B2BLoanType, ReportValueType } from '../../types';

const { LOAN, B2B_LOAN, GUARANTEE, CREDIT_RATING, CASH_POOL } = reportEnums.REPORT_TYPES;

const getFileGetterAndCreator = (
  reportType: ReportValueType,
): [
  (
    id: number,
    clientId: number,
  ) => Promise<
    LoanType | (B2BLoanType & { dataValues: B2BLoanType }) | GuaranteeType | CreditRatingType | CashPoolType | null
  >,
  (
    file: Express.Multer.File,
    reportId: number,
    { label, newName, status }: { label: string; newName: string; status: string },
  ) => Promise<any>,
] => {
  if (reportType === LOAN) {
    return [loanRepository.getLoan, loanFileRepository.createLoanFile];
  }
  if (reportType === B2B_LOAN) {
    return [b2bLoanRepository.getLoan, b2bLoanFileRepository.createLoanFile];
  }
  if (reportType === GUARANTEE) {
    return [guaranteeRepository.getGuarantee, guaranteeFileRepository.createGuaranteeFile];
  }
  if (reportType === CREDIT_RATING) {
    return [creditRatingRepository.getCreditRating, creditRatingFileRepository.createCreditRatingFile];
  }
  if (reportType === CASH_POOL) {
    return [cashPoolRepository.getCashPoolById, cashPoolFileRepository.createCashPoolFile];
  }

  throw new BadRequestError('Wrong report type.');
};

const getFileGetter = (reportType: ReportValueType) => {
  if (reportType === LOAN) return loanFileRepository.getLoanFile;
  if (reportType === B2B_LOAN) return b2bLoanFileRepository.getLoanFile;
  if (reportType === GUARANTEE) return guaranteeFileRepository.getGuaranteeFile;
  if (reportType === CREDIT_RATING) return creditRatingFileRepository.getCreditRatingFile;
  if (reportType === CASH_POOL) return cashPoolFileRepository.getCashPoolFile;

  throw new BadRequestError('Wrong report type.');
};

const getFileDelete = (reportType: ReportValueType) => {
  if (reportType === LOAN) return loanFileRepository.deleteLoanFile;
  if (reportType === B2B_LOAN) return b2bLoanFileRepository.deleteLoanFile;
  if (reportType === GUARANTEE) return guaranteeFileRepository.deleteGuaranteeFile;
  if (reportType === CREDIT_RATING) return creditRatingFileRepository.deleteCreditRatingFile;
  if (reportType === CASH_POOL) return cashPoolFileRepository.deleteCashPoolFile;

  throw new BadRequestError('Wrong report type.');
};

const getFileUpdate = (reportType: ReportValueType) => {
  if (reportType === LOAN) return loanFileRepository.updateLoanFile;
  if (reportType === B2B_LOAN) return b2bLoanFileRepository.updateLoanFile;
  if (reportType === GUARANTEE) return guaranteeFileRepository.updateGuaranteeFile;
  if (reportType === CREDIT_RATING) return creditRatingFileRepository.updateCreditRatingFile;
  if (reportType === CASH_POOL) return cashPoolFileRepository.updateCashPoolFile;

  throw new BadRequestError('Wrong report type.');
};

export { getFileGetterAndCreator, getFileGetter, getFileDelete, getFileUpdate };
