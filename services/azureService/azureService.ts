import { BlobServiceClient, BlobUploadCommonResponse } from '@azure/storage-blob';
import { Readable } from 'stream';

import { NotFoundError, InternalServerError } from '../../utils/ErrorHandler';

const blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING!);
const containerClient = blobServiceClient.getContainerClient(process.env.CONTAINER_NAME!);

type PrefixStringType =
  | 'cashPool/'
  | 'cashPoolBatch/'
  | 'creditRating/'
  | 'cuftData/'
  | 'guarantee/'
  | 'loan/'
  | 'b2bLoan/'
  | 'template/'
  | 'statement/walwil/nordea/'
  | 'statement/walwil/seb/'
  | `statement/${number}/`
  | 'GunvorCashPoolStructure';

export type FileStorageLocationType = `${PrefixStringType}${string}`;

const getFile = (fileStorageLocation: FileStorageLocationType): Promise<Buffer> => {
  return containerClient.getBlockBlobClient(fileStorageLocation).downloadToBuffer();
};

const uploadFile = async (
  fileStorageLocation: FileStorageLocationType,
  buffer: Buffer,
  size: number,
): Promise<BlobUploadCommonResponse> => {
  const result = await containerClient
    .getBlockBlobClient(fileStorageLocation)
    .uploadStream(Readable.from(buffer, { highWaterMark: size }), size);

  if (result.errorCode) throw new InternalServerError(result.errorCode);

  return result;
};

const deleteFile = async (fileStorageLocation: FileStorageLocationType): Promise<void> => {
  const { succeeded, errorCode } = await containerClient.getBlockBlobClient(fileStorageLocation).deleteIfExists();

  if (errorCode === 'BlobNotFound') throw new NotFoundError('File');

  if (!succeeded) throw new InternalServerError(`Error ${errorCode}`);
};

const getAllFilenamesInFolder = async (prefix: PrefixStringType): Promise<Array<string>> => {
  const blobs = containerClient.listBlobsFlat({ prefix });

  const filenames = [];
  for await (const blob of blobs) {
    if (blob.name.includes(prefix)) {
      filenames.push(blob.name);
    }
  }
  return filenames;
};

export { getFile, uploadFile, deleteFile, getAllFilenamesInFolder };
