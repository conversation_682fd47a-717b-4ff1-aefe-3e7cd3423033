/* eslint-disable no-console */
const chalk = require('chalk');
const redis = require('redis');
const { promisify } = require('util');

const client = redis.createClient({ host: process.env.REDIS_HOST, port: 6379, auth_pass: process.env.REDIS_AUTH });

const shouldLog = process.env.NODE_ENV === 'development';

client.on('connect', () => shouldLog && console.log(chalk.green('====REDIS CONNECTION====\n')));
client.on('close', () => shouldLog && console.log(chalk.red('====REDIS CLOSE====\n')));
client.on('end', () => shouldLog && console.log(chalk.red('====REDIS END====\n')));
client.on('error', (error) => console.error(chalk.red('====REDIS ERROR====\n', error)));

module.exports = {
  get: promisify(client.get).bind(client),
  setex: promisify(client.setex).bind(client),
  keys: promisify(client.keys).bind(client),
  del: promisify(client.del).bind(client),
  quit: promisify(client.quit).bind(client),
};
