import { sheetToJson } from '../../../utils/documents';
import { cuftDataRepository } from '../../../repositories';
import * as cuftDataUtils from './parseAndInsertUtils';
import { CuftDataType, CreditRatingValueType } from '../../../types';

const parseAndInsertCuftData = async (file: Buffer, cuftDataFileId: number): Promise<void> => {
  const rows: Array<Array<string | number | Date>> = sheetToJson(file);
  const headerRow = rows.shift();

  cuftDataUtils.checkHeaderRow(headerRow);

  for (const row of rows) {
    const data: CuftDataType = cuftDataUtils.getDataFromRow(cuftDataFileId, row);

    const { id: cuftDataId } = await cuftDataRepository.createSingleCuftData(data);

    const createCuftDataCurrenciesPromise = cuftDataRepository.createCuftDataCurrencies(
      cuftDataUtils
        .getAllCurrenciesForCurrentRow(String(row[21]), String(row[36]))
        .map((currency) => ({ currency, cuftDataId })),
    );

    const createCuftDataCreditRatingsPromise = cuftDataRepository.createCuftDataCreditRatings(
      cuftDataUtils.getAllCreditRatingsForCurrentRow(row).map((creditRating) => {
        const [[creditRatingNameString, creditRatingValue]] = Object.entries(creditRating);
        const creditRatingName = creditRatingNameString as CreditRatingValueType;
        return { cuftDataId, creditRatingName, creditRatingValue };
      }),
    );

    const createCuftDataSecondaryBorrowersPromise = cuftDataRepository.createCuftDataSecondaryBorrowers(
      cuftDataUtils.splitRowByDivider(String(row[9])).map((name) => ({ cuftDataId, name })),
    );

    const createCuftDataGuarantorNamesPromise = cuftDataRepository.createCuftDataGuarantorNames(
      cuftDataUtils.splitRowByDivider(String(row[10])).map((name) => ({ cuftDataId, name })),
    );

    const createCuftDataLeadArrangersPromise = cuftDataRepository.createCuftDataLeadArrangers(
      cuftDataUtils.splitRowByDivider(String(row[12])).map((name) => ({ cuftDataId, name })),
    );

    await Promise.all([
      createCuftDataCurrenciesPromise,
      createCuftDataCreditRatingsPromise,
      createCuftDataSecondaryBorrowersPromise,
      createCuftDataGuarantorNamesPromise,
      createCuftDataLeadArrangersPromise,
    ]);
  }
};

export default parseAndInsertCuftData;
