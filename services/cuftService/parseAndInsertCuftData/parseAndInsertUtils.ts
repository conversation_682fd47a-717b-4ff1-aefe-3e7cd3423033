import { BadRequestError } from '../../../utils/ErrorHandler';
import { CuftDataType, CuftDataCountryValueType, TrancheAssetClassType, TrancheTypeType } from '../../../types';

export const checkHeaderRow = (headerRow: Array<string | number | Date> | undefined) => {
  const headerRowTitles = [
    'Filing Company Name',
    'CUFT Borrower ID',
    'Filing Form',
    'Filing Form Date',
    'Filing Exhibit Reference Number',
    'CUFT Agreement Type',
    'CUFT Borrower Name',
    'CUFT Borrower Country',
    'Primary SIC',
    'Secondary Borrowers',
    'Guarantors Name(s)',
    'Guarantors Relative Level',
    'Lead Arranger - Lender',
    "Moody's Principal Obilgor Credit Rating",
    "Moody's Principal Obilgor Credit Rating Date",
    'S&P Principal Obilgor Credit Rating',
    'S&P Principal Obilgor Credit Rating Date',
    'CUFT Tranche Execution Date',
    'CUFT Tranche Maturity Date',
    'CUFT Tranche Tenor (yrs)',
    'CUFT Tranche Amount',
    'CUFT Tranche Currency',
    'CUFT Tranche Asset Class',
    'Collateral Type',
    'CUFT Tranche Type',
    'CUFT Tranche Purpose',
    'CUFT Tranche Primary Reference Rate',
    'CUFT Tranche Primary Lending Margin/Fixed Rate',
    'CUFT Tranche Commitment Fee',
    'CUFT Tranche Annual Fee',
    'CUFT Tranche Letter of Credit Fee',
    'CUFT Tranche Utilisation Fee',
    'CUFT Tranche Upfront Fee',
    'Prepayment Fee Description',
    'Termination/Exit Fee Description',
    'Closing Fee/OID Description',
    'Alternative Currencies',
    'Alternative Currencies Description',
    'Sustainability Adjustments Description',
    'PIK Pricing Description',
    'Warrant Description',
    'Conversion Pricing Description',
    'Other Pricing Notes',
    'Credit Rating Rational',
    'Performance Pricing Metric 1 Rational',
    'Performance Pricing Metric 1 Level',
    'Aaa/AAA Margin',
    'Aaa/AAA Commitment Fee',
    'Aaa/AAA Facility Fee',
    'Aaa/AAA All - in Rate',
    'Aa1/AA+ Margin',
    'Aa1/AA+ Commitment Fee',
    'Aa1/AA+ Facility Fee',
    'Aa1/AA+ All - in Rate ',
    'Aa2/AA Margin',
    'Aa2/AA Commitment Fee',
    'Aa2/AA Facility Fee',
    'Aa2/AA All - in Rate',
    'Aa3/AA- Margin',
    'Aa3/AA- Commitment Fee',
    'Aa3/AA- Facility Fee',
    'Aa3/AA- All - in Rate',
    'A1/A+ Margin',
    'A1/A+ Commitment Fee',
    'A1/A+ Facility Fee',
    'A1/A+ All - in Rate',
    'A2/A Margin',
    'A2/A Commitment Fee',
    'A2/A Facility Fee',
    'A2/A All -in Rate',
    'A3/A- Margin',
    'A3/A- Commitment Fee',
    'A3/A- Facility Fee',
    'A3/A- All -in Rate',
    'Baa1/BBB+ Margin',
    'Baa1/BBB+ Commitment Fee',
    'Baa1/BBB+ Facility Fee',
    'Baa1/BBB+ All - in Rate',
    'Baa2/BBB Margin',
    'Baa2/BBB Commitment Fee',
    'Baa2/BBB Facility Fee',
    'Baa2/BBB All - in Rate',
    'Baa3/BBB- Margin',
    'Baa3/BBB- Commitment Fee',
    'Baa3/BBB- Facility Fee',
    'Baa3/BBB- All - in Rate',
    'Ba1/BB+ Margin',
    'Ba1/BB+ Commitment Fee',
    'Ba1/BB+ Facility Fee',
    'Ba1/BB+ All - in Rate',
    'Ba2/BB Margin',
    'Ba2/BB Commitment Fee',
    'Ba2/BB Facility Fee',
    'Ba2/BB All - in Rate',
    'Ba3/BB- Margin',
    'Ba3/BB- Commitment Fee',
    'Ba3/BB- Facility Fee',
    'Ba3/BB- All - in Rate',
    'B1/B+ Margin',
    'B1/B+ Commitment Fee',
    'B1/B+ Facility Fee',
    'B1/B+ All - in Rate',
    'B2/B Margin',
    'B2/B Commitment Fee',
    'B2/B Facility Fee',
    'B2/B All - in Rate',
    'B3/B- Margin',
    'B3/B- Commitment Fee',
    'B3/B- Facility Fee',
    'B3/B- All - in Rate',
    '<B3/B- Margin',
    '<B3/B- Commitment Fee',
    '<B3/B- Facility Fee',
    '<B3/B- All - in Rate',
    'Tranche Order ID',
    'Review Ctrlr ID',
    'Exhibit Link',
    'Delivery Date',
  ];
  if (!headerRow) {
    throw new BadRequestError('Header row is missing.');
  }

  for (let i = 0; i < headerRow.length; i++) {
    if (String(headerRow[i]) !== headerRowTitles[i]) {
      throw new BadRequestError(`Header column ${headerRow[i]} is invalid. Should be ${headerRowTitles[i]}.`);
    }
  }
};

export const getDataFromRow = (cuftDataFileId: number, row: (string | number | Date)[]): CuftDataType => ({
  cuftDataFileId,
  filingCompanyName: String(row[0]),
  cuftBorrowerName: String(row[6]),
  cuftBorrowerCountry: String(row[7]) as CuftDataCountryValueType,
  primarySic: Number(row[8]),
  allCurrencies: `${row[21]} ${String(row[36]).split('; ').join(' ')}`.trim(),
  moodyPrincipalObligorCreditRating: String(row[13]),
  spyPrincipalObligorCreditRating: String(row[15]),
  cuftTrancheExecutionDate: new Date(row[17]),
  cuftTrancheMaturityDate: new Date(row[18]),
  cuftTrancheTenor: Number(row[19]),
  cuftTrancheAssetClass: String(row[22]) as TrancheAssetClassType,
  cuftTrancheType: String(row[24]) as TrancheTypeType,
  cuftTranchePrimaryReferenceRate: String(row[26]),
  trancheOrderID: String(row[114]),
  reviewCtrlrID: String(row[115]),
  exhibitLink: String(row[116]),
  deliveryDate: new Date(row[117]),
  createdAt: new Date(),
  updatedAt: new Date(),
});

export const getAllCurrenciesForCurrentRow = (cuftTrancheCurrency: string, alternativeCurrencies: string) => {
  return `${cuftTrancheCurrency} ${alternativeCurrencies.split('; ').join(' ')}`.trim().split(' ');
};

export const getAllCreditRatingsForCurrentRow = (row: (string | number | Date)[]) => {
  return [
    row[49] != null && String(row[49]).trim() && { 'AAA/Aaa': String(row[49]).trim() },
    row[53] != null && String(row[53]).trim() && { 'AA+/Aa1': String(row[53]).trim() },
    row[57] != null && String(row[57]).trim() && { 'AA/Aa2': String(row[57]).trim() },
    row[61] != null && String(row[61]).trim() && { 'AA-/Aa3': String(row[61]).trim() },
    row[65] != null && String(row[65]).trim() && { 'A+/A1': String(row[65]).trim() },
    row[69] != null && String(row[69]).trim() && { 'A/A2': String(row[69]).trim() },
    row[73] != null && String(row[73]).trim() && { 'A-/A3': String(row[73]).trim() },
    row[77] != null && String(row[77]).trim() && { 'BBB+/Baa1': String(row[77]).trim() },
    row[81] != null && String(row[81]).trim() && { 'BBB/Baa2': String(row[81]).trim() },
    row[85] != null && String(row[85]).trim() && { 'BBB-/Baa3': String(row[85]).trim() },
    row[89] != null && String(row[89]).trim() && { 'BB+/Ba1': String(row[89]).trim() },
    row[93] != null && String(row[93]).trim() && { 'BB/Ba2': String(row[93]).trim() },
    row[97] != null && String(row[97]).trim() && { 'BB-/Ba3': String(row[97]).trim() },
    row[101] != null && String(row[101]).trim() && { 'B+/B1': String(row[101]).trim() },
    row[105] != null && String(row[105]).trim() && { 'B/B2': String(row[105]).trim() },
    row[109] != null && String(row[109]).trim() && { 'B-/B3': String(row[109]).trim() },
    row[113] != null && String(row[113]).trim() && { 'CCC+/Caa1': String(row[113]).trim() },
    row[113] != null && String(row[113]).trim() && { 'CCC/Caa2': String(row[113]).trim() },
    row[113] != null && String(row[113]).trim() && { 'CC/Ca': String(row[113]).trim() },
    row[113] != null && String(row[113]).trim() && { 'C/Ca': String(row[113]).trim() },
  ].filter(Boolean);
};

export const splitRowByDivider = (row: string) => row.split('; ');
