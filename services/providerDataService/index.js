/* eslint-disable no-console */
const { Worker } = require('worker_threads');

async function index() {
  return await new Promise((resolve, reject) => {
    const worker = new Worker('./services/providerDataService/providerDataService.js', { workerData: process.argv });
    worker.on('message', (value) => {
      if (value instanceof Error) reject(value);
      else resolve(value);
    });
    worker.on('error', reject);
    worker.on('exit', (code) => {
      if (code !== 0) reject(new Error(`Worker stopped with exit code ${code}`));
    });
  });
}

index()
  .then((res) => console.log(res))
  .catch((err) => console.log(err));
