const fs = require('fs');
const { unlink, rmdir } = require('fs/promises');
const StreamZip = require('node-stream-zip');
const readline = require('readline');
const { parentPort, workerData } = require('worker_threads');

const { ftp } = require('../../config/config');
const { parseProviderData } = require('../../controllers/workers/providerDataWorker');
const { sequelize } = require('../../models');
const { providerDataFtpClient } = require('./providerDataFtpClient');

const month = workerData[2];
const year = workerData[3];
const zipped = workerData[4] === 'true';

function getFile() {
  const file = zipped ? `${ftp.username}/curves.${month}.csv.zip` : `${ftp.username}/curves.${month}.csv`;
  providerDataFtpClient.get(file, async (err, stream) => {
    if (err) parentPort.postMessage(err);

    stream.once('close', async (err) => {
      if (err) parentPort.postMessage(err);
      await getData();
    });

    stream.pipe(fs.createWriteStream(zipped ? '/datadrive/curves.csv.zip' : '/datadrive/curves.csv'));
  });
}

async function getData() {
  try {
    await sequelize.transaction(async () => {
      if (zipped) {
        const zip = new StreamZip.async({ file: '/datadrive/curves.csv.zip' });
        if (!fs.existsSync('/datadrive/zipContent')) {
          fs.mkdirSync('/datadrive/zipContent');
        }
        await zip.extract(null, '/datadrive/zipContent');
        await zip.close();
      }

      const readInterface = readline.createInterface({
        input: fs.createReadStream(
          zipped ? `/datadrive/zipContent/curves.${month}.${year}.csv` : '/datadrive/curves.csv',
        ),
        console: false,
      });

      await parseProviderData(readInterface);

      if (zipped) {
        await Promise.all([rmdir('/datadrive/zipContent', { recursive: true }), unlink('/datadrive/curves.csv.zip')]);
      } else {
        await unlink('/datadrive/curves.csv');
      }
    });
  } catch (error) {
    parentPort.postMessage(error);
  }
}

getFile();
