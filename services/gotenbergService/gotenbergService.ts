import { AxiosResponse } from 'axios';
import FormData from 'form-data';

import gotenbergAPI from './gotenbergAPI';

export const convertOfficeDocumentToPDF = (fileBuffer: Buffer, filename: string): Promise<AxiosResponse<Buffer>> => {
  const data = new FormData();
  data.append('files', fileBuffer, { filename });

  const config = { maxBodyLength: Infinity, responseType: 'arraybuffer', headers: data.getHeaders() } as const;

  return gotenbergAPI.post('/forms/libreoffice/convert', data, config);
};
