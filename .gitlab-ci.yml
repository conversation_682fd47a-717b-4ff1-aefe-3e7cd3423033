image: node:14.17.1

cache:
  paths:
    - node_modules/

.setup: &setup
  before_script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y  )'
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - |
      if [ "$CI_COMMIT_REF_NAME" == "develop" ]; then
        KEY=$SSH_PRIVATE_KEY
      else
        KEY=$SSH_PRODUCTION_KEY
        SERVER_IP=$SERVER_PRODUCTION_IP
      fi

    - echo "$KEY"
    - echo "$KEY" | tr -d '\r' | ssh-add - > /dev/null
    - echo "$SERVER_IP"

    - ssh-keyscan $SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts

deploy-staging:
  <<: *setup
  script:
    - bash deploy/deploy.sh
  environment:
    name: staging
  only:
    - develop

deploy-production:
  <<: *setup
  script:
    - ssh $SERVER_PRODUCTION_USER@$SERVER_PRODUCTION_IP 'bash /srv/nord/deploy.sh deploy_backend'
  environment:
    name: production
  only:
    - master
