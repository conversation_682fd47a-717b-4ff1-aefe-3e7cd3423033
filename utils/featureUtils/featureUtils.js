const { featureNames, reportEnums } = require('../../enums');
const {
  cashPoolRepository,
  creditRatingRepository,
  featureRepository,
  guaranteeRepository,
  loanRepository,
  b2bLoanRepository,
  userRepository,
} = require('../../repositories');
const { CreateLimitError, InternalServerError } = require('../../utils/ErrorHandler');
const { loanGuaranteeCurrencies } = require('../reportUtils/constants');

const _getReportCountGetter = (reportType) => {
  if (reportType === reportEnums.REPORT_TYPES.LOAN) {
    return [loanRepository.getLoansCount, 'loan'];
  }
  if (reportType === reportEnums.REPORT_TYPES.GUARANTEE) {
    return [guaranteeRepository.getGuaranteesCount, 'guarantee'];
  }
  if (reportType === reportEnums.REPORT_TYPES.CREDIT_RATING) {
    return [creditRatingRepository.getCreditRatingsCount, 'credit rating'];
  }
  if (reportType === reportEnums.REPORT_TYPES.CASH_POOL) {
    return [cashPoolRepository.getCashPoolCount, 'cash pool'];
  }
  if (reportType === reportEnums.REPORT_TYPES.B2B_LOAN) {
    return [b2bLoanRepository.getLoansCount, 'back-to-back loan'];
  }
  if (reportType === 'user') {
    return [userRepository.getUsersCount, 'user'];
  }

  throw new InternalServerError('Wrong report type');
};

/**
 * featureName: loanNumber | backToBackLoanNumber | guaranteeNumber | creditRatingNumber | cashPoolNumber | userNumber (Features table).
 * reportType: loan | b2bLoan | guarantee | creditRating | cashPool | user
 */
async function checkCreateLimits({ clientId, featureName, reportType, incrementCount = 1 }) {
  const [getReportCount, reportName] = _getReportCountGetter(reportType);
  const [clientFeature, numberOfReports] = await Promise.all([
    featureRepository.getClientFeatureByName({ clientId, featureName }),
    getReportCount(clientId),
  ]);

  const maxNumberOfReports = clientFeature.values;
  if (numberOfReports + incrementCount > maxNumberOfReports && clientFeature.isEnabled) {
    throw new CreateLimitError(reportName);
  }
}

async function checkCombinedLoanGuaranteeLimit({ clientId }) {
  const [loanCount, b2bLoanCount, guaranteeCount, clientFeature] = await Promise.all([
    loanRepository.getLoansCount(clientId),
    b2bLoanRepository.getLoansCount(clientId),
    guaranteeRepository.getGuaranteesCount(clientId),
    featureRepository.getClientFeatureByName({ clientId, featureName: featureNames.LOAN_GUARANTEE_NUMBER }),
  ]);

  const maxNumberOfReports = clientFeature.values;
  const combinedNumberOfReports = loanCount + b2bLoanCount + guaranteeCount;
  if (combinedNumberOfReports + 1 > maxNumberOfReports && clientFeature.isEnabled) {
    throw new CreateLimitError('loans and guarantee');
  }
}

/**
 * Checks if the client has access to country specific data or regional data. It depends on what the client paid for.
 * Geography data value can be either basic (regional) or full (country). If the data is not available for some country
 * algorithm falls back to region data.
 */
const checkIsUsingRegionalProviderData = async (clientId) => {
  const geographyData = await featureRepository.getClientFeatureByName({
    clientId,
    featureName: featureNames.GEOGRAPHY_DATA,
  });
  return geographyData.values === 'basic';
};

const getDefaultClientFeatureValues = (featureName) => {
  if (featureName === featureNames.USER_NUMBER) return 20;
  if (featureName === featureNames.LOAN_NUMBER) return 20;
  if (featureName === featureNames.BACK_TO_BACK_LOAN_NUMBER) return 20;
  if (featureName === featureNames.GUARANTEE_NUMBER) return 20;
  if (featureName === featureNames.LOAN_GUARANTEE_NUMBER) return 20;
  if (featureName === featureNames.CREDIT_RATING_NUMBER) return 20;
  if (featureName === featureNames.CASH_POOL_NUMBER) return 20;
  if (featureName === featureNames.GEOGRAPHY_DATA) return 'full';
  if (featureName === featureNames.CURRENCY) return { currencies: loanGuaranteeCurrencies };
  return null;
};

/** Creates default features when a new client is created */
const createDefaultClientFeatures = async (clientId) => {
  const features = await featureRepository.getAllFeatures();

  const clientFeatures = features.map((feature) => ({
    clientId,
    featureId: feature.id,
    isEnabled: true,
    values: getDefaultClientFeatureValues(feature.name),
    createdAt: new Date(),
    updatedAt: new Date(),
  }));

  return featureRepository.createClientFeatures(clientFeatures);
};

module.exports = {
  checkCreateLimits,
  checkCombinedLoanGuaranteeLimit,
  checkIsUsingRegionalProviderData,
  getDefaultClientFeatureValues,
  createDefaultClientFeatures,
};
