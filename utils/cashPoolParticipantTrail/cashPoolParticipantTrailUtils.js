const _ = require('lodash');
const { Op } = require('sequelize');

const getFilterBy = (filterBy) => {
  const START_DATE = 'startDate';
  const END_DATE = 'endDate';
  const COMPANY_IDS = 'companyIds';

  const trailsFilter = {};
  const companyFilter = {};
  const topCurrencyAccountFilter = {};

  const { startDate, endDate } = _.pick(filterBy, [START_DATE, END_DATE]);

  for (const [key, value] of Object.entries(filterBy)) {
    if (!key || !value) {
      continue;
    }

    if (startDate && endDate && (key === START_DATE || key === END_DATE)) {
      trailsFilter['date'] = { [Op.between]: [startDate, endDate] };
      continue;
    }
    if (key === START_DATE) {
      trailsFilter['date'] = { [Op.gte]: new Date(value) };
      continue;
    }
    if (key === END_DATE) {
      trailsFilter['date'] = { [Op.lte]: new Date(value) };
      continue;
    }
    if (key === 'topCurrencyAccountId') {
      topCurrencyAccountFilter['id'] = value;
      continue;
    }
    if (key === COMPANY_IDS && filterBy[COMPANY_IDS]?.length !== 0) {
      companyFilter['id'] = filterBy[COMPANY_IDS];
      continue;
    }
  }

  return [trailsFilter, companyFilter, topCurrencyAccountFilter];
};

module.exports = { getFilterBy };
