import XLSX from 'xlsx';
import { isDate } from 'date-fns';
import _ from 'lodash';

type ColumnNameWithValueType = Record<string, string | number | Date | undefined>;

type JsonToSheetDataType = Array<{
  sheetData: Array<ColumnNameWithValueType> | Array<Array<ColumnNameWithValueType>>;
  sheetName: string;
}>;

const minWidth = 20;
const dateWidth = 10;

const fitToColumn = (data: ColumnNameWithValueType[] | ColumnNameWithValueType[][]) => {
  const columnWidths = [];
  for (const columnName in data[0]) {
    columnWidths.push({
      wch: Math.max(
        columnName ? columnName.toString().length : 0,
        ...data.map((obj: any) => {
          if (!obj[columnName]) return 0;
          if (isDate(obj[columnName])) return dateWidth;
          return obj[columnName].toString().length;
        }),
        minWidth,
      ),
    });
  }
  return columnWidths;
};

const fitToColumnArray = (arrayOfArray: ColumnNameWithValueType[] | ColumnNameWithValueType[][]) => {
  if (!arrayOfArray[0] || !Array.isArray(arrayOfArray[0])) return;

  return arrayOfArray[0].map((_a, i) => ({
    wch: Math.max(
      ...arrayOfArray.map((a2: any) => {
        if (!a2[i]) return 0;
        if (isDate(a2[i])) return dateWidth;
        return a2[i].toString().length;
      }),
      minWidth,
    ),
  }));
};

const formatNumbers = (worksheet: any) => {
  for (const cell of Object.values(worksheet) as any) {
    if (cell.z !== undefined) continue;
    if (cell.t === 'n') {
      if (cell.v > 0.01 || cell.v < -0.01) cell.z = '#,##0.00_);\\(#,##0.00\\)';
      else if (cell.v === 0) cell.z = '#,##0_);';
      else cell.z = '#,##0.0000000_);\\(#,##0.0000000\\)';
    }
  }
};

export function jsonToSheet(data: JsonToSheetDataType): Buffer {
  const workbook = XLSX.utils.book_new();

  for (const { sheetData, sheetName } of data) {
    let worksheet;
    if (Array.isArray(sheetData) && Array.isArray(sheetData[0])) {
      worksheet = XLSX.utils.aoa_to_sheet(sheetData as Array<Array<ColumnNameWithValueType>>);
      worksheet['!cols'] = fitToColumnArray(sheetData);
    } else {
      worksheet = XLSX.utils.json_to_sheet(sheetData);
      worksheet['!cols'] = fitToColumn(sheetData);
    }

    formatNumbers(worksheet);

    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
  }

  return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
}

export function sheetToJson(
  file: Buffer,
  header = 1,
  options = {},
  sheetNum = 0,
): Array<Array<string | number | Date>> {
  const workbook = XLSX.read(file, { cellDates: true, type: 'array' });
  return XLSX.utils.sheet_to_json(workbook.Sheets[workbook.SheetNames[sheetNum]], {
    header,
    blankrows: false,
    ...options,
  });
}

export function mapHeaderToValue(headers: Array<string | number>, values: Array<Array<string | number | Date>>) {
  return values.map((row) => {
    const rowObject: Record<string, string | number | Date> = {};
    headers.forEach((header, index) => {
      rowObject[_.camelCase(header as string)] = row[index] ?? null;
    });
    return rowObject;
  });
}
