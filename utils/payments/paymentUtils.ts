import _ from 'lodash';
import moment from 'moment';
import { Op, literal } from 'sequelize';

import { whtEnums } from '../../enums';
import models from '../../models';
import { roundToXDecimals } from '../numbers';
import { getReportUnit, numberOfMonthsInPeriod } from '../reportUtils';
import {
  FilterType,
  LoanTypeType,
  WHTApproachesType,
  PaymentFrequencyType,
  WHTPayment,
  LoanType,
  LoanGuaranteeReportType,
  PaymentType,
  BulletPaymentType,
  BalloonPaymentType,
  GuaranteePaymentSheetData,
  LoanPaymentSheetData,
} from '../../types';
import { BadRequestError, UnreachableCodeError } from '../ErrorHandler';

const { Guarantee, Loan, BulletPayment } = models;

const loanOrderMapping = {
  lender: { model: Loan, as: 'loan', field: literal("lender->>'name'") },
  borrower: { model: Loan, as: 'loan', field: literal("borrower->>'name'") },
  finalInterestRate: { model: Loan, as: 'loan', field: literal("report->>'finalInterestRate'") },
  currency: { model: Loan, as: 'loan', field: 'currency' },
  amount: { model: Loan, as: 'loan', field: 'amount' },
  issueDate: { model: Loan, as: 'loan', field: 'issueDate' },
  maturityDate: { model: Loan, as: 'loan', field: 'maturityDate' },
  paymentFrequency: { model: Loan, as: 'loan', field: 'paymentFrequency' },
  referenceRate: { model: Loan, as: 'loan', field: literal('"rateType"->>\'referenceRate\'') },
  rateType: { model: Loan, as: 'loan', field: literal('"rateType"->>\'type\'') },
  isPaid: 'isPaid',
  paymentDueDate: 'paymentDueDate',
  paymentAmount: 'paymentAmount',
  paymentNumber: 'paymentNumber',
  interestPayment: { model: BulletPayment, as: 'bulletPayment', field: 'interestPayment' },
  loanType: { model: BulletPayment, as: 'bulletPayment', field: 'id' },
};

const guaranteeOrderMapping = {
  guarantor: { model: Guarantee, as: 'guarantee', field: literal("guarantor->>'name'") },
  principal: { model: Guarantee, as: 'guarantee', field: literal("principal->>'name'") },
  finalInterestRate: { model: Guarantee, as: 'guarantee', field: literal("report->>'finalInterestRate'") },
  currency: { model: Guarantee, as: 'guarantee', field: 'currency' },
  amount: { model: Guarantee, as: 'guarantee', field: 'amount' },
  issueDate: { model: Guarantee, as: 'guarantee', field: 'issueDate' },
  terminationDate: { model: Guarantee, as: 'guarantee', field: 'terminationDate' },
  paymentFrequency: { model: Guarantee, as: 'guarantee', field: 'paymentFrequency' },
  isPaid: 'isPaid',
  paymentDueDate: 'paymentDueDate',
  paymentAmount: 'paymentAmount',
  paymentNumber: 'paymentNumber',
  interestPayment: { model: BulletPayment, as: 'bulletPayment', field: 'interestPayment' },
};

const whtOrderMapping = {
  lender: { model: Loan, as: 'loan', field: literal("lender->>'name'") },
  borrower: { model: Loan, as: 'loan', field: literal("borrower->>'name'") },
  finalInterestRate: { model: Loan, as: 'loan', field: literal("report->>'finalInterestRate'") },
  currency: { model: Loan, as: 'loan', field: 'currency' },
  amount: { model: Loan, as: 'loan', field: 'amount' },
  issueDate: { model: Loan, as: 'loan', field: 'issueDate' },
  maturityDate: { model: Loan, as: 'loan', field: 'maturityDate' },
  paymentFrequency: { model: Loan, as: 'loan', field: 'paymentFrequency' },
  referenceRate: { model: Loan, as: 'loan', field: literal('"rateType"->>\'referenceRate\'') },
  rateType: { model: Loan, as: 'loan', field: literal('"rateType"->>\'type\'') },
  isPaid: 'isPaid',
  paymentDueDate: 'paymentDueDate',
  paymentAmount: 'paymentAmount',
  paymentNumber: 'paymentNumber',
  loanType: { model: Loan, as: 'loan', field: 'type' },
};

const getOrderBy = (orderBy: any, paymentType: string): any[][] | undefined => {
  if (!orderBy) return;

  let orderMapper: any = null;
  if (paymentType === 'loan') orderMapper = loanOrderMapping;
  else if (paymentType === 'guarantee') orderMapper = guaranteeOrderMapping;
  else if (paymentType === 'wht') orderMapper = whtOrderMapping;

  const mapped = orderMapper[orderBy.split('+')[0]];
  const ordering = orderBy.split('+')[1] || 'asc';

  if (!mapped) return;

  if (typeof mapped === 'string') {
    return [[mapped, ordering]];
  }

  return [[{ model: mapped.model, as: mapped.as }, mapped.field, ordering]];
};

const getFilterBy = (
  filterBy: any,
  { isLoan, arePaymentsWht = false }: { isLoan: boolean; arePaymentsWht: boolean },
): [FilterType, FilterType] => {
  const START_DATE = 'startDate';
  const END_DATE = 'endDate';
  const allowedPaymentFilters = ['isPaid', 'paymentAmount', 'loanId', 'guaranteeId', START_DATE, END_DATE];
  const allowedReportFilters = ['currency', 'amount'];
  const allowedLoanFilters = ['lender', 'borrower', 'rateType'];
  const allowedGuaranteeFilters = ['guarantor', 'principal'];

  const paymentFilter: FilterType = {};
  const reportFilter: FilterType = {};

  const startAndEndDate = _.pick(filterBy, [START_DATE, END_DATE]);

  for (const [key, value] of Object.entries(filterBy)) {
    if (!key || !value) {
      continue;
    }

    if (allowedPaymentFilters.includes(key)) {
      if (Object.keys(startAndEndDate).length === 2 && (key === START_DATE || key === END_DATE)) {
        paymentFilter['paymentDueDate'] = { [Op.between]: [startAndEndDate.startDate, startAndEndDate.endDate] };
        continue;
      }
      if (key === START_DATE) {
        paymentFilter['paymentDueDate'] = { [Op.gte]: new Date(value as Date) };
        continue;
      }
      if (key === END_DATE) {
        paymentFilter['paymentDueDate'] = { [Op.lte]: new Date(value as Date) };
        continue;
      }
      if (key === 'paymentAmount') {
        const paymentModel = arePaymentsWht ? 'WHTPayment' : 'Payment';
        paymentFilter[key] = literal(`"${paymentModel}"."paymentAmount"::text LIKE '%${value}%'`);
        continue;
      }
      paymentFilter[key] = value;
      continue;
    }

    if (allowedReportFilters.includes(key)) {
      reportFilter[key] = value;
      continue;
    }

    if (allowedLoanFilters.includes(key) && isLoan) {
      if (key === 'lender' || key === 'borrower') {
        reportFilter[key] = literal(`loan.${key}->>'id'='${value}'`);
        continue;
      }
      if (key === 'rateType') {
        reportFilter[key] = literal(`loan."rateType"->>'type'='${value}'`);
        continue;
      }
    }

    if (allowedGuaranteeFilters.includes(key) && !isLoan) {
      if (key === 'guarantor' || key === 'principal') {
        reportFilter[key] = literal(`guarantee.${key}->>'id'='${value}'`);
        continue;
      }
    }
  }

  return [paymentFilter, reportFilter];
};

const getReportModel = (loanFilter: any, guaranteeFilter: any) => {
  const exclude = ['createdAt', 'calculationLog'];
  return loanFilter
    ? { model: Loan, as: 'loan', attributes: { exclude }, where: loanFilter }
    : { model: Guarantee, as: 'guarantee', attributes: { exclude }, where: guaranteeFilter };
};

const getBasisPointsInPercentage = (basisPoints: number, paymentFrequency: PaymentFrequencyType): number =>
  basisPoints / 100 / paymentFrequencyEnum[paymentFrequency];

const getPaymentAfterPrincipalPayment = (
  loan: any,
  paymentAmount: number,
  principalPaymentDate: Date,
  lastPaidPayment: any,
  /* nextPaymentDueDate in case of payPrincipal and current payment due date when marking float bullet payment as paid */
  nextPaymentDueDate: Date,
  isPrincipalPayment: boolean,
): number => {
  // Ternary here for case when no payment date has passed, then we are between issueDate and first payment
  // To get daysInPeriod we calculate days between issueDate and first payment
  const previousPaymentDate = lastPaidPayment?.paymentDueDate
    ? new Date(lastPaidPayment.paymentDueDate)
    : new Date(loan.issueDate);
  const principalDate = new Date(principalPaymentDate);
  const nextPaymentDate = new Date(nextPaymentDueDate);

  const daysInPeriod = moment(nextPaymentDate).diff(moment(previousPaymentDate), 'days', true);
  const daysAfterLastPeriod = moment(principalDate).diff(moment(previousPaymentDate), 'days', true);
  const daysBeforeNextPeriod = moment(nextPaymentDate).diff(moment(principalDate), 'days', true);

  const percentageOfPeriodBeforePrincipalPayment = daysAfterLastPeriod / daysInPeriod;
  const percentageOfPeriodAfterPrincipalPayment = daysBeforeNextPeriod / daysInPeriod;

  // payment amount of payment after principal payment
  if (isPrincipalPayment)
    return (
      loan.amount * loan.interestRate * percentageOfPeriodBeforePrincipalPayment +
      (loan.amount - Number(paymentAmount)) * loan.interestRate * percentageOfPeriodAfterPrincipalPayment
    );

  return (
    (loan.amount + Number(paymentAmount)) * loan.interestRate * percentageOfPeriodBeforePrincipalPayment +
    loan.amount * loan.interestRate * percentageOfPeriodAfterPrincipalPayment
  );
};

const paymentFrequencyEnum = Object.freeze({
  Monthly: 12,
  Quarterly: 4,
  'Semi-annual': 2,
  Annual: 1,
});

// used twice in payPrincipal, once with and once without the third argument
// getPaymentAfterPrincipalPayment needs the remaining principal because it subtracts paymentAmount
const getRemainingPrincipalAmount = (
  totalPrincipal: number,
  previousPrincipalPayments: any,
  paymentAmount = 0,
): number => {
  const totalPaidPrincipalAmount = previousPrincipalPayments.reduce(
    (previous: any, current: any) => previous + Number(current.paymentAmount),
    0,
  );

  return totalPrincipal - totalPaidPrincipalAmount - paymentAmount;
};

type GetInterestPerYearType = { payment: any; type: LoanTypeType };
type GetInterestPerYearReturnType = { year: number; interest: number };

/**
 * Returns `year` and `interest` depending on type of loan for ExpenseSummary to consume
 */
const getInterestPerYear = ({ payment, type }: GetInterestPerYearType): GetInterestPerYearReturnType => {
  const { paymentDueDate, compoundingPeriodEndDate, interestPayment, compoundedInterest } = payment;
  if (type === 'Bullet') {
    return { year: new Date(paymentDueDate).getFullYear(), interest: interestPayment };
  }

  /**
   * Balloons are only paid at maturity so interest we show on FE is 0. But the interest is
   * compounding every paymentFrequency. That data is the compoundedInterest in BalloonPayments table.
   * We only show the last compoundedInterest as the final interest since that is the interest the user actually pays.
   */
  if (type === 'Balloon') {
    if (moment(paymentDueDate).isSame(moment(compoundingPeriodEndDate), 'day')) {
      return { year: new Date(paymentDueDate).getFullYear(), interest: compoundedInterest };
    }

    return { year: new Date(compoundingPeriodEndDate).getFullYear(), interest: 0 };
  }

  throw new UnreachableCodeError();
};

const getPaymentAmountAndInterestPayment = ({
  loan,
  estimationOfReferenceRate,
  isLastPayment,
  amount,
  rates,
}: {
  loan: any;
  estimationOfReferenceRate: number;
  isLastPayment: boolean;
  amount: number;
  rates: any;
}): [number, number, number] => {
  const basisPointsInPercentage = getBasisPointsInPercentage(loan.report.finalInterestRate, loan.paymentFrequency);
  const interestRate = (estimationOfReferenceRate + basisPointsInPercentage) / 100;
  const [interestPayment, whtPaymentAmount] = calculateWHTPaymentAmount(amount * interestRate, rates);

  if (isLastPayment) {
    return [interestPayment + amount, interestPayment, whtPaymentAmount];
  }

  return [interestPayment, interestPayment, whtPaymentAmount];
};

const getFloatBalloonPartialPeriodPercentage = (
  isLastPayment: boolean,
  previousPaymentCompoundingPeriodEndDate: Date,
  paymentToBeMarkedCompoundingPeriodEndDate: Date,
  paymentFrequency: PaymentFrequencyType,
): number => {
  if (!isLastPayment) return 1;

  const fullPeriodDate = moment(previousPaymentCompoundingPeriodEndDate).add(
    numberOfMonthsInPeriod[paymentFrequency],
    'month',
  );

  const fullPeriodDays = fullPeriodDate.diff(moment(previousPaymentCompoundingPeriodEndDate), 'days');
  const partialPeriodDays = moment(paymentToBeMarkedCompoundingPeriodEndDate).diff(
    moment(previousPaymentCompoundingPeriodEndDate),
    'days',
  );

  return partialPeriodDays / fullPeriodDays;
};

const getBalloonPaymentAmountAndInterestPayment = ({
  loan,
  estimationOfReferenceRate,
  isLastPayment,
  previousPaymentCompoundedInterest,
  percentageOfPeriod,
  rates,
}: {
  loan: LoanType;
  estimationOfReferenceRate: number;
  isLastPayment: boolean;
  previousPaymentCompoundedInterest: number;
  percentageOfPeriod: number;
  rates: LoanGuaranteeReportType;
}): [number, number, number, number] => {
  if (!loan.report.finalInterestRate) {
    throw new BadRequestError('Can not get interest until finalInterestRate is defined');
  }
  const basisPointsInPercentage = getBasisPointsInPercentage(loan.report.finalInterestRate, loan.paymentFrequency);
  const interestRate = (estimationOfReferenceRate + basisPointsInPercentage) / 100;

  const compoundedInterest =
    (loan.amount + previousPaymentCompoundedInterest) * Math.pow(1 + interestRate, percentageOfPeriod) - loan.amount;
  const additionalInterest = compoundedInterest - previousPaymentCompoundedInterest;

  if (isLastPayment) {
    const [newCompoundedInterest, whtPaymentAmount] = calculateWHTPaymentAmount(compoundedInterest, rates);
    return [newCompoundedInterest + Number(loan.amount), additionalInterest, newCompoundedInterest, whtPaymentAmount];
  }

  return [0, additionalInterest, compoundedInterest, 0];
};

const getBulletPaymentAmountAndInterestPayment = ({
  isPreviousPaymentPrincipalPayment,
  loan,
  estimationOfReferenceRate,
  remainingPrincipalAmount,
  previousPayment,
  secondPreviousPayment,
  paymentToBeMarked,
  isLastPayment,
}: {
  isPreviousPaymentPrincipalPayment: boolean;
  loan: LoanType;
  estimationOfReferenceRate: number;
  remainingPrincipalAmount: number;
  previousPayment: PaymentType;
  secondPreviousPayment: PaymentType;
  paymentToBeMarked: PaymentType;
  isLastPayment: boolean;
}): [number, number, number] => {
  // special case when previous the payment was principal payment
  if (isPreviousPaymentPrincipalPayment) {
    if (!loan.report.finalInterestRate) {
      throw new BadRequestError('Can not get payments until finalInterestRate is defined');
    }
    const basisPoints = loan.report.finalInterestRate;
    const interestRate =
      (getBasisPointsInPercentage(basisPoints, loan.paymentFrequency) + estimationOfReferenceRate) / 100;

    const interestPaymentBeforeWHT = getPaymentAfterPrincipalPayment(
      {
        amount: remainingPrincipalAmount,
        interestRate,
        paymentFrequency: loan.paymentFrequency,
        issueDate: loan.issueDate,
      },
      previousPayment.paymentAmount,
      previousPayment.paymentDueDate,
      secondPreviousPayment,
      paymentToBeMarked.paymentDueDate,
      false,
    );

    const [interestPayment, whtPaymentAmount] = calculateWHTPaymentAmount(interestPaymentBeforeWHT, loan.report);

    const paymentAmount = isLastPayment ? remainingPrincipalAmount + interestPayment : interestPayment;
    return [paymentAmount, interestPayment, whtPaymentAmount];
  } else {
    return getPaymentAmountAndInterestPayment({
      loan,
      estimationOfReferenceRate,
      isLastPayment,
      amount: remainingPrincipalAmount,
      rates: loan.report,
    });
  }
};

const getLoanPaymentsSheetData = (payments: any, columns: Array<string>) => {
  return payments.map((p: any) => {
    let result = {} as LoanPaymentSheetData;

    const actualPaymentNumber = p.bulletPayment ? p.paymentNumber : 1;
    const interestPayment = p.bulletPayment?.interestPayment || p.balloonPayment?.compoundedInterest;

    for (const column of columns) {
      switch (column) {
        case 'Lender':
          result['Lender'] = p.loan.lender.name;
          continue;
        case 'Borrower':
          result['Borrower'] = p.loan.borrower.name;
          continue;
        case 'Loan type':
          result['Loan type'] = p.loan.type;
          continue;
        case 'Rate type':
          result['Rate type'] = p.loan.rateType.type;
          continue;
        case 'Currency':
          result['Currency'] = p.loan.currency;
          continue;
        case 'Interest amount':
          result['Interest amount'] = p.paymentAmount ? roundToXDecimals(p.paymentAmount, 2) : '';
          continue;
        case 'Interest due':
          result['Interest due date'] = p.paymentDueDate;
          continue;
        case 'Status':
          result['Status'] = p.isPaid ? 'Published' : 'Unpublished';
          continue;
        case 'Interest number':
          result['Interest number'] = `${actualPaymentNumber} of ${p.dataValues.totalNumberOfPayments}`;
          continue;
        case 'Interest frequency':
          result['Interest frequency'] = p.loan.paymentFrequency;
          continue;
        case 'Issue date':
          result['Issue date'] = p.loan.issueDate;
          continue;
        case 'Maturity date':
          result['Maturity date'] = p.loan.maturityDate;
          continue;
        case 'Rate':
          result['Rate'] = `${p.loan.report.finalInterestRate}${getReportUnit(p.loan.rateType)}`;
          continue;
        case 'Principal amount':
          result['Principal amount'] = p.loan.amount;
          continue;
        case 'Interest':
          result['Interest'] = interestPayment ? roundToXDecimals(interestPayment, 2) : '';
          continue;
        case 'Reference rate':
          result['Reference rate'] = p.loan.rateType.referenceRate || '-';
          continue;
        default:
          continue;
      }
    }

    return result;
  });
};

const getGuaranteePaymentsSheetData = (payments: any, columns: Array<string>) => {
  return payments.map((p: any) => {
    let result = {} as GuaranteePaymentSheetData;

    for (const column of columns) {
      switch (column) {
        case 'Guarantor':
          result['Guarantor'] = p.guarantee.guarantor.name;
          continue;
        case 'Principal':
          result['Principal'] = p.guarantee.principal.name;
          continue;
        case 'Currency':
          result['Currency'] = p.guarantee.currency;
          continue;
        case 'Interest amount':
          result['Interest amount'] = roundToXDecimals(p.paymentAmount, 2);
          continue;
        case 'Interest due':
          result['Interest due date'] = p.paymentDueDate;
          continue;
        case 'Status':
          result['Status'] = p.isPaid ? 'Published' : 'Unpublished';
          continue;
        case 'Interest number':
          result['Interest number'] = `${p.paymentNumber} of ${p.dataValues.totalNumberOfPayments}`;
          continue;
        case 'Fee interest frequency':
          result['Fee interest frequency'] = p.guarantee.paymentFrequency;
          continue;
        case 'Issue date':
          result['Issue date'] = p.guarantee.issueDate;
          continue;
        case 'Termination date':
          result['Termination date'] = p.guarantee.terminationDate;
          continue;
        case 'Rate':
          result['Rate'] = `${p.guarantee.report.finalInterestRate}%`;
          continue;
        case 'Principal amount':
          result['Principal amount'] = p.guarantee.amount;
          continue;
        case 'Guarantee Fee':
          result['Guarantee fee'] = roundToXDecimals(p.bulletPayment?.interestPayment, 2);
          continue;
        default:
          continue;
      }
    }

    return result;
  });
};

/**
 * Calculates wht based on the interest for the year and the wht interest rate.
 * In case of the BORROWER_PAYS approach it also updates the interestPerYear
 * with regards to the calculated wht.
 */
const calculateWhtInterestRateBasedOnApproach = (
  approach: WHTApproachesType,
  whtInterestRate: number,
  interestPerYear: Record<string, number>,
): [Record<string, number>, Record<string, number>] => {
  const whtPerYear: Record<string, number> = {};
  const newInterestPerYearDependentOnWht: Record<string, number> = {};

  for (const [year, interest] of Object.entries(interestPerYear)) {
    const wht = (interest * whtInterestRate) / 100;
    if (approach === whtEnums.WHT_APPROACHES.BORROWER_PAYS) {
      whtPerYear[year] = wht;
      newInterestPerYearDependentOnWht[year] = interest - wht;
    }
    if (approach === whtEnums.WHT_APPROACHES.GROSS_UP) {
      whtPerYear[year] = (wht / (100 - whtInterestRate)) * 100;
      newInterestPerYearDependentOnWht[year] = interest;
    }
  }
  return [whtPerYear, newInterestPerYearDependentOnWht];
};

// If loan type is bullet every wht payment has it matching loan payment.
// If loan type is ballon there is only one wht payments and its matching loan payment is last loan balloon payment
const addPaymentIdsToWHTPayments = (
  createdPayments: BulletPaymentType[] | BalloonPaymentType[],
  whtPayments: WHTPayment[],
  loanType: LoanTypeType,
) => {
  if (loanType === 'Bullet') {
    for (const [index, payment] of createdPayments.entries()) {
      whtPayments[index].paymentId = payment.paymentId;
    }
  } else if (loanType === 'Balloon') {
    whtPayments[0].paymentId = createdPayments[createdPayments.length - 1].paymentId;
  }
};

const calculateWHTPaymentAmount = (interest: number, rates: LoanGuaranteeReportType) => {
  if (rates.isWhtEnabled) {
    if (rates.approach === whtEnums.WHT_APPROACHES.BORROWER_PAYS) {
      const borrowerPaysAdjustedInterest = interest * (1 - rates.whtInterestRate! / 100);
      const whtPaymentAmount =
        borrowerPaysAdjustedInterest / (1 - rates.whtInterestRate! / 100) - borrowerPaysAdjustedInterest;
      return [borrowerPaysAdjustedInterest, whtPaymentAmount];
    } else {
      const whtPaymentAmount = interest / (1 - rates.whtInterestRate! / 100) - interest;
      return [interest, whtPaymentAmount];
    }
  }
  return [interest, 0];
};

export {
  getOrderBy,
  getFilterBy,
  getReportModel,
  getPaymentAfterPrincipalPayment,
  paymentFrequencyEnum,
  getBasisPointsInPercentage,
  getRemainingPrincipalAmount,
  getInterestPerYear,
  getPaymentAmountAndInterestPayment,
  getBalloonPaymentAmountAndInterestPayment,
  getBulletPaymentAmountAndInterestPayment,
  getFloatBalloonPartialPeriodPercentage,
  getLoanPaymentsSheetData,
  getGuaranteePaymentsSheetData,
  calculateWhtInterestRateBasedOnApproach,
  addPaymentIdsToWHTPayments,
  calculateWHTPaymentAmount,
};
