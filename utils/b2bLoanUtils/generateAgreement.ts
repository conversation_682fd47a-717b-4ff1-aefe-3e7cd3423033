import { getActualGeneratedFile, getTemplateFile } from '../templateFilesUtils';
import * as loanUtils from '../loanUtils/loanUtils';
import { TemplateFileLabelsEnum } from '../../enums/templateFiles';
import { B2BLoanWithBorrowerAndLenderType, LoanFileMetadataType } from '../../types';

const { B2B_LOAN_FIXED_AGREEMENT, B2B_LOAN_FLOAT_AGREEMENT } = TemplateFileLabelsEnum;

const getTemplateFilename = (loan: B2BLoanWithBorrowerAndLenderType) => {
  return `Intercompany_Loan_Agreement_(${loan.rateType.type}).docx`;
};

export async function generateAgreement(
  loan: B2BLoanWithBorrowerAndLenderType,
  clientId: number,
  templateData: any,
): Promise<[Buffer, LoanFileMetadataType]> {
  const label = loan.rateType.type === 'fixed' ? B2B_LOAN_FIXED_AGREEMENT : B2B_LOAN_FLOAT_AGREEMENT;

  const dbClientTemplateFileAgreement = await getTemplateFile({
    clientId,
    country: loan.borrower.country,
    companyId: loan.borrower.id,
    label,
  });

  const agreementFilename = getTemplateFilename(loan);

  const agreementFile = await getActualGeneratedFile(dbClientTemplateFileAgreement, templateData, agreementFilename);

  return [
    agreementFile,
    {
      loanId: loan.id,
      label: 'Agreement',
      name: loanUtils.getReportName(loan.borrower.name, loan.currency, loan.amount, 'Back-to-back Loan Agreement'),
      extension: '.docx',
      isGenerated: true,
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    },
  ];
}
