import { B2BLoanType, StandardRemunerationOptionType } from '../../types';

export const calculateLegInterestRates = (data: B2BLoanType, finalInterestRate: number) => {
  const getStandardRemunerationPercentage = (standardRemuneration: StandardRemunerationOptionType): number => {
    if (standardRemuneration?.type === '%') {
      return standardRemuneration.margin;
    }
    if (standardRemuneration?.type === 'basis points') {
      return standardRemuneration.margin;
    }
    if (standardRemuneration?.type === 'Operating cost & Markup') {
      const multiplier = data.rateType.type === 'fixed' ? 100 : 10000;
      const opexAndMarkup = standardRemuneration.operationalCost * (1 + standardRemuneration.markup / 100);

      return (opexAndMarkup / data.amount) * multiplier;
    }

    return 0;
  };

  const multiplier = data.rateType.type === 'fixed' ? 100 : 10000; // 10000 because 100 for decimal to percentage and 100 for percentage to basis points
  const companies = [...data.borrowers].reverse();
  const rates: Array<number> = [Number(finalInterestRate)]; // finalInterestRate is stored as percentage or as basis points
  const riskTakerPercentage = data.capm!.requiredRateOfReturn! * data.expectedLoss!.expectedLoss! * multiplier;

  for (let i = 1; i < companies.length; i++) {
    const previousRate = Number(rates[i - 1]);
    const remunerationPercentage = getStandardRemunerationPercentage(data.standardRemuneration[companies[i].id]);
    if (companies[i].id == data.riskTakerId) {
      rates.push(previousRate - riskTakerPercentage - remunerationPercentage);
    } else {
      rates.push(previousRate - remunerationPercentage);
    }
  }

  return [...rates].reverse();
};
