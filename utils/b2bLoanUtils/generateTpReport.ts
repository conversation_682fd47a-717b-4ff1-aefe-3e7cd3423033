import { getActualGeneratedFile, getTemplateFile } from '../templateFilesUtils';
import * as loanUtils from '../loanUtils/loanUtils';
import { TemplateFileLabelsEnum } from '../../enums/templateFiles';
import { B2BLoanWithBorrowerAndLenderType, LoanFileMetadataType } from '../../types';

const { B2B_LOAN_ADJUSTED_REPORT, B2B_LOAN_STANDALONE_REPORT } = TemplateFileLabelsEnum;

const getTemplateFilename = (loan: B2BLoanWithBorrowerAndLenderType) => {
  return loan.pricingApproach.includes('implicit')
    ? 'Loan_Implicit_Support_Adjusted_(template).docx'
    : 'Loan_Standalone_(template).docx';
};

export async function generateTpReport(
  loan: B2BLoanWithBorrowerAndLenderType,
  clientId: number,
  templateData: any,
): Promise<[Buffer, LoanFileMetadataType]> {
  const label = loan.pricingApproach.includes('implicit') ? B2B_LOAN_ADJUSTED_REPORT : B2B_LOAN_STANDALONE_REPORT;

  const dbClientTemplateFileReport = await getTemplateFile({
    clientId,
    country: loan.borrower.country,
    companyId: loan.borrower.id,
    label,
  });

  const tpReportFilename = getTemplateFilename(loan);

  const tpReportFile = await getActualGeneratedFile(dbClientTemplateFileReport, templateData, tpReportFilename);

  return [
    tpReportFile,
    {
      loanId: loan.id,
      label: 'TP Report',
      name: loanUtils.getReportName(loan.borrower.name, loan.currency, loan.amount, 'Back-to-back Loan Report'),
      extension: '.docx',
      isGenerated: true,
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    },
  ];
}
