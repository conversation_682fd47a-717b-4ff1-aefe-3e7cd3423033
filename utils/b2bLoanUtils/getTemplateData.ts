import { Op, literal } from 'sequelize';

import { getHigherAndLowerCreditRating } from '../../utils/creditRatingUtils';
import { providerCountries } from '../../utils/providerDataUtils';
import { formatDate } from '../dates';
import { roundToXDecimals } from '../numbers';
import { assessmentAnswersTemplateData, countryToRegionMapper } from '../reportUtils';
import { capitalize, groupByThreeCharacters, truncateToTwoDecimals } from '../strings';
import { companyRepository } from '../../repositories';
import { B2BLoanWithBorrowerAndLenderType, EmbeddedCompanyCreditRatingType, CompanyAssessmentType } from '../../types';

function createTemplateData(data: B2BLoanWithBorrowerAndLenderType & { isUsingRegionalProviderData: boolean }) {
  const { lender, borrower, isUsingRegionalProviderData, ...loan } = data;
  const calculationData = loan.calculationLog.data;

  const parsedAmount = groupByThreeCharacters(loan.amount);

  let selectedEntity, selectedEntityPoints;
  if (loan.pricingApproach.includes('non-standard')) {
    selectedEntity = { ...lender, creditRating: { ...lender.creditRating } };
    selectedEntityPoints = calculationData['lender points'];

    // In corner case when pricing approach is implicit non-standard and lender is parent company,
    // use lender credit rating as credit rating adjusted
    if (loan.pricingApproach.includes('implicit') && lender.parentCompanyId === lender.id) {
      selectedEntity.creditRating.ratingAdj = lender.creditRating.rating;
    }
  } else {
    selectedEntity = borrower;
    selectedEntityPoints = calculationData['borrower points'];
  }
  const selectedEntitySurroundingRatings = getHigherAndLowerCreditRating(selectedEntity.creditRating.rating);
  const selectedEntitySurroundingRatingsAdj = getHigherAndLowerCreditRating(selectedEntity.creditRating.ratingAdj);

  return {
    lender: lender.name,
    'lender country': lender.country,
    'lender industry': lender.industry,
    'lender rating': lender.creditRating.rating,
    'lender rating adj': lender.creditRating.ratingAdj ?? lender.creditRating.rating,

    BORROWER: borrower.name.toUpperCase(),
    borrower: borrower.name,
    'borrower country': borrower.country,
    'borrower industry': borrower.industry,
    'borrower rating': borrower.creditRating.rating,
    'borrower rating adj': borrower.creditRating.ratingAdj ?? borrower.creditRating.rating,

    'selected entity': selectedEntity.name,
    'selected entity country': selectedEntity.country,
    'selected entity industry': selectedEntity.industry,

    'selected entity rating': selectedEntity.creditRating.rating,
    'selected rating lower': selectedEntitySurroundingRatings.lowerCreditRating ?? 'n/a',
    'selected rating upper': selectedEntitySurroundingRatings.higherCreditRating ?? 'n/a',

    'selected entity rating adj': selectedEntity.creditRating.ratingAdj ?? 'n/a',
    'selected rating lower adj': selectedEntitySurroundingRatingsAdj.lowerCreditRating ?? 'n/a',
    'selected rating upper adj': selectedEntitySurroundingRatingsAdj.higherCreditRating ?? 'n/a',

    tenor: calculationData?.tenor ? truncateToTwoDecimals(parseFloat(calculationData?.tenor)) : 'n/a',
    'tenor months': calculationData?.tenor ? Math.round((calculationData?.tenor % 1) * 12) : 'n/a',
    'tenor years': calculationData?.tenor ? Math.floor(calculationData?.tenor) : 'n/a',
    'tenor shorter': calculationData['tenor shorter'] ?? 'n/a',
    'tenor longer': calculationData['tenor longer'] ?? 'n/a',

    CURRENCY: loan.currency.toUpperCase(),
    currency: loan.currency,
    'unique id': loan.id,
    'unique id 2': loan.externalId,
    AMOUNT: parsedAmount,
    amount: parsedAmount,
    'issue date': formatDate(loan.issueDate),
    'maturity date': formatDate(loan.maturityDate),
    seniority: loan.seniority,
    'payment frequency': loan.paymentFrequency,
    'reference rate': loan.rateType.type === 'fixed' ? 'n/a' : loan.rateType.referenceRate,
    'reference rate maturity': loan.rateType.type === 'fixed' ? '' : loan.rateType.referenceRateMaturity,
    'loan type': loan.type,
    note: loan.note,

    'interest label': loan.rateType.type === 'fixed' ? '%' : ' bps',
    'interest name': loan.rateType.type === 'fixed' ? 'rate' : 'spread',
    'interest type': capitalize(loan.rateType.type),

    'lender base': roundToXDecimals(calculationData['lender points'].midPoint),
    'borrower base': roundToXDecimals(calculationData['borrower points'].midPoint),

    TL: calculationData['lower points'].midPoint ? roundToXDecimals(calculationData['lower points'].midPoint) : 'n/a',
    SL: calculationData['lower points'].lowerBasePoint
      ? roundToXDecimals(calculationData['lower points'].lowerBasePoint)
      : 'n/a',
    LL: calculationData['lower points'].higherBasePoint
      ? roundToXDecimals(calculationData['lower points'].higherBasePoint)
      : 'n/a',

    TU: calculationData['upper points'].midPoint ? roundToXDecimals(calculationData['upper points'].midPoint) : 'n/a',
    SU: calculationData['upper points'].lowerBasePoint
      ? roundToXDecimals(calculationData['upper points'].lowerBasePoint)
      : 'n/a',
    LU: calculationData['upper points'].higherBasePoint
      ? roundToXDecimals(calculationData['upper points'].higherBasePoint)
      : 'n/a',

    base: selectedEntityPoints.midPoint ? roundToXDecimals(selectedEntityPoints.midPoint) : 'n/a',
    SM: selectedEntityPoints.lowerBasePoint ? roundToXDecimals(selectedEntityPoints.lowerBasePoint) : 'n/a',
    LM: selectedEntityPoints.higherBasePoint ? roundToXDecimals(selectedEntityPoints.higherBasePoint) : 'n/a',

    'upper bound': 'upperBound' in loan.report ? roundToXDecimals(String(loan.report.upperBound)) : 'n/a',
    'lower bound': 'lowerBound' in loan.report ? roundToXDecimals(String(loan.report.lowerBound)) : 'n/a',
    rate: 'finalInterestRate' in loan.report ? roundToXDecimals(String(loan.report.finalInterestRate)) : 'n/a',

    'report date': formatDate(new Date()),
    'data date': formatDate(calculationData?.dataDate),

    'calculation log': loan.calculationLog.log,

    'data geography':
      providerCountries.includes(selectedEntity.country) && !isUsingRegionalProviderData
        ? selectedEntity.country
        : countryToRegionMapper[selectedEntity.country],
    'data geography lender':
      providerCountries.includes(lender.country) && !isUsingRegionalProviderData
        ? lender.country
        : countryToRegionMapper[lender.country],
    'data geography borrower':
      providerCountries.includes(borrower.country) && !isUsingRegionalProviderData
        ? borrower.country
        : countryToRegionMapper[borrower.country],
  };
}

function createTemplateDataImplicit(
  loan: B2BLoanWithBorrowerAndLenderType & { isUsingRegionalProviderData: boolean },
  lenderAssessment: CompanyAssessmentType,
  borrowerAssessment: CompanyAssessmentType,
  parentCompanyRating: EmbeddedCompanyCreditRatingType,
) {
  const templateData = createTemplateData(loan);

  return {
    ...templateData,
    'consolidated group rating': parentCompanyRating.rating,
    'lender assessment': lenderAssessment?.name ?? 'n/a',
    'borrower assessment': borrowerAssessment?.name ?? 'n/a',
    ...assessmentAnswersTemplateData(lenderAssessment?.answers, true),
    ...assessmentAnswersTemplateData(borrowerAssessment?.answers, false),
  };
}

export const getTemplateData = async (
  loan: B2BLoanWithBorrowerAndLenderType,
  clientId: number,
  isUsingRegionalProviderData: boolean,
) => {
  if (loan.pricingApproach.includes('implicit')) {
    const [lenderAssessment, borrowerAssessment, parentCompanyRating] = await Promise.all([
      companyRepository.getCompany({ id: loan.lender.id }, ['assessment']),
      companyRepository.getCompany({ id: loan.borrower.id }, ['assessment']),
      companyRepository.getCompany({ id: { [Op.eq]: literal('"parentCompanyId"') }, clientId }, ['creditRating']),
    ]);
    return createTemplateDataImplicit(
      { ...loan, isUsingRegionalProviderData },
      lenderAssessment.assessment,
      borrowerAssessment.assessment,
      parentCompanyRating.creditRating,
    );
  }

  return createTemplateData({ ...loan, isUsingRegionalProviderData });
};
