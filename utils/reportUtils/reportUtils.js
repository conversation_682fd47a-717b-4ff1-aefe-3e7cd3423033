const { addMonths, isAfter } = require('date-fns');
const moment = require('moment');

const {
  BadRequestError,
  NotFoundError,
  UpdateFinalizedError,
  MoveUneditableError,
} = require('../../utils/ErrorHandler');
const { capitalize } = require('../strings');

const numberOfMonthsInPeriod = {
  Monthly: 1,
  Quarterly: 3,
  'Semi-annual': 6,
  Annual: 12,
};

function hasPoints(report) {
  return 'lowerBound' in report && 'midPoint' in report && 'upperBound' in report;
}

function checkIsImported(report, attributes, isCreditRatingReport) {
  if (isCreditRatingReport) return !attributes;
  return !hasPoints(report);
}

function extractCreditRating({ rating, ratingAdj }, pricingApproach) {
  if (!ratingAdj) return rating;
  else {
    if (pricingApproach.includes('stand-alone')) return rating;
    else return ratingAdj;
  }
}

// assessment answers for the template data
function assessmentAnswersTemplateData(answers, asterisk) {
  const assessmentAnswersTemplateData = {
    [`answer1${asterisk ? '*' : ''}`]: answers ? (answers?.ringFencing ? 'Yes' : 'No') : 'n/a',
  };
  for (let i = 2; i <= 11; i++) {
    assessmentAnswersTemplateData[`answer${i}${asterisk ? '*' : ''}`] = answers
      ? answers[`question${i - 1}`]
        ? 'Yes'
        : 'No'
      : 'n/a';
  }

  return assessmentAnswersTemplateData;
}

// Check if company values changed to run the report algorithm again
function companyValuesChanged(oldCompany, newCompany) {
  return (
    oldCompany.id !== newCompany.id ||
    oldCompany.creditRating.rating !== newCompany.creditRating.rating ||
    oldCompany.creditRating.ratingAdj !== newCompany.creditRating.ratingAdj ||
    oldCompany.industry !== newCompany.industry ||
    oldCompany.country !== newCompany.country
  );
}

function getCalculationLogRedisKey(reportType, reportId) {
  return `${reportType}:${reportId}:calculationLog`;
}

/** Guarantees are always fixed bullet. Guarantee has terminationDate, loans have maturityDate */
function getTypes(report) {
  if (report.terminationDate) {
    return ['fixed', 'Bullet'];
  }

  return [report.rateType.type, report.type];
}

function runReportUpdateGuards(report, reportName) {
  if (!report) {
    throw new NotFoundError(capitalize(reportName));
  }

  if (report.status === 'Final') {
    throw new UpdateFinalizedError(reportName);
  }

  if (!report.editable) {
    throw new MoveUneditableError(reportName);
  }
}

function getReportUnit(rateType) {
  if (rateType) {
    return rateType.type === 'fixed' ? '%' : ' basis points';
  } else {
    return '%';
  }
}

function areRublesDiscontinued(date, currency) {
  const discontinuedDate = moment('2022-03-14');
  return moment(date).isAfter(discontinuedDate, 'day') && currency === 'RUB';
}

function areBRLCOPUnavailable(date, currency) {
  const unavailableBeforeDate = moment('2022-09-22');
  return moment(date).isBefore(unavailableBeforeDate, 'day') && ['BRL', 'COP'].includes(currency);
}

function checkCurrencyProviderDataAvailability(report) {
  if (areRublesDiscontinued(report.issueDate, report.currency)) {
    throw new BadRequestError('RUB data is discontinued from 15 March 2022.');
  }

  if (areBRLCOPUnavailable(report.issueDate, report.currency)) {
    throw new BadRequestError(`${report.currency} data is not available before 22 September 2022.`);
  }
}

/**
 * It takes time for a company to agree on a loan so we want to allow changing the issue date
 * by two months after the initial issue date used when setting up the loan. If clientFeature.isEnabled
 * that means there is a limit on the number of reports and if the updated issue date is more than two
 * months after the original error is thrown. If isEnabled is false error won't be throw since the client
 * doesn't have a limit on a number of reports.
 * New column was added to Loan and Guarantee models `originalIssueDate` for exactly this purpose. Otherwise
 * the issueDate could have been updated for example by month and a half and then again by month and a half.
 */
function issueDateUpdateCheck(oldReport, newReport, clientFeature) {
  const originalIssueDateWithTwoAddedMonths = addMonths(new Date(oldReport.originalIssueDate), 2);
  const isNewIssueDateTwoMonthsOrMoreAfterOriginalIssueDate = isAfter(
    new Date(newReport.issueDate),
    originalIssueDateWithTwoAddedMonths,
  );
  if (clientFeature.isEnabled && isNewIssueDateTwoMonthsOrMoreAfterOriginalIssueDate) {
    throw new BadRequestError('Updated issue date has to be less then two months since the original issue date.');
  }
}

module.exports = {
  numberOfMonthsInPeriod,
  assessmentAnswersTemplateData,
  hasPoints,
  checkIsImported,
  extractCreditRating,
  companyValuesChanged,
  getCalculationLogRedisKey,
  getTypes,
  runReportUpdateGuards,
  getReportUnit,
  checkCurrencyProviderDataAvailability,
  issueDateUpdateCheck,
};
