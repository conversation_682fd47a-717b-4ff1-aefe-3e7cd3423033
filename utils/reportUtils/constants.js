const tenors = [0.25, 0.5, 1, 2, 3, 5, 7, 10, 15, 20, 25, 30];

const tenorFloatToString = {
  0.25: '3M',
  0.5: '6M',
  1: '1Y',
  2: '2Y',
  3: '3Y',
  5: '5Y',
  7: '7Y',
  10: '10Y',
  15: '15Y',
  20: '20Y',
  25: '25Y',
  30: '30Y',
};

const countryToRegionMapper = Object.freeze({
  Afghanistan: 'Asia Ex-Japan',
  'Åland Islands': 'Western Europe',
  Albania: 'Eastern Europe',
  Algeria: 'Africa',
  'American Samoa': 'Oceania',
  Andorra: 'Western Europe',
  Angola: 'Africa',
  Anguilla: 'North America',
  Antarctica: 'North America',
  'Antigua and Barbuda': 'North America',
  Argentina: 'Latin America',
  Armenia: 'Eastern Europe',
  Aruba: 'North America',
  Australia: 'Oceania',
  Austria: 'Western Europe',
  Azerbaijan: 'Eastern Europe',
  Bahamas: 'North America',
  Bahrain: 'Middle East',
  Bangladesh: 'Asia Ex-Japan',
  Barbados: 'North America',
  Belarus: 'Eastern Europe',
  Belgium: 'Western Europe',
  Belize: 'Latin America',
  Benin: 'Middle East',
  Bermuda: 'North America',
  Bhutan: 'Asia Ex-Japan',
  Bolivia: 'Latin America',
  'Bonaire, Sint Eustatius and Saba': 'Latin America',
  'Bosnia and Herz.': 'Eastern Europe',
  Botswana: 'Africa',
  'Bouvet Island': 'Western Europe',
  Brazil: 'Latin America',
  'British Indian Ocean Territory': 'Africa',
  Brunei: 'Asia Ex-Japan',
  Bulgaria: 'Eastern Europe',
  'Burkina Faso': 'Africa',
  Burundi: 'Africa',
  Cambodia: 'Asia Ex-Japan',
  Cameroon: 'Africa',
  Canada: 'North America',
  'Cape Verde': 'Africa',
  'Cayman Islands': 'North America',
  'Central African Rep.': 'Africa',
  Chad: 'Africa',
  Chile: 'Latin America',
  China: 'Asia Ex-Japan',
  'Christmas Island': 'Oceania',
  'Cocos (Keeling) Islands': 'Oceania',
  Colombia: 'Latin America',
  Comoros: 'Africa',
  Congo: 'Africa',
  'Cook Islands': 'Oceania',
  'Costa Rica': 'Latin America',
  "Côte d'Ivoire, Republic of": 'Africa',
  Croatia: 'Eastern Europe',
  Cuba: 'Latin America',
  Curaçao: 'Latin America',
  Cyprus: 'Western Europe',
  'Czech Republic': 'Eastern Europe',
  'Dem. Rep. Congo': 'Africa',
  Denmark: 'Western Europe',
  Djibouti: 'Africa',
  Dominica: 'North America',
  'Dominican Rep.': 'Latin America',
  Ecuador: 'Latin America',
  Egypt: 'Africa',
  'El Salvador': 'Latin America',
  'Eq. Guinea': 'Africa',
  Eritrea: 'Africa',
  Estonia: 'Eastern Europe',
  Ethiopia: 'Africa',
  'Falkland Is.': 'Latin America',
  'Faroe Islands': 'Western Europe',
  Fiji: 'Oceania',
  Finland: 'Western Europe',
  'Fr. S. Antarctic Lands': 'Oceania',
  France: 'Western Europe',
  'French Guiana': 'Latin America',
  'French Polynesia': 'Oceania',
  Gabon: 'Africa',
  Gambia: 'Africa',
  Georgia: 'Eastern Europe',
  Germany: 'Western Europe',
  Ghana: 'Africa',
  Gibraltar: 'Western Europe',
  Greece: 'Western Europe',
  Greenland: 'Western Europe',
  Grenada: 'Latin America',
  Guadeloupe: 'North America',
  Guam: 'Oceania',
  Guatemala: 'Latin America',
  Guernsey: 'Western Europe',
  Guinea: 'Africa',
  'Guinea-Bissau': 'Africa',
  Guyana: 'Latin America',
  Haiti: 'Latin America',
  'Heard Island and McDonald Islands': 'Oceania',
  'Holy See (Vatican City)': 'Western Europe',
  Honduras: 'Latin America',
  'Hong Kong': 'Asia Ex-Japan',
  Hungary: 'Eastern Europe',
  Iceland: 'Western Europe',
  India: 'Asia Ex-Japan',
  Indonesia: 'Asia Ex-Japan',
  Iran: 'Middle East',
  Iraq: 'Middle East',
  Ireland: 'Western Europe',
  'Isle of Man': 'Western Europe',
  Israel: 'Middle East',
  Italy: 'Western Europe',
  Jamaica: 'North America',
  Japan: 'Japan',
  Jersey: 'Western Europe',
  Jordan: 'Middle East',
  Kazakhstan: 'Asia Ex-Japan',
  Kenya: 'Africa',
  Kiribati: 'Oceania',
  Kosovo: 'Eastern Europe',
  Kuwait: 'Middle East',
  Kyrgyzstan: 'Asia Ex-Japan',
  Laos: 'Asia Ex-Japan',
  Latvia: 'Eastern Europe',
  Lebanon: 'Middle East',
  Lesotho: 'Africa',
  Liberia: 'Africa',
  Libya: 'Africa',
  Liechtenstein: 'Western Europe',
  Lithuania: 'Eastern Europe',
  Luxembourg: 'Western Europe',
  Macao: 'Asia Ex-Japan',
  Macedonia: 'Eastern Europe',
  Madagascar: 'Africa',
  Malawi: 'Africa',
  Malaysia: 'Asia Ex-Japan',
  Maldives: 'Asia Ex-Japan',
  Mali: 'Africa',
  Malta: 'Western Europe',
  'Marshall Islands': 'Oceania',
  Martinique: 'North America',
  Mauritania: 'Africa',
  Mauritius: 'Africa',
  Mayotte: 'Africa',
  Mexico: 'Latin America',
  'Micronesia, Federated States of': 'Oceania',
  Moldova: 'Eastern Europe',
  Monaco: 'Western Europe',
  Mongolia: 'Asia Ex-Japan',
  Montenegro: 'Eastern Europe',
  Montserrat: 'North America',
  Morocco: 'Africa',
  Mozambique: 'Africa',
  Myanmar: 'Asia Ex-Japan',
  'N. Cyprus': 'Middle East',
  Namibia: 'Africa',
  Nauru: 'Oceania',
  Nepal: 'Asia Ex-Japan',
  Netherlands: 'Western Europe',
  'New Caledonia': 'Oceania',
  'New Zealand': 'Oceania',
  Nicaragua: 'Latin America',
  Niger: 'Africa',
  Nigeria: 'Africa',
  Niue: 'Oceania',
  'Norfolk Island': 'Oceania',
  'North Korea': 'Asia Ex-Japan',
  'Northern Mariana Islands': 'North America',
  Norway: 'Western Europe',
  Oman: 'Middle East',
  Pakistan: 'Asia Ex-Japan',
  Palau: 'North America',
  Palestine: 'Middle East',
  Panama: 'Latin America',
  'Papua New Guinea': 'Oceania',
  Paraguay: 'Latin America',
  Peru: 'Latin America',
  Philippines: 'Asia Ex-Japan',
  Pitcairn: 'Oceania',
  Poland: 'Eastern Europe',
  Portugal: 'Western Europe',
  'Puerto Rico': 'North America',
  Qatar: 'Middle East',
  Réunion: 'Africa',
  Romania: 'Eastern Europe',
  Russia: 'Eastern Europe',
  Rwanda: 'Africa',
  'S. Sudan': 'Africa',
  'Saint Barthélemy': 'North America',
  'Saint Helena, Ascension and Tristan da Cunha': 'Latin America',
  'Saint Kitts and Nevis': 'North America',
  'Saint Lucia': 'North America',
  'Saint Martin': 'North America',
  'Saint Pierre and Miquelon': 'North America',
  'Saint Vincent and the Grenadines': 'North America',
  Samoa: 'Oceania',
  'San Marino': 'Western Europe',
  'Sao Tome and Principe': 'Africa',
  'Saudi Arabia': 'Middle East',
  Senegal: 'Africa',
  Serbia: 'Eastern Europe',
  Seychelles: 'Africa',
  'Sierra Leone': 'Africa',
  Singapore: 'Asia Ex-Japan',
  'Sint Maarten (Dutch part)': 'Latin America',
  Slovakia: 'Eastern Europe',
  Slovenia: 'Eastern Europe',
  'Solomon Is.': 'Oceania',
  Somalia: 'Africa',
  Somaliland: 'Africa',
  'South Africa': 'Africa',
  'South Georgia and South Sandwich Islands': 'Latin America',
  'South Korea': 'Asia Ex-Japan',
  Spain: 'Western Europe',
  'Sri Lanka': 'Asia Ex-Japan',
  Sudan: 'Africa',
  Suriname: 'Latin America',
  Swaziland: 'Africa',
  Sweden: 'Western Europe',
  Switzerland: 'Western Europe',
  Syria: 'Middle East',
  Taiwan: 'Asia Ex-Japan',
  Tajikistan: 'Asia Ex-Japan',
  Tanzania: 'Africa',
  Thailand: 'Asia Ex-Japan',
  'Timor-Leste': 'Asia Ex-Japan',
  Togo: 'Africa',
  Tokelau: 'Oceania',
  Tonga: 'Oceania',
  'Trinidad and Tobago': 'North America',
  Tunisia: 'Africa',
  Turkey: 'Middle East',
  Turkmenistan: 'Asia Ex-Japan',
  'Turks and Caicos Islands': 'North America',
  Tuvalu: 'Oceania',
  Uganda: 'Africa',
  Ukraine: 'Eastern Europe',
  'United Arab Emirates': 'Middle East',
  'United Kingdom': 'Western Europe',
  'United States Minor Outlying Islands': 'Oceania',
  'United States of America': 'North America',
  Uruguay: 'Latin America',
  Uzbekistan: 'Asia Ex-Japan',
  Vanuatu: 'Oceania',
  Venezuela: 'Latin America',
  Vietnam: 'Asia Ex-Japan',
  'Virgin Islands, British': 'North America',
  'Virgin Islands, U.S.': 'North America',
  'W. Sahara': 'Africa',
  'Wallis and Futuna': 'Oceania',
  Yemen: 'Middle East',
  Zambia: 'Africa',
  Zimbabwe: 'Africa',
});

const countryFlagMapper = {
  "Côte d'Ivoire, Republic of": '🇨🇮',
  Afghanistan: '🇦🇫',
  'Åland Islands': '🇦🇽',
  Albania: '🇦🇱',
  Algeria: '🇩🇿',
  'American Samoa': '🇦🇸',
  Andorra: '🇦🇩',
  Angola: '🇦🇴',
  Anguilla: '🇦🇮',
  Antarctica: '🇦🇶',
  'Antigua and Barbuda': '🇦🇬',
  Argentina: '🇦🇷',
  Armenia: '🇦🇲',
  Aruba: '🇦🇼',
  Australia: '🇦🇺',
  Austria: '🇦🇹',
  Azerbaijan: '🇦🇿',
  Bahamas: '🇧🇸',
  Bahrain: '🇧🇭',
  Bangladesh: '🇧🇩',
  Barbados: '🇧🇧',
  Belarus: '🇧🇾',
  Belgium: '🇧🇪',
  Belize: '🇧🇿',
  Benin: '🇧🇯',
  Bermuda: '🇧🇲',
  Bhutan: '🇧🇹',
  Bolivia: '🇧🇴',
  'Bonaire, Sint Eustatius and Saba': '🇧🇶',
  'Bosnia and Herz.': '🇧🇦',
  Botswana: '🇧🇼',
  'Bouvet Island': '',
  Brazil: '🇧🇷',
  'British Indian Ocean Territory': '🇮🇴',
  Brunei: '🇧🇳',
  Bulgaria: '🇧🇬',
  'Burkina Faso': '🇧🇫',
  Burundi: '🇧🇮',
  Cambodia: '🇰🇭',
  Cameroon: '🇨🇲',
  Canada: '🇨🇦',
  'Cape Verde': '🇨🇻',
  'Cayman Islands': '🇰🇾',
  'Central African Rep.': '🇨🇫',
  Chad: '🇹🇩',
  Chile: '🇨🇱',
  China: '🇨🇳',
  'Christmas Island': '🇨🇽',
  'Cocos (Keeling) Islands': '🇨🇨',
  Colombia: '🇨🇴',
  Comoros: '🇰🇲',
  Congo: '🇨🇨',
  'Cook Islands': '🇨🇰',
  'Costa Rica': '🇨🇷',
  Croatia: '🇭🇷',
  Cuba: '🇨🇺',
  Curaçao: '🇨🇼',
  Cyprus: '🇨🇾',
  'Czech Republic': '🇨🇿',
  'Dem. Rep. Congo': '🇨🇩',
  Denmark: '🇩🇰',
  Djibouti: '🇩🇯',
  Dominica: '🇩🇲',
  'Dominican Rep.': '🇩🇴',
  Ecuador: '🇪🇨',
  Egypt: '🇪🇬',
  'El Salvador': '🇸🇻',
  'Eq. Guinea': '🇬🇶',
  Eritrea: '🇪🇷',
  Estonia: '🇪🇪',
  Ethiopia: '🇪🇹',
  'Falkland Is.': '🇫🇰',
  'Faroe Islands': '🇫🇴',
  Fiji: '🇫🇯',
  Finland: '🇫🇮',
  'Fr. S. Antarctic Lands': '',
  France: '🇫🇷',
  'French Guiana': '🇬🇫',
  'French Polynesia': '🇵🇫',
  Gabon: '🇬🇦',
  Gambia: '🇬🇲',
  Georgia: '🇬🇪',
  Germany: '🇩🇪',
  Ghana: '🇬🇭',
  Gibraltar: '🇬🇮',
  Greece: '🇬🇷',
  Greenland: '🇬🇱',
  Grenada: '🇬🇩',
  Guadeloupe: '🇬🇵',
  Guam: '🇬🇺',
  Guatemala: '🇬🇹',
  Guernsey: '🇬🇬',
  Guinea: '🇬🇳',
  'Guinea-Bissau': '',
  Guyana: '🇬🇾',
  Haiti: '🇭🇹',
  'Heard Island and McDonald Islands': '',
  'Holy See (Vatican City)': '🇻🇦',
  Honduras: '🇭🇳',
  'Hong Kong': '🇭🇰',
  Hungary: '🇭🇺',
  Iceland: '🇮🇸',
  India: '🇮🇳',
  Indonesia: '🇮🇩',
  Iran: '🇮🇷',
  Iraq: '🇮🇶',
  Ireland: '🇮🇪',
  'Isle of Man': '🇮🇲',
  Israel: '🇮🇱',
  Italy: '🇮🇹',
  Jamaica: '🇯🇲',
  Japan: '🇯🇵',
  Jersey: '🇯🇪',
  Jordan: '🇯🇴',
  Kazakhstan: '🇰🇿',
  Kenya: '🇰🇪',
  Kiribati: '🇰🇮',
  Kosovo: '🇽🇰',
  Kuwait: '🇰🇼',
  Kyrgyzstan: '🇰🇬',
  Laos: '🇱🇦',
  Latvia: '🇱🇻',
  Lebanon: '🇱🇧',
  Lesotho: '🇱🇸',
  Liberia: '🇱🇷',
  Libya: '🇱🇾',
  Liechtenstein: '🇱🇮',
  Lithuania: '🇱🇹',
  Luxembourg: '🇱🇺',
  Macao: '🇲🇴',
  Macedonia: '🇲🇰',
  Madagascar: '🇲🇬',
  Malawi: '🇲🇼',
  Malaysia: '🇲🇾',
  Maldives: '🇲🇻',
  Mali: '🇲🇱',
  Malta: '🇲🇹',
  'Marshall Islands': '🇲🇭',
  Martinique: '🇲🇶',
  Mauritania: '🇲🇷',
  Mauritius: '🇲🇺',
  Mayotte: '🇾🇹',
  Mexico: '🇲🇽',
  'Micronesia, Federated States of': '🇫🇲',
  Moldova: '🇲🇩',
  Monaco: '🇲🇨',
  Mongolia: '🇲🇲',
  Montenegro: '🇲🇪',
  Montserrat: '🇲🇸',
  Morocco: '🇲🇦',
  Mozambique: '🇲🇿',
  Myanmar: '🇲🇲',
  'N. Cyprus': '',
  Namibia: '🇳🇦',
  Nauru: '🇳🇷',
  Nepal: '🇳🇵',
  Netherlands: '🇳🇱',
  'New Caledonia': '🇳🇨',
  'New Zealand': '🇳🇿',
  Nicaragua: '🇳🇮',
  Niger: '🇳🇪',
  Nigeria: '🇳🇬',
  Niue: '🇳🇺',
  'Norfolk Island': '🇳🇫',
  'North Korea': '🇰🇵',
  'Northern Mariana Islands': '🇲🇵',
  Norway: '🇳🇴',
  Oman: '🇴🇲',
  Pakistan: '🇵🇰',
  Palau: '🇵🇼',
  Palestine: '🇵🇼',
  Panama: '🇵🇦',
  'Papua New Guinea': '🇵🇬',
  Paraguay: '🇵🇾',
  Peru: '🇵🇪',
  Philippines: '🇵🇭',
  Pitcairn: '🇵🇳',
  Poland: '🇵🇱',
  Portugal: '🇵🇹',
  'Puerto Rico': '🇵🇷',
  Qatar: '🇶🇦',
  Réunion: '🇷🇪',
  Romania: '🇷🇴',
  Russia: '🇷🇺',
  Rwanda: '🇷🇼',
  'S. Sudan': '🇸🇸',
  'Saint Barthélemy': '🇧🇱',
  'Saint Helena, Ascension and Tristan da Cunha': '🇸🇭',
  'Saint Kitts and Nevis': '🇰🇳',
  'Saint Lucia': '🇱🇨',
  'Saint Martin': '',
  'Saint Pierre and Miquelon': '🇵🇲',
  'Saint Vincent and the Grenadines': '🇻🇨',
  Samoa: '🇼🇸',
  'San Marino': '🇸🇲',
  'Sao Tome and Principe': '🇸🇹',
  'Saudi Arabia': '🇸🇦',
  Senegal: '🇸🇳',
  Serbia: '🇷🇸',
  Seychelles: '🇸🇨',
  'Sierra Leone': '🇸🇱',
  Singapore: '🇸🇬',
  'Sint Maarten (Dutch part)': '🇸🇽',
  Slovakia: '🇸🇰',
  Slovenia: '🇸🇮',
  'Solomon Is.': '🇸🇧',
  Somalia: '🇸🇴',
  Somaliland: '',
  'South Africa': '🇿🇦',
  'South Georgia and South Sandwich Islands': '🇬🇸',
  'South Korea': '🇰🇷',
  Spain: '🇪🇸',
  'Sri Lanka': '🇱🇰',
  Sudan: '🇸🇩',
  Suriname: '🇸🇷',
  Swaziland: '',
  Sweden: '🇸🇪',
  Switzerland: '🇨🇭',
  Syria: '🇸🇾',
  Taiwan: '🇹🇼',
  Tajikistan: '🇹🇯',
  Tanzania: '🇹🇿',
  Thailand: '🇹🇭',
  'Timor-Leste': '🇹🇱',
  Togo: '🇹🇬',
  Tokelau: '🇹🇰',
  Tonga: '🇹🇴',
  'Trinidad and Tobago': '🇹🇹',
  Tunisia: '🇹🇳',
  Turkey: '🇹🇷',
  Turkmenistan: '🇹🇲',
  'Turks and Caicos Islands': '🇹🇨',
  Tuvalu: '🇹🇻',
  Uganda: '🇺🇬',
  Ukraine: '🇺🇦',
  'United Arab Emirates': '🇦🇪',
  'United Kingdom': '🇬🇧',
  'United States Minor Outlying Islands': '',
  'United States of America': '🇺🇸',
  Uruguay: '🇺🇾',
  Uzbekistan: '🇺🇿',
  Vanuatu: '🇻🇺',
  Venezuela: '🇻🇪',
  Vietnam: '🇻🇳',
  'Virgin Islands, British': '🇻🇬',
  'Virgin Islands, U.S.': '🇻🇮',
  'W. Sahara': '🇪🇭',
  'Wallis and Futuna': '🇼🇫',
  Yemen: '🇾🇪',
  Zambia: '🇿🇲',
  Zimbabwe: '🇿🇼',
};

/* Used to set default currencies available to clients after they are created */
const loanGuaranteeCurrencies = [
  'USD',
  'EUR',
  'GBP',
  'CHF',
  'AUD',
  'JPY',
  'CAD',
  'CNH',
  'CNY',
  'HKD',
  'INR',
  'IDR',
  'KRW',
  'MYR',
  'PHP',
  'SGD',
  'TWD',
  'THB',
  'DKK',
  'NOK',
  'SEK',
  'CZK',
  'HUF',
  'RON',
  'PLN',
  'RUB',
  'NZD',
  'MXN',
  'PEN',
  'BRL',
  'COP',
  'ILS',
  'ZAR',
  'TRY',
];

module.exports = {
  tenors,
  tenorFloatToString,
  countryToRegionMapper,
  countryFlagMapper,
  loanGuaranteeCurrencies,
};
