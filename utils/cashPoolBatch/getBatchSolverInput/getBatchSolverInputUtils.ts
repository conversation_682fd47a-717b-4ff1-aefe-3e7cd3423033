import _ from 'lodash';

import { BadRequestError } from '../../ErrorHandler';
import { GetCashPoolReturnType, TopCurrencyAccountType, CashPoolParticipantType, CompanyType } from '../../../types';

export type ParticipantDataType = {
  balance: number;
  credit_IR: number;
  debit_IR: number;
  name: number;
  accountId: number;
  overnightRate: number;
};

export type AccountsByNameAndTopCurrencyAccountIdType = Record<
  string,
  {
    accountId: number;
    companyId: number;
    name: string;
    nameWithTopCurrencyAccountId?: string;
    creditInterestRate: number;
    debitInterestRate: number;
    balance?: number;
  }
>;

export const runRowGuards = ({
  date,
  name,
  accountsByName,
  balance,
  overnightRate,
  isFixed,
  lineCounter,
}: {
  date: number | string | Date | null;
  name: string;
  accountsByName: AccountsByNameAndTopCurrencyAccountIdType;
  balance: number | string | Date | null;
  overnightRate: number | string | Date | null;
  isFixed: boolean;
  lineCounter: number;
}) => {
  if (!_.isDate(date)) {
    throw new BadRequestError(`Invalid date '${date}' on line ${lineCounter}`);
  }
  if (!accountsByName[name]) {
    throw new BadRequestError(
      `Invalid participant name '${name}' (or no longer a participant of the cash pool) on line ${lineCounter}`,
    );
  }
  if (!_.isNumber(balance)) {
    throw new BadRequestError(`Invalid balance number on line ${lineCounter}`);
  }
  if (!_.isNumber(overnightRate) && !isFixed) {
    throw new BadRequestError(`Invalid overnight rate number on line ${lineCounter}`);
  }
};

/**
 * With fixed interestType CIR and DIR entered for each participant in cash pool creation is used.
 * Float is different in because each day for which the pooling is done has to have an overnight rate (ex. 2.4%)
 * To get the allIn CIR and DIR we first divide CIR and DIR with 100 because in that case they are basis points.
 * After that to get the final rates we either add to or subtract from the overnight rate.
 *
 * This is also used to get the leader (cash pool) credit and debit rates in getLeaderSolverData.
 */
export const getRates = ({
  isFixed,
  creditInterestRate,
  debitInterestRate,
  overnightRate,
  isLeader,
}: {
  isFixed: boolean;
  creditInterestRate: number;
  debitInterestRate: number;
  overnightRate: number | string | Date | null;
  isLeader: boolean;
}) => {
  if (isFixed) return { creditInterestRate, debitInterestRate };

  if (overnightRate == null) {
    throw new BadRequestError('One or more entries are missing the overnight rate.');
  }
  let allInCreditRate = Number(overnightRate) + creditInterestRate / 100;
  let allInDebitRate = Number(overnightRate) + debitInterestRate / 100;

  /**
   * If allInRates were set to zero if they were negative it would cause a ZeroDivisionError in the solver.
   * Since the following applies `CPP Debit > CPL Debit > CPL Credit > CPP Credit` these hardcoded numbers
   * are used to keep those constraints satisfied while having the minimal impact on the cash pool model.
   */
  if (isLeader) {
    if (allInCreditRate <= 0) allInCreditRate = 0.012;
    if (allInDebitRate <= 0) allInDebitRate = 0.014;
  } else {
    if (allInCreditRate <= 0) allInCreditRate = 0.011;
    if (allInDebitRate <= 0) allInDebitRate = 0.013;
  }
  return { creditInterestRate: allInCreditRate, debitInterestRate: allInDebitRate };
};

/**
 * `topCurrencyAccount` data is used for all cash pool types since in case of Physical and Notional it contains
 * the same data as the CashPool model and for Nordic is contains the data for that specific top currency account.
 * CIR/DIR is sent as a percentage for both fixed and float. It's then changed to a decimal in the solver.
 */
const getLeaderSolverData = ({
  cashPool,
  topCurrencyAccount,
  isFixed,
  cashPoolLeader,
  overnightRate,
}: {
  cashPool: GetCashPoolReturnType;
  topCurrencyAccount: TopCurrencyAccountType;
  isFixed: boolean;
  cashPoolLeader: CashPoolParticipantType & { company: CompanyType };
  overnightRate: number;
}) => {
  const { creditInterestRate, debitInterestRate } = getRates({
    isFixed,
    creditInterestRate: topCurrencyAccount.creditInterestRate,
    debitInterestRate: topCurrencyAccount.debitInterestRate,
    overnightRate,
    isLeader: true,
  });

  return {
    credit_IR: creditInterestRate,
    debit_IR: debitInterestRate,
    markup: cashPool.operatingCostMarkup,
    operating_expenses: cashPool.operatingCost,
    name: cashPoolLeader.company.id,
  };
};

export const getFinalSolverInput = ({
  participantsData,
  cashPool,
  topCurrencyAccount,
  isFixed,
  cashPoolLeader,
}: {
  participantsData: Record<string, Array<ParticipantDataType>>;
  cashPool: GetCashPoolReturnType;
  topCurrencyAccount: TopCurrencyAccountType;
  isFixed: boolean;
  cashPoolLeader: CashPoolParticipantType & { company: CompanyType };
}) => {
  const solverInput = [];
  for (const date in participantsData) {
    // overnight rates should be the same for one date so it doesn't matter which participant we choose (first one in this case)
    const { overnightRate } = participantsData[date][0];
    solverInput.push({
      CPL: getLeaderSolverData({ cashPool, topCurrencyAccount, isFixed, cashPoolLeader, overnightRate }),
      CPP: participantsData[date],
      date,
      totalRisk: cashPool.totalRisk,
    });
  }
  return solverInput;
};
