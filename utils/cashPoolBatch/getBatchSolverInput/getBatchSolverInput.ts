import _, { groupBy } from 'lodash';

import { cashPoolEnums } from '../../../enums';
import {
  getRates,
  getFinalSolverInput,
  runRowGuards,
  ParticipantDataType,
  AccountsByNameAndTopCurrencyAccountIdType,
} from './getBatchSolverInputUtils';
import * as annumUtils from './annumUtils';
import {
  GetCashPoolReturnType,
  TopCurrencyAccountType,
  GetCashPoolParticipantAccountsType,
  CashPoolParticipantType,
  CompanyType,
} from '../../../types';

import * as azureService from '../../../services/azureService';
import { mapHeaderToValue, sheetToJson } from '../../../utils/documents';
import { parse } from 'date-fns';
const { PHYSICAL, NOTIONAL, NORDIC } = cashPoolEnums.cashPoolTypes;

type GetBatchSolverInputType = {
  cashPool: GetCashPoolReturnType;
  topCurrencyAccount: TopCurrencyAccountType;
  accounts: Array<GetCashPoolParticipantAccountsType>;
  batchFileInJson: Array<Array<string | number | Date>>;
  cashPoolLeader: CashPoolParticipantType & { company: CompanyType };
};

type GetBatchSolverInputGunvorType = {
  cashPool: GetCashPoolReturnType;
  topCurrencyAccount: TopCurrencyAccountType;
  accounts: Array<GetCashPoolParticipantAccountsType>;
  batchFileInJson: Array<Record<string, string | number | Date>>;
  cashPoolLeader: CashPoolParticipantType & { company: CompanyType };
  dateRange: Array<string>;
};

const getPhysicalBatchSolverInput = ({
  cashPool,
  topCurrencyAccount,
  accounts,
  batchFileInJson,
  cashPoolLeader,
}: GetBatchSolverInputType) => {
  const isFixed = topCurrencyAccount.interestType === 'fixed';

  const annumDivider = annumUtils.getAnnumDivider(String(cashPool.currencies));
  annumUtils.mutateRatesByDividingWithAnnum(annumDivider, cashPool, topCurrencyAccount);

  /**
   * Example of accountsByName which allows direct access to the object by name for CIR/DIR and companyId
   * since that is the data that's in the uploaded excel (row[1]), this allows not using .find by name for each row
   * {
   *    'Green Tree': { companyId: 26, accountId: 15, name: 'Green Tree', creditInterestRate: 3, debitInterestRate: 9 },
   *    'Cheese Tree': { companyId: 31, accountId: 16, name: 'Cheese Tree', creditInterestRate: 3.3, debitInterestRate: 9.9 }
   * }
   */
  const accountsByName: AccountsByNameAndTopCurrencyAccountIdType = _.keyBy(
    accounts.map((account: any) => ({
      accountId: account.id,
      companyId: account.participant.company.id,
      name: account.participant.company.name,
      creditInterestRate: account.creditInterestRate / annumDivider,
      debitInterestRate: account.debitInterestRate / annumDivider,
    })),
    'name',
  );

  const participantsData: Record<string, Array<ParticipantDataType>> = {};
  let lineCounter = 1;

  for (const row of batchFileInJson) {
    const [date, name, balance, overnightRate] = row;
    if (!participantsData[String(date)]) participantsData[String(date)] = [];

    runRowGuards({ date, name: String(name), accountsByName, balance, overnightRate, isFixed, lineCounter });

    const overnightRateAnnum = Number(overnightRate) / annumDivider;

    const { creditInterestRate, debitInterestRate } = getRates({
      isFixed,
      creditInterestRate: accountsByName[String(name)].creditInterestRate,
      debitInterestRate: accountsByName[String(name)].debitInterestRate,
      overnightRate: overnightRateAnnum,
      isLeader: false,
    });

    participantsData[String(date)].push({
      balance: Number(balance),
      credit_IR: creditInterestRate,
      debit_IR: debitInterestRate,
      name: accountsByName[String(name)].companyId,
      accountId: accountsByName[String(name)].accountId,
      overnightRate: overnightRateAnnum,
    });
    lineCounter++;
  }

  /** Final data format is the same as in the batchSolverInputData  */
  return getFinalSolverInput({ participantsData, cashPool, topCurrencyAccount, isFixed, cashPoolLeader });
};

// async function getGunvorReferenceFile() {
//   const referenceFile = await azureService.getFile('GunvorCashPoolStructure');

//   const jsonSheet = sheetToJson(referenceFile, 1, { defval: null }, 1);

//   const headers = jsonSheet[0];
//   jsonSheet.shift();

//   return mapHeaderToValue(headers as Array<string | number>, jsonSheet);
// }

export const getPhysicalBatchSolverInputGunvor = async ({
  cashPool,
  topCurrencyAccount,
  accounts,
  batchFileInJson,
  cashPoolLeader,
  dateRange,
}: GetBatchSolverInputGunvorType) => {
  const isFixed = topCurrencyAccount.interestType === 'fixed';

  const annumDivider = annumUtils.getAnnumDivider(String(cashPool.currencies));
  annumUtils.mutateRatesByDividingWithAnnum(annumDivider, cashPool, topCurrencyAccount);

  const accountsMap = accounts.map((account: any) => ({
    accountId: account.id,
    companyId: account.participant.company.id,
    name: account.participant.company.name,
    creditInterestRate: account.creditInterestRate / annumDivider,
    debitInterestRate: account.debitInterestRate / annumDivider,
    balance: account.balance ?? 0,
    externalIds: account.externalIds,
  }));

  // const referenceFile = await getGunvorReferenceFile();

  const participantsData: Record<string, Array<ParticipantDataType>> = {};

  const groupedTransactions = groupBy(Object.values(batchFileInJson), 'valueDate');

  for (const date of dateRange) {
    const parsedDate = parse(String(date), 'dd/MM/yyyy', new Date());
    const transactions = groupedTransactions[date];

    if (!participantsData[String(parsedDate)]) participantsData[String(parsedDate)] = [];

    const allAccounts = {};

    accountsMap.forEach((acc) => {
      Object.assign(allAccounts, {
        [acc.accountId]: {
          balance: Number(acc.balance),
          credit_IR: acc.creditInterestRate,
          debit_IR: acc.debitInterestRate,
          name: acc.companyId,
          accountId: acc.accountId,
          overnightRate: 0,
        },
      });
    });

    const hasMatchedTransactions =
      transactions &&
      transactions.some((transaction) => {
        const allExternalIds = new Set(
          accountsMap.flatMap((account) => (account.externalIds || []).map((ext: any) => ext.externalId)),
        );

        return allExternalIds.has(transaction.account as string);
      });

    if (accountsMap.every((acc) => acc.balance === 0) && !hasMatchedTransactions) {
      delete participantsData[String(parsedDate)];
      continue;
    }

    if (!Object.keys(groupedTransactions).includes(date)) {
      Object.assign(participantsData, { [String(parsedDate)]: Object.values(allAccounts) });
      continue;
    }

    for (const transaction of transactions) {
      // const entityReference = _.find(referenceFile, (ref) => ref['kyribaIcAccountAccountCode'] === transaction.account);
      const accountIndex = accountsMap.findIndex((acc) => {
        return (acc.externalIds || []).some((externalId: any) => externalId.externalId === transaction.account);
      });

      // if (!entityReference || accountIndex < 0) {
      //   continue;
      // }
      if (accountIndex < 0) {
        continue;
      }
      const modifier = String(transaction.accountCurrency)[0] === '+' ? 1 : -1;

      accountsMap[accountIndex].balance =
        Number(accountsMap[accountIndex].balance) + modifier * Number(transaction.accountAmount);
      const overnightRate = null;

      const overnightRateAnnum = Number(overnightRate) / annumDivider;

      const { creditInterestRate, debitInterestRate } = getRates({
        isFixed,
        creditInterestRate: accountsMap[accountIndex].creditInterestRate,
        debitInterestRate: accountsMap[accountIndex].debitInterestRate,
        overnightRate: overnightRateAnnum,
        isLeader: false,
      });

      Object.assign(allAccounts, {
        [accountsMap[accountIndex].accountId]: {
          balance: Number(accountsMap[accountIndex].balance),
          credit_IR: creditInterestRate,
          debit_IR: debitInterestRate,
          name: accountsMap[accountIndex].companyId,
          accountId: accountsMap[accountIndex].accountId,
          overnightRate: overnightRateAnnum,
        },
      });
    }

    Object.assign(participantsData, { [String(parsedDate)]: Object.values(allAccounts) });
  }

  /** Final data format is the same as in the batchSolverInputData  */
  return getFinalSolverInput({ participantsData, cashPool, topCurrencyAccount, isFixed, cashPoolLeader });
};

const getNordicBatchSolverInput = ({
  cashPool,
  topCurrencyAccount,
  accounts,
  batchFileInJson,
  cashPoolLeader,
}: GetBatchSolverInputType) => {
  const isFixed = topCurrencyAccount.interestType === 'fixed';

  const annumDivider = annumUtils.getAnnumDivider(String(cashPool.currencies));
  annumUtils.mutateRatesByDividingWithAnnum(annumDivider, cashPool, topCurrencyAccount);

  const accountsByNameWithTopCurrencyAccountId: AccountsByNameAndTopCurrencyAccountIdType = _.keyBy(
    accounts.map((account: any) => ({
      accountId: account.id,
      companyId: account.participant.company.id,
      name: account.participant.company.name,
      nameWithTopCurrencyAccountId: `${account.participant.company.name}---${topCurrencyAccount.id}`,
      creditInterestRate: account.creditInterestRate / annumDivider,
      debitInterestRate: account.debitInterestRate / annumDivider,
    })),
    'nameWithTopCurrencyAccountId',
  );

  const participantsData: Record<string, Array<ParticipantDataType>> = {};
  let lineCounter = 1;
  for (const row of batchFileInJson) {
    const [date, name, balance, overnightRate, topCurrencyAccountId] = row;
    const nameWithTopCurrencyAccountId = `${name}---${topCurrencyAccountId}`;
    if (!participantsData[String(date)]) participantsData[String(date)] = [];

    runRowGuards({
      date,
      name: nameWithTopCurrencyAccountId,
      accountsByName: accountsByNameWithTopCurrencyAccountId,
      balance,
      overnightRate,
      isFixed,
      lineCounter,
    });

    const overnightRateAnnum = Number(overnightRate) / annumDivider;

    const { creditInterestRate, debitInterestRate } = getRates({
      isFixed,
      creditInterestRate: accountsByNameWithTopCurrencyAccountId[nameWithTopCurrencyAccountId].creditInterestRate,
      debitInterestRate: accountsByNameWithTopCurrencyAccountId[nameWithTopCurrencyAccountId].debitInterestRate,
      overnightRate: overnightRateAnnum,
      isLeader: false,
    });

    participantsData[String(date)].push({
      balance: Number(balance),
      credit_IR: creditInterestRate,
      debit_IR: debitInterestRate,
      name: accountsByNameWithTopCurrencyAccountId[nameWithTopCurrencyAccountId].companyId,
      accountId: accountsByNameWithTopCurrencyAccountId[nameWithTopCurrencyAccountId].accountId,
      overnightRate: overnightRateAnnum,
    });
    lineCounter++;
  }

  return getFinalSolverInput({ participantsData, cashPool, topCurrencyAccount, isFixed, cashPoolLeader });
};

const getBatchSolverInput = ({ cashPool, ...rest }: GetBatchSolverInputType) => {
  if (cashPool.type === PHYSICAL || cashPool.type === NOTIONAL)
    return getPhysicalBatchSolverInput({ cashPool, ...rest });
  if (cashPool.type === NORDIC) return getNordicBatchSolverInput({ cashPool, ...rest });
};

export default getBatchSolverInput;
