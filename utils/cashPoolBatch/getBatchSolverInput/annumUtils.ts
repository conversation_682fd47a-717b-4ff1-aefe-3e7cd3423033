import { GetCashPoolReturnType, TopCurrencyAccountType } from '../../../types';

export const getAnnumDivider = (currency: string) => {
  const defaultDivider = 360;
  const currencyMapper = { GBP: 365 };

  return currencyMapper[currency as keyof typeof currencyMapper] ?? defaultDivider;
};

export const mutateRatesByDividingWithAnnum = (
  annumDivider: number,
  cashPool: GetCashPoolReturnType,
  topCurrencyAccount: TopCurrencyAccountType,
): void => {
  cashPool.creditInterestRate! /= annumDivider;
  cashPool.debitInterestRate! /= annumDivider;

  topCurrencyAccount.creditInterestRate /= annumDivider;
  topCurrencyAccount.debitInterestRate /= annumDivider;
};
