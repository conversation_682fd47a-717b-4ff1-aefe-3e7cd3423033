import { literal } from 'sequelize';

import models from '../../models';
import * as statementUtils from '../../utils/statementUtils';

const { CashPoolParticipantAccounts, Cash_Pool_Participants, Company } = models;

const statementDataMapping = (attribute: string, direction: 'ASC' | 'DESC') => {
  if (attribute === 'date') return [[attribute, direction]];
  if (attribute === 'statementDate') return [[attribute, direction]];
  if (attribute === 'balanceChange') return [[attribute, direction]];
  if (attribute === 'isUsed') return [['cashPoolBatchId', direction]];
  if (attribute === 'companyName') {
    return [
      [
        { model: CashPoolParticipantAccounts, as: 'account' },
        { model: Cash_Pool_Participants, as: 'participant' },
        { model: Company, as: 'company' },
        'name',
        direction,
      ],
    ];
  }
  if (attribute === 'source') {
    return [[literal(`CASE WHEN "statementNumber" = '${statementUtils.consts.MANUALLY_ADDED}' THEN 0 ELSE 1 END`)]];
  }
};

export const getOrderBy = (orderBy: any): Array<Array<any>> | undefined => {
  if (!orderBy) return;

  const [attribute, direction = 'ASC'] = orderBy.split('+');

  const order = statementDataMapping(attribute, direction);
  if (!order) return;

  if (order.length > 0 && !order[0].some((col) => col === 'id')) {
    order.push(['id', direction]);
  }

  return order;
};
