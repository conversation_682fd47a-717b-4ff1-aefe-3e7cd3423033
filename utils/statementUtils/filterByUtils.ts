import { Op } from 'sequelize';
import _ from 'lodash';

import * as statementUtils from '../../utils/statementUtils';
import { FilterType } from '../../types';

const getFilterBy = (filterBy: any): FilterType => {
  const START_DATE = 'startDate';
  const END_DATE = 'endDate';
  const expectedFilters = {
    [START_DATE]: START_DATE,
    [END_DATE]: END_DATE,
    statementDate: 'statementDate',
    createdDate: 'createdDate',
    source: 'source',
    isUsed: 'isUsed',
  };
  const allowedStatementDataFilters = Object.values(expectedFilters);
  const startAndEndDate = _.pick(filterBy, [START_DATE, END_DATE]);

  const statementFilter: FilterType = {};

  for (const [key, value] of Object.entries<Date | number | string | null | Array<string | number>>(filterBy)) {
    if (!key || !value) continue;

    if (allowedStatementDataFilters.includes(key)) {
      if (Object.keys(startAndEndDate).length === 2 && (key === START_DATE || key === END_DATE)) {
        statementFilter['date'] = { [Op.between]: [startAndEndDate.startDate, startAndEndDate.endDate] };
        continue;
      }
      if (key === START_DATE) {
        statementFilter['date'] = { [Op.gte]: new Date(value as Date) };
        continue;
      }
      if (key === END_DATE) {
        statementFilter['date'] = { [Op.lte]: new Date(value as Date) };
        continue;
      }
      if (key === expectedFilters.createdDate) {
        const date = new Date(value as string);
        const nextDay = new Date(date);
        nextDay.setDate(date.getDate() + 1);

        statementFilter['createdAt'] = {
          [Op.gte]: date,
          [Op.lt]: nextDay,
        };
        continue;
      }
      if (key === expectedFilters.isUsed) {
        if (value === 'true') {
          statementFilter['cashPoolBatchId'] = { [Op.ne]: null };
        }
        if (value === 'false') {
          statementFilter['cashPoolBatchId'] = { [Op.eq]: null };
        }
        continue;
      }

      if (key === expectedFilters.source) {
        if (value === statementUtils.consts.MANUALLY_ADDED) {
          statementFilter['statementNumber'] = statementUtils.consts.MANUALLY_ADDED;
        } else if (value === statementUtils.consts.INTEREST) {
          statementFilter['statementNumber'] = statementUtils.consts.INTEREST;
        } else if (value === statementUtils.consts.TEMPLATE_UPLOAD) {
          statementFilter['statementNumber'] = statementUtils.consts.TEMPLATE_UPLOAD;
        } else {
          statementFilter['statementNumber'] = {
            [Op.notIn]: [
              statementUtils.consts.MANUALLY_ADDED,
              statementUtils.consts.INTEREST,
              statementUtils.consts.TEMPLATE_UPLOAD,
            ],
          };
        }
        continue;
      }
      statementFilter[key] = value;
      continue;
    }
  }
  return statementFilter;
};

const getCompanyFilterBy = (filterBy: any) => {
  const expectedFilters = { companyId: 'companyId' };
  const allowedCompanyFilters = Object.values(expectedFilters);

  const companyFilter: FilterType = {};

  for (const [key, value] of Object.entries<Date | number | string | null | Array<string | number>>(filterBy)) {
    if (!key || !value) continue;

    if (allowedCompanyFilters.includes(key)) {
      if (key === 'companyId') {
        companyFilter['id'] = value;
        continue;
      }
    }
  }

  return companyFilter;
};

export const getStatementDataFilters = (query: any) => {
  const statementDataFilter = getFilterBy(query);
  const companyFilter = getCompanyFilterBy(query);

  return { statementDataFilter, companyFilter };
};
