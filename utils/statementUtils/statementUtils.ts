import _ from 'lodash';

import { cashPoolParticipantAccountRepository } from '../../repositories';
import { InternalServerError } from '../../utils/ErrorHandler';
import { ParticipantAccountIdType } from '../../types';
import { isBefore, isEqual } from 'date-fns';

import models from '../../models';

const { CashPool } = models;

type ExternalId = string;
type AccountId = number;
type AccountIdMapperType = Record<ExternalId, AccountId>;

/**
 * Fetches account that should have account identification `externalId` defined.
 * `externalId` is id that the clients use and accountId is our id for each account
 * in the cash pool. This mapping is needed so the rows in the `CashPoolStatementData` table
 * have the foreign key to the `CashPoolParticipantAccounts`.
 */
export const getAccountIdentificationToAccountIdMapping = async (
  clientId: number,
  cashPoolId: number,
): Promise<{
  accountIdMapper: AccountIdMapperType;
  cashPoolAccounts: Array<{
    id: number;
    externalIds: Array<ParticipantAccountIdType>;
    balance: number | null;
    participant: any;
  }>;
}> => {
  const cashPoolAccounts = await cashPoolParticipantAccountRepository.getCashPoolParticipantAccountsByClientId(
    clientId,
    cashPoolId,
  );

  if (cashPoolAccounts.length === 0) throw new InternalServerError('Accounts for provided cash pool do not exist.');

  const accountIdMapper: AccountIdMapperType = {};

  for (const cashPoolAccount of cashPoolAccounts) {
    if (cashPoolAccount.externalIds.length === 0) {
      throw new InternalServerError(`Account with id: ${cashPoolAccount.id} has zero account ids.`);
    }
    for (const { externalId, cashPoolAccountId } of cashPoolAccount.externalIds) {
      accountIdMapper[externalId] = cashPoolAccountId;
    }
  }

  return { accountIdMapper, cashPoolAccounts };
};

export const validateDate = (cashPool: typeof CashPool, date: Date | string) => {
  return (
    !_.isEmpty(cashPool.batches) &&
    (isBefore(new Date(date), new Date(cashPool.batches[0].endDate)) ||
      isEqual(new Date(date), new Date(cashPool.batches[0].endDate)))
  );
};
