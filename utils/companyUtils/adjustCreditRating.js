const { checkAndHandleIndexOverflow, creditRatingConstants } = require('../creditRatingUtils');
const { assessmentConstants } = require('./constants');
const { central, nearlyCentral, stronglyIntegral, integral, weaklyIntegral, peripheral } = assessmentConstants;

function adjustCreditRating(creditRating, parentCompanyCreditRating, assessmentName, ringFencing) {
  const indexOfCreditRating = creditRatingConstants.indexOf(creditRating);
  const indexOfParentCreditRating = creditRatingConstants.indexOf(parentCompanyCreditRating);
  if (ringFencing) return creditRating;
  else {
    const adjustedIndex = _adjustIndex(indexOfCreditRating, indexOfParentCreditRating, assessmentName);
    return creditRatingConstants[checkAndHandleIndexOverflow(adjustedIndex)] || null;
  }
}

function _adjustIndex(indexOfCreditRating, indexOfParentCreditRating, assessmentName) {
  let adjustedIndex;
  if (indexOfCreditRating <= indexOfParentCreditRating) adjustedIndex = indexOfParentCreditRating;
  else {
    switch (assessmentName) {
      case central:
        adjustedIndex = indexOfParentCreditRating;
        break;
      case nearlyCentral:
        adjustedIndex = indexOfParentCreditRating + 1;
        break;
      case stronglyIntegral:
        adjustedIndex = indexOfCreditRating - 3;
        if (adjustedIndex <= indexOfParentCreditRating) adjustedIndex = indexOfParentCreditRating + 1;
        break;
      case integral:
        adjustedIndex = indexOfCreditRating - 2;
        if (adjustedIndex <= indexOfParentCreditRating) adjustedIndex = indexOfParentCreditRating + 1;
        break;
      case weaklyIntegral:
        adjustedIndex = indexOfCreditRating - 1;
        if (adjustedIndex <= indexOfParentCreditRating) adjustedIndex = indexOfParentCreditRating + 1;
        break;
      case peripheral:
        adjustedIndex = indexOfCreditRating;
        break;
    }
  }

  return adjustedIndex;
}

module.exports = adjustCreditRating;
