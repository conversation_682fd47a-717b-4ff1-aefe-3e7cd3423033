import { jsonToSheet } from '../documents';

import { CompanyType } from '../../types';

const firstHeaderRow = 'Company Name';
const thirdHeaderRow = 'Implicit Support Q #2';

const addImplicitSupportQuestionTexts = (
  implicitSupportSheetData: Array<Record<string, string | undefined>>,
): Array<Record<string, string | undefined>> => {
  implicitSupportSheetData.push(
    {},
    {},
    {
      [firstHeaderRow]: 'Implicit Support Q #1',
      [thirdHeaderRow]: "The company's assets or profits are separated from the parent and other members of the group",
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #2',
      [thirdHeaderRow]: 'The parent has the ability to provide support to the company',
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #3',
      [thirdHeaderRow]:
        "The parent is legally bound to provide support to the company, for example through a guarantee agreement covering the company's obligations",
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #4',
      [thirdHeaderRow]:
        "The parent has provided the company with a current letter of comfort or support covering the company's obligations",
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #5',
      [thirdHeaderRow]: 'The parent has provided support to the company in previous times of distress',
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #6',
      [thirdHeaderRow]: 'The parent is the ultimate owner of 75% or more of the company',
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #7',
      [thirdHeaderRow]: 'The company is closely related to the group by name or reputation',
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #8',
      [thirdHeaderRow]:
        "The company functions as an integral part of the group's supply chain or holds important assets necessary for the group's operations",
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #9',
      [thirdHeaderRow]: 'The company operates in the same line of business as the group',
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #10',
      [thirdHeaderRow]: "The company generates a significant share of the group's consolidated revenue",
    },
    {
      [firstHeaderRow]: 'Implicit Support Q #11',
      [thirdHeaderRow]: 'The company has been operating as a member of the group for five years or longer',
    },
  );

  return implicitSupportSheetData;
};

const exportCompaniesAsExcel = (companies: Array<CompanyType>) => {
  const companiesSheetData: Array<Record<string, string | number | undefined>> = [];
  const implicitSupportSheetData: Array<Record<string, string | undefined>> = [];
  for (const company of companies) {
    companiesSheetData.push({
      [firstHeaderRow]: company.name,
      Sector: company.industry,
      Country: company.country,
      'Issuer Rating': company.creditRating.rating,
      'Rating adjusted': company.creditRating.ratingAdj,
      'Probability of Default': company.creditRating.probabilityOfDefault,
      'Probability of Default Adjusted': company.creditRating.probabilityOfDefaultAdj,
      Assessment: company.assessment?.name,
    });

    if (!company.assessment?.answers) {
      implicitSupportSheetData.push({ [firstHeaderRow]: company.name });
      continue;
    }

    implicitSupportSheetData.push({
      [firstHeaderRow]: company.name,
      'Implicit Support Q #1': company.assessment.answers.ringFencing ? 'X' : '',
      [thirdHeaderRow]: company.assessment.answers.question1 ? 'X' : '',
      'Implicit Support Q #3': company.assessment.answers.question2 ? 'X' : '',
      'Implicit Support Q #4': company.assessment.answers.question3 ? 'X' : '',
      'Implicit Support Q #5': company.assessment.answers.question4 ? 'X' : '',
      'Implicit Support Q #6': company.assessment.answers.question5 ? 'X' : '',
      'Implicit Support Q #7': company.assessment.answers.question6 ? 'X' : '',
      'Implicit Support Q #8': company.assessment.answers.question7 ? 'X' : '',
      'Implicit Support Q #9': company.assessment.answers.question8 ? 'X' : '',
      'Implicit Support Q #10': company.assessment.answers.question9 ? 'X' : '',
      'Implicit Support Q #11': company.assessment.answers.question10 ? 'X' : '',
    });
  }

  const implicitSupportWithQuestionsSheetData = addImplicitSupportQuestionTexts([...implicitSupportSheetData]);

  return jsonToSheet([
    { sheetData: companiesSheetData, sheetName: 'Companies' },
    { sheetData: implicitSupportWithQuestionsSheetData, sheetName: 'Implicit Support Questions' },
  ]);
};

export default exportCompaniesAsExcel;
