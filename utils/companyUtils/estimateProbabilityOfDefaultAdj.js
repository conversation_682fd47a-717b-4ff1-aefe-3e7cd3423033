const { roundToXDecimals } = require('../numbers');
const { creditRatingToEstimatedProbabilityOfDefaultMapper } = require('./constants');

// podAdj = pod * (podRatingAdj / podRating);
function estimateProbabilityOfDefaultAdj(probabilityOfDefault, rating, ratingAdj) {
  let probabilityOfDefaultAdj =
    probabilityOfDefault *
    (creditRatingToEstimatedProbabilityOfDefaultMapper[ratingAdj] /
      creditRatingToEstimatedProbabilityOfDefaultMapper[rating]);
  probabilityOfDefaultAdj = Math.min(Math.max(0, probabilityOfDefaultAdj), 100);
  return roundToXDecimals(probabilityOfDefaultAdj, 6);
}

module.exports = estimateProbabilityOfDefaultAdj;
