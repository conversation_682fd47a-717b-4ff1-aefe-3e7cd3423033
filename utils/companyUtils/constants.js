const { creditRatingConstants } = require('../creditRatingUtils');

const assessmentConstants = Object.freeze({
  central: 'Central',
  nearlyCentral: 'Nearly Central',
  stronglyIntegral: 'Strongly Integral',
  integral: 'Integral',
  weaklyIntegral: 'Weakly Integral',
  peripheral: 'Peripheral',
});

const questionPoints = {
  question3: 1,
  question4: 1.25,
  question5: 0.75,
  question6: 1,
  question7: 1.25,
  question8: 0.75,
  question9: 1.25,
  question10: 0.75,
};

const pointsLabelMapper = [
  {
    min: 6.75,
    max: 8,
    label: assessmentConstants.central,
  },
  {
    min: 5.5,
    max: 6.75,
    label: assessmentConstants.nearlyCentral,
  },
  {
    min: 4,
    max: 5.5,
    label: assessmentConstants.stronglyIntegral,
  },
  {
    min: 2.5,
    max: 4,
    label: assessmentConstants.integral,
  },
  {
    min: 1.25,
    max: 2.5,
    label: assessmentConstants.weaklyIntegral,
  },
  {
    min: 0,
    max: 1.25,
    label: assessmentConstants.peripheral,
  },
];

// Mapping for credit ratings and estimated probability of defaults
// These values are used to get the adjusted probability of default
const creditRatingToEstimatedProbabilityOfDefaultMapper = {
  'AAA/Aaa': 0.05,
  'AA+/Aa1': 0.1,
  'AA/Aa2': 0.15,
  'AA-/Aa3': 0.2,
  'A+/A1': 0.3,
  'A/A2': 0.35,
  'A-/A3': 0.5,
  'BBB+/Baa1': 0.8,
  'BBB/Baa2': 0.95,
  'BBB-/Baa3': 1.38,
  'BB+/Ba1': 2.23,
  'BB/Ba2': 2.65,
  'BB-/Ba3': 3.91,
  'B+/B1': 6.44,
  'B/B2': 7.7,
  'B-/B3': 11.14,
  'CCC+/Caa1': 18.01,
  'CCC/Caa2': 21.45,
  'CCC-/Caa3': 32.35,
  'CC/Ca': 43.25,
  'C/Ca': 68.7,
};

module.exports = {
  creditRatingConstants,
  assessmentConstants,
  questionPoints,
  pointsLabelMapper,
  creditRatingToEstimatedProbabilityOfDefaultMapper,
};
