const numberUtils = require('../numbers');

/** Returns cumulative probability of default as a percentage */
function getCumulativeProbabilityOfDefault(principal, tenor, pricingApproach) {
  const probabilityOfDefault = pricingApproach.includes('stand-alone')
    ? principal?.creditRating?.probabilityOfDefault
    : principal?.creditRating?.probabilityOfDefaultAdj;

  const cumulativeProbabilityOfDefault = 1 - Math.pow(1 - probabilityOfDefault / 100, tenor);
  const roundedCumulativeProbabilityOfDefault = numberUtils.roundToXDecimals(cumulativeProbabilityOfDefault, 6);

  return { probabilityOfDefaultPercentage: roundedCumulativeProbabilityOfDefault * 100 };
}

module.exports = getCumulativeProbabilityOfDefault;
