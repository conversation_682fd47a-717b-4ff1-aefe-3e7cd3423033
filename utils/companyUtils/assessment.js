const { getLabelFromRangeArray } = require('../arrays');
const { assessmentConstants, pointsLabelMapper, questionPoints } = require('./constants');

function calculateAssessment(implicitSupportAnswers) {
  const { question1, question2, ...answers } = implicitSupportAnswers;
  if (question1) {
    if (question2) return assessmentConstants.central;
    else {
      const points = _calculatePoints(answers);
      if (points === 0) return assessmentConstants.peripheral;
      else return getLabelFromRangeArray(points, pointsLabelMapper);
    }
  } else return assessmentConstants.peripheral;
}

function _calculatePoints(answers) {
  const entries = Object.entries(answers);
  let accumulator = 0;
  for (let i = 0, len = entries.length; i < len; i++) {
    const [key, value] = entries[i];
    if (value) accumulator += questionPoints[key];
  }

  return accumulator;
}

module.exports = calculateAssessment;
