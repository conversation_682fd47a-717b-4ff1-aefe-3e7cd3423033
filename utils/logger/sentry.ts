import * as Sentry from '@sentry/node';

const warning = (warningInfoObject: any) => {
  const err = warningInfoObject.error || new Error(warningInfoObject.message);
  err.message = warningInfoObject.message;
  Sentry.withScope((scope: Sentry.Scope) => {
    scope.setLevel(Sentry.Severity.Warning);
    scope.setExtras(warningInfoObject);
    Sentry.captureException(err);
  });
};

const error = (errorInfoObject: any) => {
  const err = errorInfoObject.error || new Error(errorInfoObject.message);
  err.message = errorInfoObject.message;
  Sentry.withScope((scope: Sentry.Scope) => {
    scope.setLevel(Sentry.Severity.Error);
    scope.setExtras(errorInfoObject);
    Sentry.captureException(err);
  });
};

export default { warning, error };
