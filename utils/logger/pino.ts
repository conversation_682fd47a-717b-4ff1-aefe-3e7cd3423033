import pino from 'pino';

const consoleLogger = pino({
  transport: {
    target: 'pino-pretty',
    options: { colorize: true, ignore: 'pid,hostname' },
  },
});

const fileLogger = (destination: string) =>
  pino(
    pino.transport({
      targets: [
        {
          level: 'info',
          target: 'pino-pretty',
          options: { colorize: true, ignore: 'pid,hostname', sync: true },
        },
        {
          level: 'info',
          target: 'pino-pretty',
          options: { destination, colorize: false, ignore: 'pid,hostname', sync: true },
        },
      ],
    }),
  );

export { consoleLogger, fileLogger };
