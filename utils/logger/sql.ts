/* eslint-disable no-console */
import chalk from 'chalk';

const logWithNewLine = (text: string) => console.log(`${text}\n`);

const logging = (sql: string) => {
  return;
  if (process.env.NODE_ENV !== 'development') {
    return;
  }
  const getTokenStatementRegex = /FROM "Tokens" AS "Token" WHERE "Token"."accessToken" =.*LIMIT 1/;
  const featureStatementRegex = /SELECT "Client_Feature"."id",.*LIMIT 1;/;
  if (sql.match(featureStatementRegex)) return logWithNewLine(chalk.grey(sql));
  if (sql.match(getTokenStatementRegex)) return logWithNewLine(chalk.grey(sql));
  if (sql.includes('SELECT')) return logWithNewLine(chalk.blue(sql));
  if (sql.includes('INSERT')) return logWithNewLine(chalk.green(sql));
  if (sql.includes('UPDATE')) return logWithNewLine(chalk.yellow(sql));
  if (sql.includes('DELETE')) return logWithNewLine(chalk.red(sql));
  return logWithNewLine(sql);
};

export default logging;
