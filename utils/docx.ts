import fs from 'fs/promises';
import <PERSON>xtemplater from 'docxtemplater';
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip';

import { InternalServerError } from './ErrorHandler';

const generateDocx = async (data: any, fileBuffer: Buffer): Promise<Buffer> => {
  try {
    const zip = new PizZip(fileBuffer);
    const docx = new Docxtemplater(zip, { linebreaks: true });
    docx.render(data);
    return docx.getZip().generate({ type: 'nodebuffer' });
  } catch (error) {
    throw new InternalServerError('Error creating docx object.');
  }
};

const generateDocxFromBuffer = (data: any, fileBuffer: Buffer): Promise<Buffer> => {
  return generateDocx(data, fileBuffer);
};

const generateDocxFromLocalFile = async (data: any, filename: string): Promise<Buffer> => {
  const fileBuffer = await fs.readFile(`./static/${filename}`);
  return generateDocx(data, fileBuffer);
};

export { generateDocxFromBuffer, generateDocxFromLocalFile };
