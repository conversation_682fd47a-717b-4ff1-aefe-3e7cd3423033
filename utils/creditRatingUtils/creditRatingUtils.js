const { formatDate } = require('../dates');
const { creditRatingConstants } = require('./constants');

function checkAndHandleIndexOverflow(index) {
  if (index === creditRatingConstants.length) return creditRatingConstants.length - 1;
  else if (index <= 0) return 0;
  else return index;
}

function getHigherAndLowerCreditRating(baseCreditRating) {
  const baseCreditRatingIndex = creditRatingConstants.indexOf(baseCreditRating);
  let lowerCreditRating, higherCreditRating;
  higherCreditRating = creditRatingConstants[baseCreditRatingIndex - 1];
  lowerCreditRating = creditRatingConstants[baseCreditRatingIndex + 1];

  if (creditRatingConstants.indexOf(higherCreditRating) < 0) {
    higherCreditRating = null;
  } else if (creditRatingConstants.indexOf(lowerCreditRating) === creditRatingConstants.length) {
    lowerCreditRating = null;
  }

  return { lowerCreditRating, higherCreditRating };
}

function initializeGeneratedFileData(company) {
  return {
    name: `${company.name}_${formatDate(new Date())}`,
    extension: '.pdf',
    label: 'Credit Rating',
    status: 'Final',
    mimeType: 'application/pdf',
    isGenerated: true,
  };
}

module.exports = {
  checkAndHandleIndexOverflow,
  getHigherAndLowerCreditRating,
  initializeGeneratedFileData,
};
