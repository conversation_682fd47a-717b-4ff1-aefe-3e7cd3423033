const _ = require('lodash');

function _between(value, min, max) {
  return value > min && value <= max;
}

function getLabelFromRangeArray(value, rangeArray) {
  for (let i = 0, len = rangeArray.length; i < len; i++) {
    const { min, max, label } = rangeArray[i];
    if (_between(value, min, max)) return label;
  }
}

const joinTwoArraysOfObjectsByKey = (array1, array2, key) =>
  _.values(_.merge(_.keyBy(array1, key), _.keyBy(array2, key)));

module.exports = {
  getLabelFromRangeArray,
  joinTwoArraysOfObjectsByKey,
};
