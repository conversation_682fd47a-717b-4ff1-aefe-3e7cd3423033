class BaseError extends Error {
  statusCode: number;
  payload: any;

  constructor(statusCode: number, message: string, payload?: any) {
    super();
    this.statusCode = statusCode;
    this.message = message;
    this.payload = payload;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

class BadRequestError extends BaseError {
  constructor(message = 'Bad request') {
    super(400, message);
  }
}

class UnauthorizedError extends BaseError {
  constructor(message = 'Unauthorized') {
    super(401, message);
  }
}

class ForbiddenError extends BaseError {
  constructor(message = 'Forbidden') {
    super(403, message);
  }
}

class NotFoundError extends BaseError {
  constructor(entity = '') {
    super(404, `${entity} not found`);
  }
}

class ConflictError extends BaseError {
  constructor(message = 'Conflict') {
    super(409, message);
  }
}

class ValidationError extends BaseError {
  constructor(message = 'Validation error', payload: any) {
    super(400, message, payload);
  }
}

class XSSError extends BaseError {
  constructor(message = 'XSS attempted') {
    super(400, message);
  }
}

class InternalServerError extends BaseError {
  constructor(message = 'Internal Server Error') {
    super(500, message);
  }
}

class UnreachableCodeError extends BaseError {
  constructor(message = 'Unreachable code error') {
    super(500, message);
  }
}

class SolverInternalError extends BaseError {
  constructor(statusCode = 500, message = 'Solver Internal Error') {
    super(statusCode, message);
  }
}

class UpdateFinalizedError extends BaseError {
  constructor(entity = '') {
    super(400, `Cannot update finalized ${entity}`);
  }
}

class MoveUneditableError extends BaseError {
  constructor(entity = '') {
    super(400, `Cannot move ${entity} of a deleted company`);
  }
}

class FinalizeUneditableError extends BaseError {
  constructor(entity = '') {
    super(400, `Cannot finalize ${entity} of a deleted company`);
  }
}

class MarkAsDraftUneditableError extends BaseError {
  constructor(entity = '') {
    super(400, `Cannot mark as draft ${entity} of a deleted company`);
  }
}

class CreateLimitError extends BaseError {
  constructor(entity = '') {
    super(400, `Max number of ${entity}s already created.`);
  }
}

export {
  BadRequestError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  ValidationError,
  UpdateFinalizedError,
  MoveUneditableError,
  FinalizeUneditableError,
  MarkAsDraftUneditableError,
  CreateLimitError,
  XSSError,
  InternalServerError,
  UnreachableCodeError,
  SolverInternalError,
};
