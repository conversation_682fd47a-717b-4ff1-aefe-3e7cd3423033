import { templateFilenames } from './constants';
import { generateDocxFromBuffer, generateDocxFromLocalFile } from '../docx';
import * as azureService from '../../services/azureService';
import { clientTemplateRepository } from '../../repositories';
import { TemplateFileType } from '../../types';
import { TemplateFileLabelsEnum } from '../../enums/templateFiles';
import { BadRequestError } from '../ErrorHandler';

export const getTemplateFilenameFromLabel = (label: TemplateFileLabelsEnum) => {
  if (label === TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT) return templateFilenames.LOAN_INTERCOMPANY_AGREEMENT_FIXED;
  if (label === TemplateFileLabelsEnum.LOAN_FLOAT_AGREEMENT) return templateFilenames.LOAN_INTERCOMPANY_AGREEMENT_FLOAT;
  if (label === TemplateFileLabelsEnum.LOAN_STANDALONE_REPORT) return templateFilenames.LOAN_STANDALONE;
  if (label === TemplateFileLabelsEnum.LOAN_ADJUSTED_REPORT) return templateFilenames.LOAN_IMPLICIT;
  if (label === TemplateFileLabelsEnum.LOAN_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE)
    return templateFilenames.LOAN_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE;
  if (label === TemplateFileLabelsEnum.LOAN_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE)
    return templateFilenames.LOAN_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE;

  if (label === TemplateFileLabelsEnum.GUARANTEE_AGREEMENT) return templateFilenames.GUARANTEE_INTERCOMPANY_AGREEMENT;
  if (label === TemplateFileLabelsEnum.GUARANTEE_YIELD_EXPECTED_LOSS_STANDALONE_REPORT)
    return templateFilenames.GUARANTEE_YIELD_EL_STANDALONE;
  if (label === TemplateFileLabelsEnum.GUARANTEE_YIELD_EXPECTED_LOSS_ADJUSTED_REPORT)
    return templateFilenames.GUARANTEE_YIELD_EL_IMPLICIT;
  if (label === TemplateFileLabelsEnum.GUARANTEE_SECURITY_STANDALONE_REPORT)
    return templateFilenames.GUARANTEE_SECURITY_STANDALONE;
  if (label === TemplateFileLabelsEnum.GUARANTEE_SECURITY_ADJUSTED_REPORT)
    return templateFilenames.GUARANTEE_SECURITY_IMPLICIT;

  throw new BadRequestError('Invalid file label');
};

export const getTemplateFile = async ({
  clientId,
  country,
  companyId,
  label,
}: {
  clientId: number;
  country: string;
  companyId: number;
  label: string;
}): Promise<TemplateFileType | null> => {
  // Order of search is important. Search in this order: company -> country -> client. Return first found template.
  const companyTemplate = await clientTemplateRepository.getTemplateFile({
    where: { clientId, country: null, companyId, label },
  });
  if (companyTemplate) return companyTemplate;

  const countryTemplate = await clientTemplateRepository.getTemplateFile({
    where: { clientId, country, companyId: null, label },
  });
  if (countryTemplate) return countryTemplate;

  const clientTemplate = await clientTemplateRepository.getTemplateFile({
    where: { clientId, country: null, companyId: null, label },
  });
  if (clientTemplate) return clientTemplate;

  return null;
};

export const getActualGeneratedFile = async (
  dbClientTemplateFile: TemplateFileType | null,
  templateData: any,
  filename: string,
): Promise<Buffer> => {
  if (dbClientTemplateFile) {
    const file = await azureService.getFile(`template/${dbClientTemplateFile.id}`);
    return generateDocxFromBuffer(templateData, file);
  }

  return generateDocxFromLocalFile(templateData, filename);
};
