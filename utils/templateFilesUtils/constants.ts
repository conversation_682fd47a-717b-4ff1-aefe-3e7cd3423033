export const templateFilenames = Object.freeze({
  LOAN_INTERCOMPANY_AGREEMENT_FIXED: 'Intercompany_Loan_Agreement_(fixed).docx',
  LOAN_INTERCOMPANY_AGREEMENT_FLOAT: 'Intercompany_Loan_Agreement_(float).docx',
  LOAN_STANDALONE: 'Loan_Standalone_(template).docx',
  LOAN_IMPLICIT: 'Loan_Implicit_Support_Adjusted_(template).docx',
  LOAN_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE: 'Loan_Standalone_(template-no_lender_perspective).docx',
  LOAN_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE: 'Loan_Implicit_Support_Adjusted_(template-no_lender_perspective).docx',

  GUARANTEE_INTERCOMPANY_AGREEMENT: 'Intercompany_guarantee_agreement.docx',
  GUARANTEE_YIELD_EL_STANDALONE: 'Guarantee_Yield_Expected_Loss_Standalone_(template).docx',
  GUARANTEE_YIELD_EL_IMPLICIT: 'Guarantee_Yield_Expected_Loss_Implicit_Support_Adjusted_(template).docx',
  GUARANTEE_SECURITY_STANDALONE: 'Guarantee_Security_Standalone_(template).docx',
  GUARANTEE_SECURITY_IMPLICIT: 'Guarantee_Security_Implicit_Support_Adjusted_(template).docx',
});
