import { Op } from 'sequelize';
import _ from 'lodash';

import {
  calculateMaximumQuartile,
  calculateUpperQuartile,
  calculateMedianQuartile,
  calculateLowerQuartile,
  calculateMinimumQuartile,
} from '../../utils/numbers';
import { formatDate } from '../../utils/dates';
import { orderMapping } from './constants';
import {
  FilterType,
  DbCuftDataTypeWithCreditRatingsType,
  FrontendCountryValueType,
  CuftDataFilterBodyType,
  OrderByType,
  CuftOrderByType,
  CuftOrderByBodyType,
  CuftOrderMappingKeyType,
  CurrencyToCuftCurrencyType,
  CuftDataSummaryStatisticsType,
  CreditRatingValueType,
  CurrencyWhereType,
  CountryType,
} from '../../types';

const getCurrenciesFilterBy = (currencies: CurrencyToCuftCurrencyType[]): CurrencyWhereType => {
  if (currencies.length > 0) return { currency: { [Op.in]: currencies } };
  return {};
};

const getCreditRatingsFilterBy = (creditRatings: CreditRatingValueType[], excludedCreditRatingIds: number[]) => {
  if (creditRatings.length > 0) {
    return {
      [Op.and]: [{ creditRatingName: { [Op.in]: creditRatings } }, { id: { [Op.notIn]: excludedCreditRatingIds } }],
    };
  }
  return {};
};

const getFilterBy = (
  filterBy: CuftDataFilterBodyType,
  countriesByName: Record<string, CountryType | undefined>,
): FilterType => {
  const expectedFilters = {
    issueDate: 'issueDate',
    tenor: 'tenor',
    countries: 'countries',
    trancheAssetClasses: 'trancheAssetClasses',
  };
  const allowedCuftDataFilters = Object.values(expectedFilters);

  const cuftFilter: FilterType = {};

  for (const [key, value] of Object.entries<Date | number | string | null | Array<string | number>>(filterBy)) {
    if (!key || !value) continue;

    if (allowedCuftDataFilters.includes(key)) {
      if (key === expectedFilters.issueDate) {
        const { dateMonthsBeforeIssueDate, issueDate } = filterBy;
        cuftFilter['cuftTrancheExecutionDate'] = { [Op.between]: [dateMonthsBeforeIssueDate, issueDate] };
        continue;
      }
      if (key === expectedFilters.tenor) {
        const { tenor, numberOfYearsBeforeAndAfterTenor } = filterBy;
        if (!tenor || !numberOfYearsBeforeAndAfterTenor) continue;
        cuftFilter['cuftTrancheTenor'] = {
          [Op.between]: [tenor - numberOfYearsBeforeAndAfterTenor, tenor + numberOfYearsBeforeAndAfterTenor],
        };
      }
      if (key === expectedFilters.countries && Array.isArray(value) && value.length > 0) {
        const frontendCountries = value as Array<FrontendCountryValueType>;
        const mappedCountries = frontendCountries.map((countryName) => countriesByName[countryName]?.cuftCode);
        cuftFilter['cuftBorrowerCountry'] = { [Op.in]: mappedCountries };
        continue;
      }
      if (key === expectedFilters.trancheAssetClasses && Array.isArray(value) && value.length > 0) {
        cuftFilter['cuftTrancheAssetClass'] = { [Op.in]: value };
        continue;
      }
    }
  }

  return cuftFilter;
};

export const getCuftDataFilters = (
  body: CuftDataFilterBodyType,
  countriesByName: Record<string, CountryType | undefined>,
) => {
  const cuftDataFilter = getFilterBy(body, countriesByName);
  const currenciesFilter = getCurrenciesFilterBy(body.currencies);
  const creditRatingsFilter = getCreditRatingsFilterBy(body.creditRatings, body.excludedCreditRatingIds);

  return { cuftDataFilter, currenciesFilter, creditRatingsFilter };
};

export const getOrderBy = (orderBy: CuftOrderByBodyType): CuftOrderByType => {
  const defaultOrder: CuftOrderByType = [['cuftTrancheExecutionDate', 'desc']];

  if (!orderBy) return defaultOrder;

  const mapped = orderMapping[orderBy.split('+')[0] as CuftOrderMappingKeyType];

  const ordering = (orderBy.split('+')[1] || 'asc') as OrderByType;

  if (typeof mapped === 'string') {
    return [[mapped, ordering]];
  }

  if (typeof mapped === 'object') {
    return [[{ model: mapped.model, as: mapped.as }, mapped.field, ordering]];
  }

  return defaultOrder;
};

export const getSummaryStatistics = (
  interests: Array<number>,
  numberOfObservations: number,
): CuftDataSummaryStatisticsType => {
  const sortedInterests = _.sortBy(interests);
  const maximum = calculateMaximumQuartile(sortedInterests);
  const upperQuartile = calculateUpperQuartile(sortedInterests);
  const median = calculateMedianQuartile(sortedInterests);
  const lowerQuartile = calculateLowerQuartile(sortedInterests);
  const minimum = calculateMinimumQuartile(sortedInterests);

  return { maximum, upperQuartile, median, lowerQuartile, minimum, numberOfObservations };
};

export const getCuftDataSheetData = (cuftData: Array<DbCuftDataTypeWithCreditRatingsType>) => {
  return cuftData.map((c: DbCuftDataTypeWithCreditRatingsType) => ({
    Borrower: c.cuftBorrowerName,
    'Filing Company Name': c.filingCompanyName,
    'Primary SIC': c.primarySic,
    Country: c.cuftBorrowerCountry,
    'Issue date': formatDate(c.cuftTrancheExecutionDate),
    'Maturity date': formatDate(c.cuftTrancheMaturityDate),
    Tenor: c.cuftTrancheTenor,
    'Credit Rating': c.creditRatings.creditRatingName,
    'Credit Spread': c.creditRatings.creditRatingValue,
    Currency: c.allCurrencies,
    'Moody Obligor Credit Rating': c.moodyPrincipalObligorCreditRating,
    'S&P Obligor Credit Rating': c.spyPrincipalObligorCreditRating,
    'Tranche Asset Class': c.cuftTrancheAssetClass,
    'Tranche Type': c.cuftTrancheType,
    'Tranche Primary Reference Rate': c.cuftTranchePrimaryReferenceRate,
    'Exhibit Link': c.exhibitLink,
  }));
};
