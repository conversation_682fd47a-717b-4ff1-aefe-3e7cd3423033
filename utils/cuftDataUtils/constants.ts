import models from '../../models';

const { CuftDataCreditRatings } = models;

export const orderMapping = Object.freeze({
  cuftBorrowerName: 'cuftBorrowerName',
  filingCompanyName: 'filingCompanyName',
  cuftBorrowerCountry: 'cuftBorrowerCountry',
  primarySic: 'primarySic',
  cuftTrancheExecutionDate: 'cuftTrancheExecutionDate',
  cuftTrancheMaturityDate: 'cuftTrancheMaturityDate',
  cuftTrancheTenor: 'cuftTrancheTenor',
  creditRating: { model: CuftDataCreditRatings, as: 'creditRatings', field: 'creditRatingName' },
  creditSpread: { model: CuftDataCreditRatings, as: 'creditRatings', field: 'creditRatingValue' },
  moodyPrincipalObligorCreditRating: 'moodyPrincipalObligorCreditRating',
  spyPrincipalObligorCreditRating: 'spyPrincipalObligorCreditRating',
  cuftTrancheAssetClass: 'cuftTrancheAssetClass',
  cuftTrancheType: 'cuftTrancheType',
  cuftTranchePrimaryReferenceRate: 'cuftTranchePrimaryReferenceRate',
});
