const moment = require('moment');
const { parse, addDays, format, formatISO } = require('date-fns');

const momentFormats = {
  DEFAULT: 'DD.MM.YYYY',
  ISO: 'YYYY-MM-DD',
};

function formatDate(date) {
  if (!date) return '-';
  return moment(new Date(date)).format(momentFormats.DEFAULT);
}

function parseDate(dateString) {
  return `${dateString.slice(0, 4)}-${dateString.slice(4, 6)}-${dateString.slice(6, 8)}`;
}

function calculateTenor(startDate, endDate) {
  return moment(endDate, momentFormats.ISO).diff(moment(startDate, momentFormats.ISO), 'years', true);
}

function getDatesInRange(startDate, endDate) {
  const result = [];
  const start = formatISO(startDate, { representation: 'date' });
  const end = formatISO(endDate, { representation: 'date' });

  let currentDate = parse(start, 'yyyy-MM-dd', new Date());

  while (currentDate <= parse(end, 'yyyy-MM-dd', new Date())) {
    result.push(format(currentDate, 'yyyy-MM-dd'));
    currentDate = addDays(currentDate, 1);
  }

  return result;
}

module.exports = {
  calculateTenor,
  formatDate,
  parseDate,
  getDatesInRange,
};
