const ratingToXCoordinateMapper = {
  'AAA/Aaa': 0,
  'AA+/Aa1': 1,
  'AA/Aa2': 2,
  'AA-/Aa3': 3,
  'A+/A1': 4,
  'A/A2': 5,
  'A-/A3': 6,
  'BBB+/Baa1': 7,
  'BBB/Baa2': 8,
  'BBB-/Baa3': 9,
  'BB+/Ba1': 10,
  'BB/Ba2': 11,
  'BB-/Ba3': 12,
  'B+/B1': 13,
  'B/B2': 14,
  'B-/B3': 15,
  'CCC+/Caa1': 16,
  'CCC/Caa2': 17,
  'CCC-/Caa3': 18,
  'CC/Ca': 19,
  'C/Ca': 20,
};

const moodyToCombinedRateMapper = Object.freeze({
  Aaa: 'AAA/Aaa',
  Aa1: 'AA+/Aa1',
  Aa2: 'AA/Aa2',
  Aa3: 'AA-/Aa3',
  A1: 'A+/A1',
  A2: 'A/A2',
  A3: 'A-/A3',
  Baa1: 'BBB+/Baa1',
  Baa2: 'BBB/Baa2',
  Baa3: 'BBB-/Baa3',
  Ba1: 'BB+/Ba1',
  Ba2: 'BB/Ba2',
  Ba3: 'BB-/Ba3',
  B1: 'B+/B1',
  B2: 'B/B2',
  B3: 'B-/B3',
  Caa1: 'CCC+/Caa1',
  Caa2: 'CCC/Caa2',
  Caa3: 'CCC-/Caa3',
  Ca: 'CC/Ca',
  C: 'C/Ca',
});

const ratingMapper = {
  AAA: 'AAA/Aaa',
  AA: 'AA/Aa2',
  A: 'A/A2',
  BBB: 'BBB/Baa2',
  BB: 'BB/Ba2',
  B: 'B/B2',
  CCC: 'CCC/Caa2',
};

const providerRatings = Object.values(ratingMapper);

const stringTenors = ['3M', '6M', '1Y', '2Y', '3Y', '5Y', '7Y', '10Y', '15Y', '20Y', '25Y', '30Y'];

// Mapper for values that need no parsing
const headerToModelEntries = Object.entries({
  DESCRIPTION: 'curveName',
  CURRENCY: 'currency',
  REGION: 'region',
  COUNTRY: 'country',
  RATING: 'rating',
  INDUSTRY3_CATEGORY: 'industryGroup',
  TIER: 'seniority',
});

const seniorityAbbreviations = Object.freeze({
  Subordinated: 'SB',
  'Senior Secured': 'SS',
  Unsubordinated: 'UN',
});

const seniorityEnum = Object.freeze({
  subordinated: 'Subordinated',
  seniorSecured: 'Senior Secured',
  unsubordinated: 'Unsubordinated',
});

const industryAbbreviations = Object.freeze({
  'Consumer Discretionary': 'CD',
  'Consumer Staples': 'CS',
  Energy: 'EN',
  Financials: 'FI',
  Healthcare: 'HC',
  Industrials: 'ID',
  Materials: 'MA',
  'Media & Communications': 'ME',
  'Quasi Government': 'QG',
  'Real Estate & REITs': 'RE',
  Sovereign: 'SO',
  Technology: 'TE',
  Utilities: 'UT',
});

const regionAbbreviations = Object.freeze({
  'Eastern Europe': 'EE',
  'North America': 'NA',
  Oceania: 'OC',
  Africa: 'AF',
  Japan: 'JP',
  'Asia Ex-Japan': 'AJ',
  'Latin America': 'LA',
  'Middle East': 'ME',
  'Western Europe': 'WE',
});

// Countries that we receive data from the provider for
// For other countries, when fetching provider data we should insert 'ALL'
const providerCountries = [
  'China',
  'Hong Kong',
  'India',
  'Indonesia',
  'South Korea',
  'Malaysia',
  'Singapore',
  'Taiwan',
  'United States of America',
  'Canada',
  'Austria',
  'Belgium',
  'Denmark',
  'Finland',
  'France',
  'Germany',
  'Ireland',
  'Italy',
  'Netherlands',
  'Norway',
  'Spain',
  'Sweden',
  'Switzerland',
  'United Kingdom',
  'Russia',
  'Australia',
  'New Zealand',
  'Argentina',
  'Brazil',
  'Mexico',
  'Peru',
  'Israel',
  'Turkey',
  'United Arab Emirates',
  'South Africa',
];

module.exports = {
  stringTenors,
  ratingToXCoordinateMapper,
  headerToModelEntries,
  providerRatings,
  providerCountries,
  ratingMapper,
  seniorityAbbreviations,
  industryAbbreviations,
  regionAbbreviations,
  moodyToCombinedRateMapper,
  seniorityEnum,
};
