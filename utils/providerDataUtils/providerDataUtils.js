const { roundToXDecimals } = require('../numbers');
const { stringTenors } = require('./constants');

function parseDataPoints(values) {
  const parsedDataPoints = {};
  stringTenors.forEach((tenor) => {
    const tenorData = values.find((value) => value.tenor === tenor);
    parsedDataPoints[`fixed${tenor}`] = roundToXDecimals(tenorData['parYield'], 2);
    parsedDataPoints[`float${tenor}`] = roundToXDecimals(tenorData['zSpread'], 2);
    parsedDataPoints[`yield${tenor}`] = roundToXDecimals(tenorData['zeroYield'], 2);
    parsedDataPoints[`discount${tenor}`] = roundToXDecimals(tenorData['discountFactor'], 2);
  });

  return parsedDataPoints;
}

module.exports = { parseDataPoints };
