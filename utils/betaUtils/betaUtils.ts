export const regions = Object.freeze({
  japan: 'japan',
  emerging: 'emerging',
  europe: 'europe',
  global: 'global',
  us: 'us',
});

export const countryToBetaRegionMapper = Object.freeze({
  Afghanistan: 'Emerging Markets',
  Algeria: 'Emerging Markets',
  Angola: 'Emerging Markets',
  Botswana: 'Emerging Markets',
  'British Indian Ocean Territory': 'Emerging Markets',
  'Burkina Faso': 'Emerging Markets',
  Burundi: 'Emerging Markets',
  Cameroon: 'Emerging Markets',
  'Cape Verde': 'Emerging Markets',
  'Central African Rep.': 'Emerging Markets',
  Chad: 'Emerging Markets',
  Comoros: 'Emerging Markets',
  Congo: 'Emerging Markets',
  "Côte d'Ivoire, Republic of": 'Emerging Markets',
  'Dem. Rep. Congo': 'Emerging Markets',
  Djibouti: 'Emerging Markets',
  Egypt: 'Emerging Markets',
  'Eq. Guinea': 'Emerging Markets',
  Eritrea: 'Emerging Markets',
  Ethiopia: 'Emerging Markets',
  Gabon: 'Emerging Markets',
  Gambia: 'Emerging Markets',
  Ghana: 'Emerging Markets',
  Guinea: 'Emerging Markets',
  'Guinea-Bissau': 'Emerging Markets',
  Kenya: 'Emerging Markets',
  Lesotho: 'Emerging Markets',
  Liberia: 'Emerging Markets',
  Libya: 'Emerging Markets',
  Madagascar: 'Emerging Markets',
  Malawi: 'Emerging Markets',
  Mali: 'Emerging Markets',
  Mauritania: 'Emerging Markets',
  Mauritius: 'Emerging Markets',
  Mayotte: 'Emerging Markets',
  Morocco: 'Emerging Markets',
  Mozambique: 'Emerging Markets',
  Namibia: 'Emerging Markets',
  Niger: 'Emerging Markets',
  Nigeria: 'Emerging Markets',
  Réunion: 'Emerging Markets',
  Rwanda: 'Emerging Markets',
  'S. Sudan': 'Emerging Markets',
  'Sao Tome and Principe': 'Emerging Markets',
  Senegal: 'Emerging Markets',
  Seychelles: 'Emerging Markets',
  'Sierra Leone': 'Emerging Markets',
  Somalia: 'Emerging Markets',
  Somaliland: 'Emerging Markets',
  'South Africa': 'Emerging Markets',
  Sudan: 'Emerging Markets',
  Swaziland: 'Emerging Markets',
  Tanzania: 'Emerging Markets',
  Togo: 'Emerging Markets',
  Tunisia: 'Emerging Markets',
  Uganda: 'Emerging Markets',
  'W. Sahara': 'Emerging Markets',
  Zambia: 'Emerging Markets',
  Zimbabwe: 'Emerging Markets',
  Bangladesh: 'Emerging Markets',
  Bhutan: 'Emerging Markets',
  Brunei: 'Emerging Markets',
  Cambodia: 'Emerging Markets',
  China: 'Emerging Markets',
  'Hong Kong': 'Emerging Markets',
  India: 'Emerging Markets',
  Indonesia: 'Emerging Markets',
  Kazakhstan: 'Emerging Markets',
  Kyrgyzstan: 'Emerging Markets',
  Laos: 'Emerging Markets',
  Macao: 'Japan',
  Malaysia: 'Emerging Markets',
  Maldives: 'Emerging Markets',
  Mongolia: 'Emerging Markets',
  Myanmar: 'Emerging Markets',
  Nepal: 'Emerging Markets',
  'North Korea': 'Emerging Markets',
  Pakistan: 'Emerging Markets',
  Philippines: 'Emerging Markets',
  Singapore: 'Japan',
  'South Korea': 'Japan',
  'Sri Lanka': 'Emerging Markets',
  Taiwan: 'Japan',
  Tajikistan: 'Emerging Markets',
  Thailand: 'Emerging Markets',
  'Timor-Leste': 'Emerging Markets',
  Turkmenistan: 'Emerging Markets',
  Uzbekistan: 'Emerging Markets',
  Vietnam: 'Emerging Markets',
  Albania: 'Europe',
  Armenia: 'Europe',
  Azerbaijan: 'Europe',
  Belarus: 'Europe',
  'Bosnia and Herz.': 'Europe',
  Bulgaria: 'Europe',
  Croatia: 'Europe',
  'Czech Republic': 'Europe',
  Estonia: 'Europe',
  Georgia: 'Europe',
  Hungary: 'Europe',
  Kosovo: 'Europe',
  Latvia: 'Europe',
  Lithuania: 'Europe',
  Macedonia: 'Europe',
  Moldova: 'Europe',
  Montenegro: 'Europe',
  Poland: 'Europe',
  Romania: 'Europe',
  Russia: 'Europe',
  Serbia: 'Europe',
  Slovakia: 'Europe',
  Slovenia: 'Europe',
  Ukraine: 'Europe',
  Japan: 'Japan',
  Argentina: 'Emerging Markets',
  Belize: 'Emerging Markets',
  Bolivia: 'Emerging Markets',
  'Bonaire, Sint Eustatius and Saba': 'Emerging Markets',
  Brazil: 'Emerging Markets',
  Chile: 'Emerging Markets',
  Colombia: 'Emerging Markets',
  'Costa Rica': 'Emerging Markets',
  Cuba: 'Emerging Markets',
  Curaçao: 'Emerging Markets',
  'Dominican Rep.': 'Emerging Markets',
  Ecuador: 'Emerging Markets',
  'El Salvador': 'Emerging Markets',
  'Falkland Is.': 'Emerging Markets',
  'French Guiana': 'Emerging Markets',
  Grenada: 'Emerging Markets',
  Guatemala: 'Emerging Markets',
  Guyana: 'Emerging Markets',
  Haiti: 'Emerging Markets',
  Honduras: 'Emerging Markets',
  Mexico: 'Emerging Markets',
  Nicaragua: 'Emerging Markets',
  Panama: 'Emerging Markets',
  Paraguay: 'Emerging Markets',
  Peru: 'Emerging Markets',
  'Saint Helena, Ascension and Tristan da Cunha': 'Emerging Markets',
  'Sint Maarten (Dutch part)': 'Emerging Markets',
  'South Georgia and South Sandwich Islands': 'Emerging Markets',
  Suriname: 'Emerging Markets',
  Uruguay: 'Emerging Markets',
  Venezuela: 'Emerging Markets',
  Bahrain: 'Europe',
  Benin: 'Emerging Markets',
  Iran: 'Emerging Markets',
  Iraq: 'Emerging Markets',
  Israel: 'Europe',
  Jordan: 'Emerging Markets',
  Kuwait: 'Europe',
  Lebanon: 'Emerging Markets',
  'N. Cyprus': 'Europe',
  Oman: 'Europe',
  Palestine: 'Emerging Markets',
  Qatar: 'Europe',
  'Saudi Arabia': 'Europe',
  Syria: 'Emerging Markets',
  Turkey: 'Emerging Markets',
  'United Arab Emirates': 'Europe',
  Yemen: 'Emerging Markets',
  Anguilla: 'Emerging Markets',
  Antarctica: 'Emerging Markets',
  'Antigua and Barbuda': 'Emerging Markets',
  Aruba: 'Emerging Markets',
  Bahamas: 'Emerging Markets',
  Barbados: 'Emerging Markets',
  Bermuda: 'Emerging Markets',
  Canada: 'United States',
  'Cayman Islands': 'Emerging Markets',
  Dominica: 'Emerging Markets',
  Guadeloupe: 'Emerging Markets',
  Jamaica: 'Emerging Markets',
  Martinique: 'Emerging Markets',
  Montserrat: 'Emerging Markets',
  'Northern Mariana Islands': 'Emerging Markets',
  Palau: 'Emerging Markets',
  'Puerto Rico': 'United States',
  'Saint Barthélemy': 'United States',
  'Saint Kitts and Nevis': 'United States',
  'Saint Lucia': 'United States',
  'Saint Martin': 'United States',
  'Saint Pierre and Miquelon': 'United States',
  'Saint Vincent and the Grenadines': 'United States',
  'Trinidad and Tobago': 'United States',
  'Turks and Caicos Islands': 'United States',
  'United States of America': 'United States',
  'Virgin Islands, British': 'United States',
  'Virgin Islands, U.S.': 'United States',
  'American Samoa': 'United States',
  Australia: 'United States',
  'Christmas Island': 'United States',
  'Cocos (Keeling) Islands': 'United States',
  'Cook Islands': 'United States',
  Fiji: 'United States',
  'Fr. S. Antarctic Lands': 'United States',
  'French Polynesia': 'United States',
  Guam: 'United States',
  'Heard Island and McDonald Islands': 'United States',
  Kiribati: 'United States',
  'Marshall Islands': 'United States',
  'Micronesia, Federated States of': 'United States',
  Nauru: 'United States',
  'New Caledonia': 'United States',
  'New Zealand': 'United States',
  Niue: 'United States',
  'Norfolk Island': 'United States',
  'Papua New Guinea': 'United States',
  Pitcairn: 'United States',
  Samoa: 'United States',
  'Solomon Is.': 'United States',
  Tokelau: 'United States',
  Tonga: 'United States',
  Tuvalu: 'United States',
  'United States Minor Outlying Islands': 'United States',
  Vanuatu: 'United States',
  'Wallis and Futuna': 'United States',
  'Åland Islands': 'Europe',
  Andorra: 'Europe',
  Austria: 'Europe',
  Belgium: 'Europe',
  'Bouvet Island': 'Europe',
  Cyprus: 'Europe',
  Denmark: 'Europe',
  'Faroe Islands': 'Europe',
  Finland: 'Europe',
  France: 'Europe',
  Germany: 'Europe',
  Gibraltar: 'Europe',
  Greece: 'Europe',
  Greenland: 'Europe',
  Guernsey: 'Europe',
  'Holy See (Vatican City)': 'Europe',
  Iceland: 'Europe',
  Ireland: 'Europe',
  'Isle of Man': 'Europe',
  Italy: 'Europe',
  Jersey: 'Europe',
  Liechtenstein: 'Europe',
  Luxembourg: 'Europe',
  Malta: 'Europe',
  Monaco: 'Europe',
  Netherlands: 'Europe',
  Norway: 'Europe',
  Portugal: 'Europe',
  'San Marino': 'Europe',
  Spain: 'Europe',
  Sweden: 'Europe',
  Switzerland: 'Europe',
  'United Kingdom': 'Europe',
});

export const types = Object.freeze({
  beta: 'Beta',
  unleveredBeta: 'Unlevered Beta',
  unleveredBetaCorrectedForCash: 'Unlevered Beta Corrected for Cash',
});
