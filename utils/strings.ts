function groupByThreeCharacters(string: any) {
  return string != null ? string.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ') : '';
}

function capitalize(string: string | null | undefined) {
  return string ? string.charAt(0).toUpperCase() + string.slice(1) : '';
}

function truncateToTwoDecimals(number: number): string | null {
  if (typeof number !== 'number' || isNaN(number)) return null;

  const numberString = String(number);

  const [integer, decimal] = numberString.split('.');

  if (decimal && decimal.length > 2) {
    if (decimal.substring(0, 2) == '00') return integer;
    return `${integer}.${decimal.substring(0, 2)}`;
  }

  return numberString;
}

export { groupByThreeCharacters, capitalize, truncateToTwoDecimals };
