const { countryToISOMapping } = require('../../utils/creditRatingUtils');
const {
  seniorityAbbreviations,
  industryAbbreviations,
  regionAbbreviations,
  providerCountries,
} = require('../../utils/providerDataUtils');
const { BadRequestError } = require('../ErrorHandler');
const { countryToRegionMapper } = require('../reportUtils');

const getCashPoolCalculationBody = ({
  cashPoolLeader,
  participants,
  // operatingCost, not used currently, we just pass 0. Operating cost is used in expense calculation.
  operatingCostMarkup,
  creditInterestRate,
  debitInterestRate,
}) => ({
  CPL: {
    credit_IR: creditInterestRate,
    debit_IR: debitInterestRate,
    markup: operatingCostMarkup,
    name: cashPoolLeader.companyId,
    operating_expenses: 0,
  },
  CPP: participants.map(({ balance, creditInterestRate, debitInterestRate, companyId }) => ({
    balance: balance || 0,
    credit_IR: creditInterestRate || 0,
    debit_IR: debitInterestRate || 0,
    name: companyId,
  })),
});

/**
 * CIR and DIR with overnightRate were added because the CIR/DIR depends on the overnight rate in float pools.
 * For fixed they contain the same CIR/DIR value but for float they are different and we want to show that in
 * the DataView table. We don't want to overwrite the actual CIR and DIR so we use this to save as CIR/DIR in
 * trails we create. See createParticipantAccountTrails function. It creates a copy of the current account row.
 * Since we can't overwrite the actual CIR/DIR we use this cirWithOvernightRate/dirWithOvernightRate to keep that
 * value which then becomes the CIR/DIR in the trails.
 */
const transformSolverDataForDb = (participant) => {
  /**
   * A corner case in the model is a debit rate that’s negative (so essentially a credit rate). While this is the expected output of the model, it may raise some challenges and questions.
   * We should have a line of code that says if debit rate = negative, then set it to 0.
   */
  if (participant.adjDir < 0) {
    participant.adjDir = 0;
    participant.adjustedInterestPaid = participant.adjDir * participant.debitBalance;
  }

  return {
    balance: participant.balance,
    cirWithOvernightRate: participant.cir,
    dirWithOvernightRate: participant.dir,
    adjustedCreditInterestRate: participant.adjCir,
    adjustedCreditInterestReceived: participant.adjustedInterestReceived,
    adjustedDebitInterestRate: participant.adjDir,
    adjustedDebitInterestPaid: participant.adjustedInterestPaid,
    netInterestBenefit: participant.netInterestBenefit,
  };
};

/**
 * Credit and Debit interest rates aren't reset because they are shown
 * in the payments table even if the participant is no longer a part of the cash pool.
 */
const getDeletedParticipantAccountFields = () => {
  return {
    balance: null,
    adjustedCreditInterestRate: null,
    adjustedDebitInterestRate: null,
    adjustedCreditInterestReceived: null,
    adjustedDebitInterestPaid: null,
    netInterestBenefit: null,
    deletedAt: new Date(),
  };
};

/**
 * Accepts either cashPool in case of Physical or a topCurrencyAccount
 * in case of Nordic. Since a Nordic topCurrencyAccount is basically a
 * Physical cash pool the logic is the same.
 */
const runCashPoolCreateUpdateGuards = (cashPool) => {
  const isFixed = cashPool.interestType === 'fixed';
  const word = isFixed ? 'rate' : 'spread';
  // if (cashPool.accounts.length < 2) {
  //   throw new BadRequestError('At least two participants needed to set up cash pool.');
  // }
  if (cashPool.creditInterestRate >= cashPool.debitInterestRate && isFixed) {
    throw new BadRequestError('The credit interest rate must be lower than the debit interest rate.');
  }
  for (const { creditInterestRate, debitInterestRate } of cashPool.accounts) {
    if (creditInterestRate >= cashPool.creditInterestRate) {
      throw new BadRequestError(
        "The participants' credit interest rates must be lower than the cash pool's credit interest rate.",
      );
    }
    if (debitInterestRate <= cashPool.debitInterestRate) {
      throw new BadRequestError(
        `The participants' debit interest ${word}s must be higher than the cash pool's debit interest ${word}.`,
      );
    }
  }
};

// Used for estimating participant rates
const createParticipantProviderSearchData = ({
  estimateRequest,
  participant,
  providerDataIssueDate,
  isUsingRegionalProviderData,
}) => {
  const participantSearchData = {
    issueDate: providerDataIssueDate,
    region: regionAbbreviations[countryToRegionMapper[participant.country]],
    country:
      providerCountries.includes(participant.country) && !isUsingRegionalProviderData
        ? countryToISOMapping[participant.country]
        : 'ALL',
    industryGroup: industryAbbreviations[participant.industry],
    currency: estimateRequest.currency,
    seniority: seniorityAbbreviations[estimateRequest.seniority],
  };

  return { participantSearchData };
};

/**
 * Goes over all accounts and sets `isLeader` flag if an account is also a leader.
 * The if part is to add the leader as a participant if it's not already there.
 * That is the case if the leader is not an account in the cash pool.
 */
const getCashPoolParticipants = (cashPool, cashPoolId) => {
  const participants = cashPool.accounts.map((account) => {
    const accountWithCashPoolId = { ...account, cashPoolId };

    if (account.companyId === cashPool.leaderId) return { ...accountWithCashPoolId, isLeader: true };
    return accountWithCashPoolId;
  });

  if (participants.every((p) => !p.isLeader)) {
    participants.push({ cashPoolId, companyId: cashPool.leaderId, isLeader: true });
  }

  return participants;
};

module.exports = {
  getCashPoolCalculationBody,
  transformSolverDataForDb,
  getDeletedParticipantAccountFields,
  runCashPoolCreateUpdateGuards,
  createParticipantProviderSearchData,
  getCashPoolParticipants,
};
