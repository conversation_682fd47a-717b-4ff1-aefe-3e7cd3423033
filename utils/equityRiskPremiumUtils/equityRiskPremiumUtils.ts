export const countryMapper: Record<string, string | undefined> = Object.freeze({
  'Andorra (Principality of)': 'Andorra',
  'Bosnia and Herzegovina': 'Bosnia and Herz.',

  'Congo (Democratic Republic of)': 'Dem. Rep. Congo',
  'Democratic Republic of Congo': 'Dem. Rep. Congo',

  'Congo (Republic of)': 'Congo',
  'Republic of the Congo': 'Congo',

  "Côte d'Ivoire": "Côte d'Ivoire, Republic of",
  Curacao: 'Curaçao',
  'Dominican Republic': 'Dominican Rep.',
  'Guernsey (States of)': 'Guernsey',
  'Jersey (States of)': 'Jersey',
  Korea: 'South Korea',
  'Solomon Islands': 'Solomon Is.',
  'St. Maarten': 'Sint Maarten (Dutch part)',
  'St. Vincent & the Grenadines': 'Saint Vincent and the Grenadines',
  'United States': 'United States of America',
});

export const regionsToErpRegions = Object.freeze({
  Africa: 'Africa',
  Asia: 'Asia Ex-Japan',
  'Australia & New Zealand': 'Oceania',
  Caribbean: 'Latin America',
  'Central and South America': 'Latin America',
  'Eastern Europe & Russia': 'Eastern Europe',
  'Middle East': 'Middle East',
  'North America': 'North America',
  'Western Europe': 'Western Europe',
});
