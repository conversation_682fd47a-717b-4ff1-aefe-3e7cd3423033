export function roundToXDecimals(number: string, decimals = 2): number {
  return parseFloat(parseFloat(number).toFixed(decimals));
}

const calculateQuartile = (sortedArray: Array<number>, q: number): number => {
  const pos = (sortedArray.length - 1) * q;
  const base = Math.floor(pos);
  const rest = pos - base;

  if (sortedArray[base + 1] != null) {
    return sortedArray[base] + rest * (sortedArray[base + 1] - sortedArray[base]);
  }

  return sortedArray[base];
};

export const calculateMaximumQuartile = (sortedArray: Array<number>) => calculateQuartile(sortedArray, 1);
export const calculateUpperQuartile = (sortedArray: Array<number>) => calculateQuartile(sortedArray, 0.75);
export const calculateMedianQuartile = (sortedArray: Array<number>) => calculateQuartile(sortedArray, 0.5);
export const calculateLowerQuartile = (sortedArray: Array<number>) => calculateQuartile(sortedArray, 0.25);
export const calculateMinimumQuartile = (sortedArray: Array<number>) => calculateQuartile(sortedArray, 0);
