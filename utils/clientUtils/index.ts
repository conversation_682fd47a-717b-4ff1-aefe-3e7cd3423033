import seedInitialClientData from './seedInitialClientData';
import { checkClientFeatureFlags } from './clientFeatureFlags';
import * as marsUtils from './marsUtils';

export function extractClientFromRequest(requestUrl?: string) {
  if (!requestUrl) return null;

  const parsedUrl = new URL(requestUrl);

  const parts = parsedUrl.hostname.split('.');

  if (parts.length === 3) {
    return null;
  } else if (parts.length > 3) {
    return parts[0];
  } else {
    return null;
  }
}

export { seedInitialClientData, checkClientFeatureFlags, marsUtils };
