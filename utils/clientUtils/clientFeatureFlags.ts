import { Client } from '../../enums';
import { isArray } from 'lodash';

const clientList = {
  [Client.GUNVOR]: ['gunvor', 'gvr_test', 'gvr test'],
  [Client.MARS]: ['mars'],
};

export function checkClientFeatureFlags(
  clients: keyof typeof clientList | Array<keyof typeof clientList>,
  clientName: string,
) {
  if (isArray(clients)) {
    return clients.some((client) => clientList[client].includes(clientName.toLowerCase()));
  }
  return clientList[clients].includes(clientName.toLowerCase());
}
