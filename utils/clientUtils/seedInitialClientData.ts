import { companyRepository } from '../../repositories';
import { CompanyType } from '../../types';

const seedInitialClientData = async ({
  user,
  companiesToCreate,
}: {
  user: { clientId: number; username: string };
  companiesToCreate: Array<CompanyType>;
}) => {
  const parentCompanyData = companiesToCreate.shift();
  const parentCompany = await companyRepository.createCompany(parentCompanyData, user);
  await companyRepository.updateCompany(
    { clientId: user.clientId },
    { parentCompanyId: parentCompany.id },
    false,
    false,
    null,
  );

  for (const companyToCreate of companiesToCreate) {
    companyToCreate.parentCompanyId = parentCompany.id;
  }

  await companyRepository.createCompanies(companiesToCreate, user);
};

export default seedInitialClientData;
