import { UserDataAccessTokenType } from '../../types';
import { guaranteeRepository, loanRepository, b2bLoanRepository } from '../../repositories';

function parseExternalId(val: any): number | null {
  const num = Number(val);
  return isNaN(num) ? null : num;
}

export async function incrementExternalId(user: Partial<UserDataAccessTokenType>) {
  const [previousLoan, previousGuarantee, previousB2BLoans] = await Promise.all([
    loanRepository.getLoans({
      where: { clientId: user.clientId },
      order: [['createdAt', 'DESC']],
      limit: 1,
      paranoid: false,
    }),
    guaranteeRepository.getGuarantees({
      where: { clientId: user.clientId },
      order: [['createdAt', 'DESC']],
      limit: 1,
      paranoid: false,
    }),
    b2bLoanRepository.getLoans({
      where: { clientId: user.clientId },
      order: [['createdAt', 'DESC']],
      limit: 1,
    }),
  ]);

  const previousLoanExternalId = parseExternalId(previousLoan[0]?.externalId);
  const previousGuaranteeExternalId = parseExternalId(previousGuarantee[0]?.externalId);
  const previousB2BLoanExternalId = parseExternalId(previousB2BLoans[0]?.externalId);

  const ids = [previousLoanExternalId, previousGuaranteeExternalId, previousB2BLoanExternalId].filter(
    (n): n is number => n !== null,
  );

  const highestId = ids.length > 0 ? Math.max(...ids) : null;

  return highestId !== null ? String(highestId + 1) : '6000';
}
