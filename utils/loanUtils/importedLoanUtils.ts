import { EmbeddedCompanyCreditRatingType } from '../../types/report.types';

export function overrideBorrowerCreditRating(creditRating: EmbeddedCompanyCreditRatingType & { newRating?: string }) {
  if (creditRating?.newRating) {
    const borrowerCreditRating = creditRating;
    borrowerCreditRating.rating = borrowerCreditRating.newRating!;
    delete borrowerCreditRating.newRating;
    return borrowerCreditRating;
  }

  return creditRating;
}
