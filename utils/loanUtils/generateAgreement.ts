import { getActualGeneratedFile, getTemplateFile } from '../templateFilesUtils';
import * as loanUtils from './loanUtils';
import { TemplateFileLabelsEnum } from '../../enums/templateFiles';
import { LoanType, LoanFileMetadataType } from '../../types';

const { LOAN_FIXED_AGREEMENT, LOAN_FLOAT_AGREEMENT } = TemplateFileLabelsEnum;

const getTemplateFilename = (loan: LoanType) => {
  return `Intercompany_Loan_Agreement_(${loan.rateType.type}).docx`;
};

export async function generateAgreement(
  loan: LoanType,
  clientId: number,
  templateData: any,
): Promise<[Buffer, LoanFileMetadataType]> {
  const label = loan.rateType.type === 'fixed' ? LOAN_FIXED_AGREEMENT : LOAN_FLOAT_AGREEMENT;

  const dbClientTemplateFileAgreement = await getTemplateFile({
    clientId,
    country: loan.borrower.country,
    companyId: loan.borrower.id,
    label,
  });

  const agreementFilename = getTemplateFilename(loan);

  const agreementFile = await getActualGeneratedFile(dbClientTemplateFileAgreement, templateData, agreementFilename);

  return [
    agreementFile,
    {
      loanId: loan.id,
      label: 'Agreement',
      name: loanUtils.getReportName(loan.borrower.name, loan.currency, loan.amount, 'Loan Agreement'),
      extension: '.docx',
      isGenerated: true,
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    },
  ];
}
