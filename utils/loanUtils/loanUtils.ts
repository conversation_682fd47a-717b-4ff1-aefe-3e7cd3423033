import { groupByThreeCharacters } from '../strings';
import { paymentFrequencyToReferenceRateMaturityMapper, mapCurrencyToReferenceRate } from './constants';
import { PaymentFrequencyType } from '../../types';

export function getReferenceRate(currency: string, paymentFrequency: PaymentFrequencyType, issueDate: Date) {
  return {
    referenceRate: mapCurrencyToReferenceRate(currency, issueDate),
    referenceRateMaturity: paymentFrequencyToReferenceRateMaturityMapper[paymentFrequency],
  };
}

export const getReportName = (borrowerName: string, currency: string, amount: number, reportName: string) =>
  `${borrowerName} ${currency} ${groupByThreeCharacters(amount)} ${reportName}`;
