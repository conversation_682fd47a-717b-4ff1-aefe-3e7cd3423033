import { getActualGeneratedFile, getTemplateFile } from '../templateFilesUtils';
import * as loanUtils from './loanUtils';
import { TemplateFileLabelsEnum } from '../../enums/templateFiles';
import { LoanType, LoanFileMetadataType } from '../../types';

const {
  LOAN_STANDALONE_REPORT,
  LOAN_ADJUSTED_REPORT,
  LOAN_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE,
  LOAN_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE,
} = TemplateFileLabelsEnum;

const getTemplateFilename = (loan: LoanType) => {
  if (loan.isApproachCalculated) {
    return loan.pricingApproach.includes('implicit')
      ? 'Loan_Implicit_Support_Adjusted_(template).docx'
      : 'Loan_Standalone_(template).docx';
  }
  return loan.pricingApproach.includes('implicit')
    ? 'Loan_Implicit_Support_Adjusted_(template-no_lender_perspective).docx'
    : 'Loan_Standalone_(template-no_lender_perspective).docx';
};

const getLabel = (loan: LoanType) => {
  if (loan.isApproachCalculated) {
    return loan.pricingApproach.includes('implicit') ? LOAN_ADJUSTED_REPORT : LOAN_STANDALONE_REPORT;
  }

  return loan.pricingApproach.includes('implicit')
    ? LOAN_ADJUSTED_REPORT_NO_LENDER_PERSPECTIVE
    : LOAN_STANDALONE_REPORT_NO_LENDER_PERSPECTIVE;
};

export async function generateTpReport(
  loan: LoanType,
  clientId: number,
  templateData: any,
): Promise<[Buffer, LoanFileMetadataType]> {
  const label = getLabel(loan);

  const dbClientTemplateFileReport = await getTemplateFile({
    clientId,
    country: loan.borrower.country,
    companyId: loan.borrower.id,
    label,
  });

  const tpReportFilename = getTemplateFilename(loan);

  const tpReportFile = await getActualGeneratedFile(dbClientTemplateFileReport, templateData, tpReportFilename);

  return [
    tpReportFile,
    {
      loanId: loan.id,
      label: 'TP Report',
      name: loanUtils.getReportName(loan.borrower.name, loan.currency, loan.amount, 'Loan Report'),
      extension: '.docx',
      isGenerated: true,
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    },
  ];
}
