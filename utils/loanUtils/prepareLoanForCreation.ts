import _ from 'lodash';

import { calculateLoanDataAlgorithm } from '../../controllers/algorithms/reportAlgorithms';
import { featureNames, reportEnums } from '../../enums';
import featureUtils from '../featureUtils';
import reportUtils from '../reportUtils';
import * as loanUtils from './loanUtils';
import { B2BLoanType, LoanType } from '../../types';

export async function prepareLoanForCreation(loan: LoanType, clientId: number) {
  reportUtils.checkCurrencyProviderDataAvailability(loan);
  await featureUtils.checkCreateLimits({
    clientId,
    featureName: featureNames.LOAN_NUMBER,
    reportType: reportEnums.REPORT_TYPES.LOAN,
  });
  await featureUtils.checkCombinedLoanGuaranteeLimit({ clientId });

  const isUsingRegionalProviderData = await featureUtils.checkIsUsingRegionalProviderData(clientId);

  const { report, calculationLog, pricingApproach } = await calculateLoanDataAlgorithm({
    ...loan,
    clientId,
    isUsingRegionalProviderData,
  });
  loan.report = report;
  loan.calculationLog = calculationLog;
  loan.pricingApproach = pricingApproach;

  // initialize reference rate
  if (loan.rateType.type === 'float') {
    loan.rateType = {
      ...loan.rateType,
      ...loanUtils.getReferenceRate(loan.currency, loan.paymentFrequency, loan.issueDate),
    };
  }
}

export async function prepareB2BLoanForCreation(loan: any, clientId: number): Promise<B2BLoanType> {
  const b2bLoanToReturn = _.cloneDeep(loan);
  reportUtils.checkCurrencyProviderDataAvailability(loan);
  await featureUtils.checkCreateLimits({
    clientId,
    featureName: featureNames.BACK_TO_BACK_LOAN_NUMBER,
    reportType: reportEnums.REPORT_TYPES.B2B_LOAN,
  });
  await featureUtils.checkCombinedLoanGuaranteeLimit({ clientId });

  const isUsingRegionalProviderData = await featureUtils.checkIsUsingRegionalProviderData(clientId);

  const ultimateLender = loan.lenders[0];
  const ultimateBorrower = loan.borrowers[loan.borrowers.length - 1];

  const { report, calculationLog, pricingApproach } = await calculateLoanDataAlgorithm({
    ...{ ...loan, lender: ultimateLender, borrower: ultimateBorrower },
    clientId,
    isUsingRegionalProviderData,
  });
  b2bLoanToReturn.report = report;
  b2bLoanToReturn.calculationLog = calculationLog;
  b2bLoanToReturn.pricingApproach = pricingApproach;

  // initialize reference rate
  if (b2bLoanToReturn.rateType.type === 'float') {
    b2bLoanToReturn.rateType = {
      ...b2bLoanToReturn.rateType,
      ...loanUtils.getReferenceRate(
        b2bLoanToReturn.currency,
        b2bLoanToReturn.paymentFrequency,
        b2bLoanToReturn.issueDate,
      ),
    };
  }

  return { ...b2bLoanToReturn, ultimateLender, ultimateBorrower };
}
