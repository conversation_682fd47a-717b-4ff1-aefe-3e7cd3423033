import moment from 'moment';

export const paymentFrequencyToReferenceRateMaturityMapper = {
  Monthly: '1 month',
  Quarterly: '3 months',
  'Semi-annual': '6 months',
  Annual: '12 months',
};

export const currencyToReferenceRateMapper = {
  USD: 'USD LIBOR',
  EUR: 'EURIBOR',
  GBP: 'GBP LIBOR',
  CHF: 'CHF LIBOR',
  AUD: 'BBSW',
  JPY: 'JPY LIBOR',
  CAD: 'CDOR/BA',
  CNH: 'SHIBOR Hong Kong',
  CNY: '7D Repo Onshore',
  HKD: 'HIBOR',
  INR: 'Modified MIFOR',
  IDR: 'JIBOR',
  KRW: 'CD',
  MYR: 'KLIBOR',
  PHP: 'PHIREF',
  SGD: 'SOR',
  TWD: 'TAIBOR',
  THB: 'BIBOR',
  DKK: 'CIBOR',
  BRL: 'CDI',
  COP: 'IBR',
  NOK: 'NIBOR',
  SEK: 'STIBOR',
  CZK: 'PRIBOR',
  HUF: 'BUBOR',
  RON: 'ROBID-ROBOR',
  PLN: 'WIBOR',
  RUB: 'MOSPRIME',
  NZD: 'BKBM',
  MXN: 'TIIE',
  PEN: 'LIMABOR',
  ILS: 'TELBOR',
  TRY: 'TRYIBOR',
  ZAR: 'JIBAR',
};

// after 2021-12-04
export const usdGbpChfJpyMapper = {
  USD_NEW: 'USD SOFR',
  GBP_NEW: 'GBP SONIA',
  CHF_NEW: 'CHF SARON',
  JPY_NEW: 'JPY TONAR',
} as const;

export const mapCurrencyToReferenceRate = (currency: string, issueDate: Date) => {
  const usdGbpChfJpyEdgeDate = moment('2021-12-04');

  if (moment(issueDate).isAfter(usdGbpChfJpyEdgeDate, 'day') && `${currency}_NEW` in usdGbpChfJpyMapper) {
    return usdGbpChfJpyMapper[`${currency}_NEW` as keyof typeof usdGbpChfJpyMapper];
  }

  return currencyToReferenceRateMapper[currency as keyof typeof currencyToReferenceRateMapper];
};
