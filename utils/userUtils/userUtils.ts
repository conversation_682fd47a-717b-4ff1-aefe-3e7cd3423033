import { userRepository } from '../../repositories';
import { BadRequestError, ConflictError, InternalServerError } from '../ErrorHandler';

/**
 * If `username` is provided from the FE we just use that and throw if it already exists.
 * For Azure users `username` is not provided and is not going to be used by users directly.
 * So if no `username` is provided we use the first part of the email as username.
 */
export async function getUsername(username: string | undefined, usernameFromEmail: string) {
  if (username) return username;

  const user = await userRepository.getUser({ username: usernameFromEmail });
  if (!user) return usernameFromEmail;

  for (let i = 1; i < 1000; i++) {
    const newUsername = `${usernameFromEmail}${i}`;
    const user = await userRepository.getUser({ username: newUsername });
    if (!user) return newUsername;
  }

  throw new InternalServerError('Could not create unique username');
}

export async function checkEmailDomainMatchesClientDomain(
  domain: string,
  emailDomains: Array<string> | null,
  isSuperadmin: boolean,
  confirm: boolean,
): Promise<void> {
  if (!emailDomains) throw new BadRequestError('Client does not have email domains set.');
  if (!emailDomains.includes(domain) && !isSuperadmin) {
    throw new BadRequestError('Email domain does not match allowed email domains.');
  }
  if (!emailDomains.includes(domain) && !confirm) {
    throw new ConflictError('Email domain does not match allowed email domains.');
  }
}
