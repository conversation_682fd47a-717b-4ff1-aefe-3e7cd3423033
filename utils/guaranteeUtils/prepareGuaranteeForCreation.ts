import { calculateGuaranteeDataAlgorithm } from '../../controllers/algorithms/reportAlgorithms';
import { featureNames } from '../../enums';
import { checkCreateLimits, checkCombinedLoanGuaranteeLimit, checkIsUsingRegionalProviderData } from '../featureUtils';
import { checkCurrencyProviderDataAvailability } from '../reportUtils';
import { GuaranteeType, GuaranteeAlgorithmReturnType } from '../../types';

export const mutateGuaranteeObjectWithAlgorithmData = (
  guarantee: GuaranteeType,
  algorithmData: GuaranteeAlgorithmReturnType,
) => {
  guarantee.report = algorithmData.report;
  guarantee.calculationLog = algorithmData.calculationLog;
  guarantee.principal.creditRating.cumulativeProbabilityOfDefault = algorithmData.cumulativeProbabilityOfDefault;
};

export async function prepareGuaranteeForCreation(guarantee: GuaranteeType, clientId: number) {
  checkCurrencyProviderDataAvailability(guarantee);

  await checkCreateLimits({ clientId, featureName: featureNames.GUARANTEE_NUMBER, reportType: 'guarantee' });
  await checkCombinedLoanGuaranteeLimit({ clientId });

  const isUsingRegionalProviderData = await checkIsUsingRegionalProviderData(clientId);

  const algorithmData = await calculateGuaranteeDataAlgorithm({ ...guarantee, isUsingRegionalProviderData });

  mutateGuaranteeObjectWithAlgorithmData(guarantee, algorithmData);
}
