import _ from 'lodash';

import { countryRepository } from '../repositories';
import { CountryType, CountrySingletonType } from '../types';

let countries: Array<CountryType> | null = null;
let countriesByName: Record<string, CountryType> | null = null;

const getCountries = async (): Promise<CountrySingletonType> => {
  if (countries && countriesByName) {
    return { countries, countriesByName };
  }

  countries = await countryRepository.getCountries();
  countriesByName = _.keyBy(countries, 'name');

  return { countries, countriesByName } as never as CountrySingletonType;
};

export default getCountries;
