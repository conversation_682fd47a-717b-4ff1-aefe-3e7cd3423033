const _ = require('lodash');

const { featureNames } = require('../enums');
const { sequelize } = require('../models');
const {
  cashPoolRepository,
  cashPoolParticipantAccountRepository,
  topCurrencyAccountRepository,
  userRepository,
  participantAccountTrailsRepository,
  cashPoolParticipantTrailRepository,
  cashPoolBatchFileRepository,
  cashPoolLeaderBenefitRepository,
} = require('../repositories');
const {
  cashPoolService,
  cashPoolAccountService,
  cashPoolExportService,
  nordicCashPoolService,
} = require('../services/cashPoolService');
const azureService = require('../services/azureService');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const cashPoolUtils = require('../utils/cashPool');
const { getFilterBy: getTrailsFilterBy } = require('../utils/cashPoolParticipantTrail');
const { jsonToSheet } = require('../utils/documents');
const { NotFoundError, BadRequestError } = require('../utils/ErrorHandler');
const { checkCreateLimits } = require('../utils/featureUtils');
const logger = require('../utils/logger');
const { checkIsUsingRegionalProviderData } = require('../utils/featureUtils');
const { estimateParticipantRatesAlgorithm } = require('./algorithms/cashPoolAlgorithms');

async function getCashPool(req, res) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user;

  const getCashPoolPromise = cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  const getParticipantBenefitPromise = cashPoolParticipantTrailRepository.getTotalAccountBenefit({ cashPoolId });
  const [cashPool, participantsTotalBenefit] = await Promise.all([getCashPoolPromise, getParticipantBenefitPromise]);

  if (!cashPool) throw new NotFoundError('Cash pool');

  cashPool.dataValues.participantsTotalBenefit = participantsTotalBenefit ?? 0;
  cashPool.dataValues.leaderTotalBenefit = cashPool.grossBenefit - participantsTotalBenefit;

  res.json(cashPool);
}

async function getCashPools(req, res) {
  const { clientId } = req.user;

  const result = await cashPoolRepository.getCashPools({ where: { clientId } });

  res.json(result);
}

async function getCashPoolParticipants(req, res) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const { participants: participantsWithMeta } = await cashPoolRepository.getAllCashPoolParticipants({ cashPoolId });
  // Parsed for test to pass, so to not include dataValues in the mock
  const participants = JSON.parse(JSON.stringify(participantsWithMeta));

  for (const participant of participants) {
    participant.topCurrencyAccountIds = participant.accounts.map(({ topCurrencyAccount }) => topCurrencyAccount.id);
    delete participant.accounts;
    delete participant.company;
  }

  res.json(participants);
}

async function exportPhysicalCashPool(req, res) {
  const { type } = req.query;
  const { cashPoolId } = req.params;
  const { id: userId, clientId } = req.user;
  const { startDate, endDate } = req.body;
  const dateRange = { startDate, endDate };

  const userPromise = userRepository.getUserById(userId);
  const cashPoolPromise = cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

  const [user, cashPool] = await Promise.all([userPromise, cashPoolPromise]);

  if (!cashPool) throw new NotFoundError('Cash Pool');
  const cashPoolLeaderName = cashPool.leader.name;

  const [trailsFilter, companyFilter] = getTrailsFilterBy(req.body);

  const trails = await participantAccountTrailsRepository.getCashPoolTrails({
    cashPoolId,
    whereTrails: trailsFilter,
    whereCompany: companyFilter,
  });
  const leaderBenefit = await cashPoolLeaderBenefitRepository.getLeaderBenefit({ cashPoolId, ...trailsFilter });

  let filename = 'Cash Pool Export';
  if (Array.isArray(companyFilter.id) && companyFilter.id.length === 1) {
    const companyName = cashPool.participants.find((p) => p.companyId === companyFilter.id[0])?.company?.name;
    filename = `${companyName} - Cash Pool Export`;
  }

  let sheetData;

  if (type.toLowerCase() === 'consolidated') {
    sheetData = cashPoolExportService.getConsolidatedSheetData({
      cashPool,
      trails,
      user,
      dateRange,
      cashPoolLeaderName,
    });
  } else {
    sheetData = cashPoolExportService.getSheetData({ cashPool, trails, user, dateRange });
  }
  const leaderBenefitData = cashPoolExportService.getLeaderBenefitSheetData(leaderBenefit, cashPoolLeaderName);

  const result = jsonToSheet([...sheetData, ...leaderBenefitData]);
  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.set('Content-Disposition', `attachment; filename="${filename}.xlsx"`);
  return res.send(result);
}

async function exportNordicCashPool(req, res) {
  const { cashPoolId } = req.params;
  const { id: userId, clientId } = req.user;
  const { topCurrencyAccountId, startDate, endDate } = req.body;
  const dateRange = { startDate, endDate };

  const userPromise = userRepository.getUserById(userId);
  const cashPoolPromise = cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  const topCurrencyAccountPromise = topCurrencyAccountRepository.getTopCurrencyAccount({
    id: topCurrencyAccountId,
    cashPoolId,
  });

  const [user, cashPool, topCurrencyAccount] = await Promise.all([
    userPromise,
    cashPoolPromise,
    topCurrencyAccountPromise,
  ]);

  if (!cashPool) throw new NotFoundError('Cash Pool');
  if (!topCurrencyAccount) throw new NotFoundError('Top Currency Account');

  const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(req.body);

  const trails = await participantAccountTrailsRepository.getCashPoolTrails({
    cashPoolId,
    whereTrails: trailsFilter,
    whereCompany: companyFilter,
    whereTopCurrencyAccount: topCurrencyAccountFilter,
  });

  const sheetData = cashPoolExportService.getSheetData({ cashPool, topCurrencyAccount, trails, user, dateRange });

  const result = jsonToSheet(sheetData);
  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.set('Content-Disposition', 'attachment; filename="Cash Pool Export.xlsx"');
  return res.send(result);
}

async function createPhysicalCashPool(req, res) {
  const cashPool = req.body;
  const { clientId } = req.user;

  await checkCreateLimits({ clientId, featureName: featureNames.CASH_POOL_NUMBER, reportType: 'cashPool' });

  if (cashPool.name.includes('---')) {
    throw new BadRequestError('Cash pool name cannot contain "---"');
  }

  cashPoolUtils.runCashPoolCreateUpdateGuards(cashPool);

  await sequelize.transaction(async () => {
    const createdCashPool = await cashPoolRepository.createCashPool({ ...cashPool, clientId });

    const participants = cashPoolUtils.getCashPoolParticipants(cashPool, createdCashPool.id);

    const participantsByCompanyId = _.keyBy(participants, 'companyId');

    const createdParticipants = await cashPoolRepository.createCashPoolParticipants(participants);
    /**
     * Create one top currency account, different for nordic where there are multiple top currency accounts
     * or even multiple top currency accounts of the same currency (for example EUR, USD, HRK, HRK).
     * Physical doesn't have a top account (from the business perspective), but it's created to work
     * simultaneously and similarly for both physical and nordic.
     * Even though interestType and overnightRate are saved in the CashPool model for Physical it's also saved here
     * to be consistent with Nordic cash pools.
     */
    const [createdTopCurrencyAccount] = await topCurrencyAccountRepository.createTopCurrencyAccounts([
      {
        cashPoolId: createdCashPool.id,
        overnightRate: createdCashPool.overnightRate,
        name: `${createdCashPool.name}---TOP_CURRENCY_ACCOUNT_OF_PHYSICAL_CASHPOOL`,
        currency: createdCashPool.currencies,
        interestType: createdCashPool.interestType,
        creditInterestRate: createdCashPool.creditInterestRate,
        debitInterestRate: createdCashPool.debitInterestRate,
      },
    ]);

    /**
     * Creates accounts of participants by going over created participants (for participantId).
     * For each participant an account is created. Account has to have creditInterestRate (and debitInterestRate).
     * That check is included because the leader can also be an account in the cash pool but also doesn't have to.
     * Notional's and Nordic's participants can have multiple accounts while Physical's always have one.
     */
    await Promise.all(
      createdParticipants.map((participant) => {
        const participantAccount = participantsByCompanyId[participant.companyId];
        if (participantAccount.creditInterestRate == null) return;

        const account = {
          cashPoolParticipantId: participant.id,
          topCurrencyAccountId: createdTopCurrencyAccount.id,
          creditInterestRate: participantAccount.creditInterestRate,
          debitInterestRate: participantAccount.debitInterestRate,
          currency: createdCashPool.currencies,
        };

        return cashPoolParticipantAccountRepository.createCashPoolParticipantAccount(account);
      }),
    );

    res.json(createdCashPool);
  });
}

async function createNotionalCashPool(req, res) {
  const cashPool = req.body;
  const { clientId } = req.user;

  await checkCreateLimits({ clientId, featureName: featureNames.CASH_POOL_NUMBER, reportType: 'cashPool' });

  if (cashPool.name.includes('---')) {
    throw new BadRequestError('Cash pool name cannot contain "---"');
  }

  cashPoolUtils.runCashPoolCreateUpdateGuards(cashPool);

  await sequelize.transaction(async () => {
    const createdCashPool = await cashPoolRepository.createCashPool({ ...cashPool, clientId });

    const leaderParticipant = { cashPoolId: createdCashPool.id, companyId: cashPool.leaderId, isLeader: true };

    const participants = cashPool.accounts.map((account) => ({ ...account, cashPoolId: createdCashPool.id }));
    const participantsByCompanyId = _.keyBy(participants, 'companyId');

    const createdParticipants = await cashPoolRepository.createCashPoolParticipants([
      leaderParticipant,
      ...participants,
    ]);

    const [createdTopCurrencyAccount] = await topCurrencyAccountRepository.createTopCurrencyAccounts([
      {
        cashPoolId: createdCashPool.id,
        overnightRate: createdCashPool.overnightRate,
        name: `${createdCashPool.name}---TOP_CURRENCY_ACCOUNT_OF_NOTIONAL_CASHPOOL`,
        currency: createdCashPool.currencies,
        interestType: createdCashPool.interestType,
        creditInterestRate: createdCashPool.creditInterestRate,
        debitInterestRate: createdCashPool.debitInterestRate,
      },
    ]);

    await Promise.all(
      createdParticipants.map((participant) => {
        if (participant.isLeader) return;
        const participantAccount = participantsByCompanyId[participant.companyId];

        const account = {
          cashPoolParticipantId: participant.id,
          topCurrencyAccountId: createdTopCurrencyAccount.id,
          creditInterestRate: participantAccount.creditInterestRate,
          debitInterestRate: participantAccount.debitInterestRate,
          currency: participantAccount.currency,
        };

        return cashPoolParticipantAccountRepository.createCashPoolParticipantAccount(account);
      }),
    );

    res.json(createdCashPool);
  });
}

/**
 * 1. Create the cash pool
 * 2. Create participants based on companyIds in topCurrencyAccounts (along with the leader)
 * 3. Create top level accounts
 * 4. Create participant accounts and associate them with created participants and top level accounts
 */
async function createNordicCashPool(req, res) {
  const { body } = req;
  const { clientId } = req.user;
  const cashPool = _.omit(body, ['topCurrencyAccounts']);
  const { leaderId, topCurrencyAccounts } = body;

  await checkCreateLimits({ clientId, featureName: featureNames.CASH_POOL_NUMBER, reportType: 'cashPool' });

  for (const topCurrencyAccount of body.topCurrencyAccounts) {
    cashPoolUtils.runCashPoolCreateUpdateGuards(topCurrencyAccount);
  }
  if (_.uniqBy(topCurrencyAccounts, 'name').length !== topCurrencyAccounts.length) {
    throw new BadRequestError('Top currency accounts need to have unique names');
  }

  await sequelize.transaction(async () => {
    const createdCashPool = await cashPoolRepository.createCashPool({ ...cashPool, clientId });
    const cashPoolId = createdCashPool.id;

    // all companyIds in all top level accounts (may contain the companyId of the leader)
    const companyIds = _.uniq(
      _.flattenDeep(topCurrencyAccounts.map((topCurrencyAcc) => topCurrencyAcc.accounts.map((acc) => acc.companyId))),
    );

    const leaderParticipant = { cashPoolId, companyId: leaderId, isLeader: true };
    const participantsWithoutLeader = companyIds
      .filter((companyId) => companyId !== leaderId)
      .map((companyId) => ({ companyId, cashPoolId }));

    const createdParticipants = await cashPoolRepository.createCashPoolParticipants([
      leaderParticipant,
      ...participantsWithoutLeader,
    ]);
    const participantsByCompanyId = _.keyBy(
      createdParticipants.map(({ dataValues }) => dataValues),
      'companyId',
    );

    const topCurrencyAccountsMapped = topCurrencyAccounts.map((topCurrencyAcc) => ({
      cashPoolId: createdCashPool.id,
      ...topCurrencyAcc,
    }));
    const createdTopCurrencyAccounts = await topCurrencyAccountRepository.createTopCurrencyAccounts(
      topCurrencyAccountsMapped,
    );

    const topAccountsByName = _.keyBy(
      createdTopCurrencyAccounts.map(({ dataValues }) => dataValues),
      'name',
    );

    const accountsToCreate = [];
    for (const topCurrencyAccount of topCurrencyAccountsMapped) {
      const accounts = topCurrencyAccount.accounts.map((account) => ({
        ...account,
        cashPoolParticipantId: participantsByCompanyId[account.companyId].id,
        topCurrencyAccountId: topAccountsByName[topCurrencyAccount.name].id,
        currency: topCurrencyAccount.currency,
      }));
      accountsToCreate.push(...accounts);
    }

    await cashPoolParticipantAccountRepository.createCashPoolParticipantAccounts(accountsToCreate);

    res.json(createdCashPool);
  });
}

async function updateEntirePhysicalCashPool(req, res) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user;
  const { body } = req;

  if (body.name.includes('---')) throw new BadRequestError('Cash pool name cannot contain "---"');

  cashPoolUtils.runCashPoolCreateUpdateGuards(body);

  await sequelize.transaction(async () => {
    const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

    if (!cashPool) throw new NotFoundError('Cash pool');

    const {
      accounts,
      id: topCurrencyAccountId,
      currency: topCurrencyAccountCurrency,
    } = cashPool.topCurrencyAccounts[0];
    const accountsWithCompanyId = accounts.map(({ dataValues }) => ({
      accountId: dataValues.id,
      companyId: dataValues.participant.companyId,
    }));

    const allAccounts = accountsWithCompanyId; // all accounts in this specific top currency account (there is only one because it's physical) from the database
    const requestAccounts = body.accounts; // all accounts of this top currency account (there is only one because it's physical) and of this cash pool

    if (body.shouldCreateAuditTrail) {
      await cashPoolService.createPhysicalNotionalCashPoolAuditTrail(cashPool.dataValues, allAccounts, clientId);
    }

    await cashPoolAccountService.handleAccounts({
      cashPoolId,
      requestAccounts,
      allAccounts,
      topCurrencyAccountId,
      topCurrencyAccountCurrency,
      leaderCompanyId: cashPool.leaderId,
    });

    const updatedCashPool = await cashPoolService.updateCashPoolAndTopCurrencyAccount({
      cashPoolId,
      clientId,
      topCurrencyAccountId,
      body,
    });

    return res.json(updatedCashPool);
  });
}

async function updateEntireNotionalCashPool(req, res) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user;
  const { body } = req;

  if (body.name.includes('---')) throw new BadRequestError('Cash pool name cannot contain "---"');

  cashPoolUtils.runCashPoolCreateUpdateGuards(body);

  await sequelize.transaction(async () => {
    const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

    if (!cashPool) throw new NotFoundError('Cash pool');

    const {
      accounts,
      id: topCurrencyAccountId,
      currency: topCurrencyAccountCurrency,
    } = cashPool.topCurrencyAccounts[0];
    const accountsWithCompanyId = accounts.map(({ dataValues }) => ({
      accountId: dataValues.id,
      companyId: dataValues.participant.companyId,
    }));

    const allAccounts = accountsWithCompanyId; // all accounts in this specific top currency account (there is only one because it's notional) from the database
    const requestAccounts = body.accounts; // all accounts of this top currency account (there is only one because it's notional) and of this cash pool

    if (body.shouldCreateAuditTrail) {
      await cashPoolService.createPhysicalNotionalCashPoolAuditTrail(cashPool.dataValues, allAccounts, clientId);
    }

    await cashPoolAccountService.handleAccounts({
      cashPoolId,
      requestAccounts,
      allAccounts,
      topCurrencyAccountId,
      topCurrencyAccountCurrency,
    });

    const updatedCashPool = await cashPoolService.updateCashPoolAndTopCurrencyAccount({
      cashPoolId,
      clientId,
      topCurrencyAccountId,
      body,
    });

    return res.json(updatedCashPool);
  });
}

async function updateEntireNordicCashPool(req, res) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user;
  const { body } = req;

  for (const topCurrencyAccount of body.topCurrencyAccounts) {
    cashPoolUtils.runCashPoolCreateUpdateGuards(topCurrencyAccount);
  }

  await sequelize.transaction(async () => {
    const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

    if (!cashPool) throw new NotFoundError('Cash pool');

    const leaderCompanyId = cashPool.leader.id;
    const currentTopCurrencyAccounts = cashPool.topCurrencyAccounts;
    const requestTopCurrencyAccounts = body.topCurrencyAccounts;
    const existingParticipantsByCompanyId = _.keyBy(
      cashPool.participants.map(({ id, companyId }) => ({ cashPoolParticipantId: id, companyId })),
      'companyId',
    );

    if (body.shouldCreateAuditTrail) {
      await cashPoolService.createNordicCashPoolAuditTrail(cashPool.dataValues, currentTopCurrencyAccounts, clientId);
    }

    await nordicCashPoolService.deleteTopCurrencyAccounts({
      cashPoolId,
      currentTopCurrencyAccounts,
      requestTopCurrencyAccounts,
    });

    for (const requestTopCurrencyAccount of requestTopCurrencyAccounts) {
      const { currentTopCurrencyAccount, isCreated } =
        await nordicCashPoolService.getExistingOrCreateTopCurrencyAccount({
          currentTopCurrencyAccounts,
          requestTopCurrencyAccount,
          cashPoolId,
        });

      if (isCreated) {
        await nordicCashPoolService.createNewTopCurrencyAccountParticipantsAndAccounts({
          currentTopCurrencyAccount,
          existingParticipantsByCompanyId,
          cashPoolId,
        });
        continue;
      }

      const { accounts, id: topCurrencyAccountId, currency: topCurrencyAccountCurrency } = currentTopCurrencyAccount;
      const accountsWithCompanyId = accounts.map(({ dataValues }) => ({
        accountId: dataValues.id,
        companyId: dataValues.participant.companyId,
      }));
      const allAccounts = accountsWithCompanyId; // all accounts in this specific top currency account from the database
      const requestAccounts = requestTopCurrencyAccount.accounts; // all accounts in this specific top currency account from the request

      await cashPoolAccountService.handleAccounts({
        cashPoolId,
        requestAccounts,
        allAccounts,
        topCurrencyAccountId,
        topCurrencyAccountCurrency,
        leaderCompanyId,
      });

      const topAccountToUpdate = _.pick(requestTopCurrencyAccount, ['name', 'creditInterestRate', 'debitInterestRate']);
      await topCurrencyAccountRepository.updateTopCurrencyAccount(topCurrencyAccountId, topAccountToUpdate);
    }

    const [, [updatedCashPool]] = await cashPoolRepository.updateCashPool({
      where: { id: cashPoolId, clientId },
      attributesToUpdate: body,
    });

    return res.json(updatedCashPool);
  });
}

async function updateCashPoolNote(req, res) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user;
  const { body } = req;

  const [affectedRows, [updatedCashPool]] = await cashPoolRepository.updateCashPool({
    where: { id: cashPoolId, clientId },
    attributesToUpdate: body,
  });

  if (affectedRows === 0) {
    throw new NotFoundError('Cash pool');
  }

  return res.json(updatedCashPool);
}

async function deleteCashPool(req, res) {
  const { clientId } = req.user;
  const { cashPoolId } = req.params;

  await sequelize.transaction(async () => {
    const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
    if (!cashPool) {
      throw new NotFoundError('Cash pool');
    }

    const batchFiles = await cashPoolBatchFileRepository.getAllCashPoolBatchFiles({ clientId, cashPoolId });
    const batchFileIds = batchFiles.map((batchFile) => batchFile.id);

    await cashPoolRepository.deleteCashPool(cashPoolId, clientId);

    await Promise.all(batchFileIds.map((id) => azureService.deleteFile(`cashPoolBatch/${id}`))).catch((error) => {
      logger.error({ message: 'Error while deleting cash pool batch files', error });
    });
    res.status(204).send();
  });
}

async function estimateParticipantRates(req, res) {
  const estimateRequest = req.body;

  const isUsingRegionalProviderData = await checkIsUsingRegionalProviderData(req.user.clientId);

  const participantsRates = await estimateParticipantRatesAlgorithm(
    {
      ...estimateRequest,
      isUsingRegionalProviderData,
    },
    req.user.clientName,
  );

  res.json(participantsRates);
}

async function exportAll(req, res) {
  const { id: userId, clientId } = req.user;

  const cashPools = await cashPoolRepository.getCashPools({ where: { clientId } });

  if (!cashPools) throw new NotFoundError('Cash Pool');

  const allTrails = [];

  for (const cashPool of cashPools) {
    const companies = await cashPoolRepository.getAllCashPoolParticipants({ cashPoolId: cashPool.id });
    const companyIds = companies.participants.map((company) => company.companyId);

    const trails = await participantAccountTrailsRepository.getCashPoolTrails({
      cashPoolId: cashPool.id,
      whereCompany: { id: companyIds },
    });

    const trailsWithLeader = trails.map((trail) => {
      trail.cashPoolName = cashPool.name;
      trail.cashPoolLeaderName = cashPool.leader.name;
      return trail;
    });

    allTrails.push(...trailsWithLeader);
  }

  const sheetData = cashPoolExportService.getAllCashPoolsSheetData({
    trails: allTrails.sort((a, b) => new Date(a.date) - new Date(b.date)),
  });

  const result = jsonToSheet(sheetData);
  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.set('Content-Disposition', 'attachment; filename="Cash Pools Export.xlsx"');
  return res.send(result);
}

module.exports = {
  getCashPool: asyncControllerWrapper(getCashPool),
  getCashPoolParticipants: asyncControllerWrapper(getCashPoolParticipants),
  exportPhysicalCashPool: asyncControllerWrapper(exportPhysicalCashPool),
  exportNordicCashPool: asyncControllerWrapper(exportNordicCashPool),
  createPhysicalCashPool: asyncControllerWrapper(createPhysicalCashPool),
  createNordicCashPool: asyncControllerWrapper(createNordicCashPool),
  createNotionalCashPool: asyncControllerWrapper(createNotionalCashPool),
  getCashPools: asyncControllerWrapper(getCashPools),
  updateCashPoolNote: asyncControllerWrapper(updateCashPoolNote),
  updateEntirePhysicalCashPool: asyncControllerWrapper(updateEntirePhysicalCashPool),
  updateEntireNordicCashPool: asyncControllerWrapper(updateEntireNordicCashPool),
  updateEntireNotionalCashPool: asyncControllerWrapper(updateEntireNotionalCashPool),
  deleteCashPool: asyncControllerWrapper(deleteCashPool),
  estimateParticipantRates: asyncControllerWrapper(estimateParticipantRates),
  exportAll: asyncControllerWrapper(exportAll),
};
