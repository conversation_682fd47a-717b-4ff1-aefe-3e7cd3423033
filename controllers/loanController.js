const { Buffer } = require('buffer');
const _ = require('lodash');
const { Op } = require('sequelize');

const { featureNames, rolesEnum, reportEnums, dayCountEnums } = require('../enums');
const { sequelize } = require('../models');
const {
  featureRepository,
  loanRepository,
  paymentRepository,
  whtPaymentRepository,
  clientRepository,
} = require('../repositories');
const repaymentService = require('../services');
const azureService = require('../services/azureService');
const redisService = require('../services/redis');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const {
  BadRequestError,
  NotFoundError,
  ForbiddenError,
  FinalizeUneditableError,
  MarkAsDraftUneditableError,
  InternalServerError,
} = require('../utils/ErrorHandler');
const featureUtils = require('../utils/featureUtils');
const loanUtils = require('../utils/loanUtils');
const paymentUtils = require('../utils/payments/paymentUtils');
const reportUtils = require('../utils/reportUtils');
const { calculateLoanDataAlgorithm } = require('./algorithms/reportAlgorithms');

const orderByCreationDate = [['createdAt', 'DESC']];
const orderByDeleteDate = [['deletedAt', 'DESC']];

async function getLoans(req, res) {
  const { limit, isPortfolio } = req.query;
  const loans = await loanRepository.getLoans({
    where: {
      clientId: req.user.clientId,
      isPortfolio: isPortfolio === 'true',
    },
    order: orderByCreationDate,
    limit: isNaN(limit) ? null : parseInt(limit),
  });
  res.json(loans);
}

async function getDeletedLoans(req, res) {
  const loans = await loanRepository.getLoans({
    where: {
      clientId: req.user.clientId,
      deletedAt: { [Op.ne]: null },
    },
    order: orderByDeleteDate,
    paranoid: false,
  });
  res.json(loans);
}

async function getLoan(req, res) {
  const loan = await loanRepository.getLoan(req.params.id, req.user.clientId);

  if (!loan) {
    throw new NotFoundError('Loan');
  }

  res.json(loan);
}

function getImportTemplate(req, res) {
  res.set('Content-Type', 'application/vnd.ms-excel');
  res.download('./static/loan_import_template.xlsx');
}

function getUploadTemplate(req, res) {
  res.set('Content-Type', 'application/vnd.ms-excel');
  res.download('./static/loan_upload_template.xlsx');
}

async function postLoan(req, res) {
  const loan = req.body;
  const { clientId } = req.user;

  const { isLoanApproachCalculated } = await clientRepository.getClient(clientId);

  await loanUtils.prepareLoanForCreation(loan, clientId);

  res.json(await loanRepository.createLoan({ ...loan, isApproachCalculated: isLoanApproachCalculated }, req.user));
}

async function postLoans(req, res) {
  const loans = req.body;
  const { clientId } = req.user;

  const { isLoanApproachCalculated } = await clientRepository.getClient(clientId);

  await sequelize.transaction(async () => {
    await Promise.all(loans.map((loan) => loanUtils.prepareLoanForCreation(loan, clientId)));

    await Promise.all(
      loans.map((loan) =>
        loanRepository.createLoan({ ...loan, isApproachCalculated: isLoanApproachCalculated }, req.user),
      ),
    );

    res.status(204).send();
  });
}

async function runAlgorithm(req, res) {
  const loan = req.body;
  const { clientId } = req.user;
  const { id } = req.params;

  const isUsingRegionalProviderData = await featureUtils.checkIsUsingRegionalProviderData(clientId);

  const oldLoan = await loanRepository.getLoan(id, clientId);
  if (!oldLoan) throw new NotFoundError('Loan');

  const { report, calculationLog, pricingApproach } = await calculateLoanDataAlgorithm({
    ...loan,
    clientId,
    isUsingRegionalProviderData,
    isApproachCalculated: oldLoan.isApproachCalculated,
  });

  const oneDaySeconds = 86400;

  const result = await redisService.setex(
    reportUtils.getCalculationLogRedisKey(reportEnums.REPORT_TYPES.LOAN, id),
    oneDaySeconds,
    JSON.stringify(calculationLog),
  );

  if (result !== 'OK') {
    throw new InternalServerError('Saving to redis failed');
  }

  res.json({ report, pricingApproach });
}

async function postImportedLoan(req, res) {
  const loan = req.body;

  loan.borrower.creditRating = loanUtils.overrideBorrowerCreditRating(loan?.borrower?.creditRating);

  const defaultDayCount = dayCountEnums.dayCountMapper['ACT/365'];

  res.json(await loanRepository.createLoan({ ...loan, dayCount: defaultDayCount }, req.user));
}

async function postImportedLoans(req, res) {
  const loans = req.body;

  const defaultDayCount = dayCountEnums.dayCountMapper['ACT/365'];

  await sequelize.transaction(async () => {
    const createLoanPromises = loans.map((loan) => {
      loan.borrower.creditRating = loanUtils.overrideBorrowerCreditRating(loan.borrower.creditRating);
      return loanRepository.createLoan({ ...loan, dayCount: defaultDayCount }, req.user);
    });

    await Promise.all(createLoanPromises);
    res.status(204).send();
  });
}

async function putLoanRates(req, res) {
  const { id } = req.params;
  const { body } = req;

  const loan = (await loanRepository.getLoan(id, req.user.clientId, null))?.dataValues;
  if (!loan) throw new NotFoundError('Loan');
  if (loan.isPortfolio) throw new BadRequestError('Loan must be in analyses for rate changing');

  loan.report = { ...loan.report, ...body };

  res.json(await loanRepository.updateLoan(id, loan, req.user));
}

async function putLoan(req, res) {
  const id = req.params.id;
  const loan = req.body;
  const { clientId } = req.user;

  const [oldLoan, clientFeature] = await Promise.all([
    loanRepository.getLoan(id, clientId, null),
    featureRepository.getClientFeatureByName({ clientId, featureName: 'loanNumber' }),
  ]);

  reportUtils.runReportUpdateGuards(oldLoan, 'loan');
  reportUtils.issueDateUpdateCheck(oldLoan, loan, clientFeature);
  reportUtils.checkCurrencyProviderDataAvailability(loan);

  const isUsingRegionalProviderData = await featureUtils.checkIsUsingRegionalProviderData(clientId);

  const { report, calculationLog, pricingApproach } = await calculateLoanDataAlgorithm({
    ...loan,
    clientId,
    isUsingRegionalProviderData,
    isApproachCalculated: oldLoan.isApproachCalculated,
  });
  loan.report = { ...loan.report, ...report };
  loan.calculationLog = calculationLog;
  loan.pricingApproach = pricingApproach;

  // Initialize referenceRate
  if (loan.rateType.type === 'float') {
    loan.rateType = {
      ...loan.rateType,
      ...loanUtils.getReferenceRate(loan.currency, loan.paymentFrequency, loan.issueDate),
    };
  }

  /**
   * lender and borrower aren't changeable after the loan is created if there is a limit on number of loans.
   * That is if the loanNumber feature isEnabled is set to true. If isEnabled is false there is no limit on number
   * of loans and all fields can be updated.
   */
  const updatableLoan = clientFeature.isEnabled ? _.omit(loan, ['lender', 'borrower']) : loan;
  res.json(await loanRepository.updateLoan(id, updatableLoan, req.user));
}

async function putLoanSaveUpdated(req, res) {
  const { clientId } = req.user;
  const { id } = req.params;
  const loan = req.body;

  const oldLoan = await loanRepository.getLoan(id, clientId, null);

  reportUtils.runReportUpdateGuards(oldLoan, 'loan');

  const { isWhtEnabled, whtInterestRate, approach } = loan.report;

  const calculationLog = await redisService.get(
    reportUtils.getCalculationLogRedisKey(reportEnums.REPORT_TYPES.LOAN, id),
  );

  if (!calculationLog) {
    throw new NotFoundError('Calculation log');
  }

  await sequelize.transaction(async () => {
    const paymentsLoanObj = { ...oldLoan.dataValues, ...loan };
    const [totalInterest, loanPayments, whtPayments] = await repaymentService.getPayments({
      report: paymentsLoanObj,
      finalInterestRate: loan.report.finalInterestRate,
      reportIdKey: 'loanId',
      basisPoints: loan.report.finalInterestRate,
      estimationOfReferenceRate: null,
      isWhtEnabled,
      whtInterestRate,
      approach,
    });
    loan.totalInterest = totalInterest;
    const createdPayments = await paymentRepository.createPayments(loanPayments, oldLoan.type);
    if (isWhtEnabled && whtInterestRate !== 0) {
      paymentUtils.addPaymentIdsToWHTPayments(createdPayments, whtPayments);
      await whtPaymentRepository.createWHTPayments(whtPayments);
    }
    const loanWithCalculationLog = { ...loan, isPortfolio: true, calculationLog: JSON.parse(calculationLog) };

    return res.json(await loanRepository.updateLoan(id, loanWithCalculationLog, req.user));
  });
}

async function putImportedLoan(req, res) {
  const id = req.params.id;
  const loan = req.body;

  const oldLoan = await loanRepository.getLoan(id, req.user.clientId, null);

  reportUtils.runReportUpdateGuards(oldLoan, 'loan');

  loan.borrower.creditRating = loanUtils.overrideBorrowerCreditRating(loan?.borrower?.creditRating);
  res.json(await loanRepository.updateLoan(id, loan, req.user));
}

async function putLoanStatus(req, res) {
  const id = req.params.id;
  const { status } = req.body;

  const loan = (await loanRepository.getLoan(id, req.user.clientId, null))?.dataValues;

  if (!loan) {
    throw new NotFoundError('Loan');
  }

  if (!loan.editable) {
    if (status === 'Draft') {
      throw new MarkAsDraftUneditableError('loan');
    }
    throw new FinalizeUneditableError('loan');
  }

  loan.status = status;
  if (status === 'Final') {
    loan.finalizedBy = req.user?.username;
  } else {
    loan.finalizedBy = null;
  }

  res.json(await loanRepository.updateLoan(id, loan, req.user));
}

async function putLoanNote(req, res) {
  const id = req.params.id;
  const { note } = req.body;

  const loan = (await loanRepository.getLoan(id, req.user.clientId, null))?.dataValues;

  if (!loan) throw new NotFoundError('Loan');
  if (loan.status === 'Final') throw new BadRequestError('Cannot update note of finalized loan.');

  loan.note = note;
  res.json(await loanRepository.updateLoan(id, loan, req.user));
}

async function generateAgreement(req, res) {
  const { clientId } = req.user;
  const loanId = req.params.id;
  const loan = (await loanRepository.getLoan(loanId, req.user.clientId, null))?.dataValues;

  if (!loan) {
    throw new NotFoundError('Loan');
  }

  if (reportUtils.checkIsImported(loan.report)) {
    throw new BadRequestError("Can't generate agreement for imported loan");
  }

  if (loan.files.find(({ dataValues }) => dataValues.label === 'Agreement' && dataValues.isGenerated)) {
    throw new BadRequestError('Agreement already exists');
  }

  const isUsingRegionalProviderData = await featureUtils.checkIsUsingRegionalProviderData(clientId);

  const templateData = await loanUtils.getTemplateData(loan, clientId, isUsingRegionalProviderData);

  await sequelize.transaction(async () => {
    const [agreementFile, agreementMetadata] = await loanUtils.generateAgreement(loan, clientId, templateData);
    loan.files = [agreementMetadata];
    const { dataValues } = await loanRepository.updateLoanAndCreateFiles(loanId, loan, req.user);

    const fileId = dataValues.id;
    await azureService.uploadFile(`loan/${fileId}`, agreementFile, Buffer.byteLength(agreementFile));

    res.json(dataValues);
  });
}

async function generateTpReport(req, res) {
  const { clientId } = req.user;
  const loanId = req.params.id;
  const loan = (await loanRepository.getLoan(loanId, clientId, null))?.dataValues;

  if (!loan) {
    throw new NotFoundError('Loan');
  }

  if (reportUtils.checkIsImported(loan.report)) {
    throw new BadRequestError("Can't generate TP report for imported loan");
  }

  if (loan.files.find(({ dataValues }) => dataValues.label === 'TP Report' && dataValues.isGenerated)) {
    throw new BadRequestError('TP report already exists');
  }

  const isUsingRegionalProviderData = await featureUtils.checkIsUsingRegionalProviderData(clientId);

  const templateData = await loanUtils.getTemplateData(loan, clientId, isUsingRegionalProviderData);

  await sequelize.transaction(async () => {
    const [tpReportFile, tpReportMetadata] = await loanUtils.generateTpReport(loan, clientId, templateData);
    loan.files = [tpReportMetadata];
    const { dataValues } = await loanRepository.updateLoanAndCreateFiles(loanId, loan, req.user);

    const fileId = dataValues.id;
    await azureService.uploadFile(`loan/${fileId}`, tpReportFile, Buffer.byteLength(tpReportFile));
    res.json(dataValues);
  });
}

/**
 * Used for moving to/from analyses/portfolio
 * When moving to portfolio (isPortfolio === true) we also calculate
 * the payments for the loan and save it in Payments table
 * When moving to Analyses we delete the payments since they will
 * have to be recalculated, also set totalInterest to null
 */
async function putLoanIsPortfolio(req, res) {
  const { id } = req.params;
  const { isPortfolio } = req.body;

  const loan = (await loanRepository.getLoan(id, req.user.clientId, null))?.dataValues;
  const finalInterestRate = loan.report.finalInterestRate;
  const { isWhtEnabled, whtInterestRate, approach } = loan.report;

  reportUtils.runReportUpdateGuards(loan, 'loan');

  await sequelize.transaction(async () => {
    if (!isPortfolio) {
      loan.movedToAnalysesDate = new Date();
      loan.totalInterest = null;
      await paymentRepository.deletePayments({ loanId: id });
      await whtPaymentRepository.deletePayments({ loanId: id });
    }
    if (isPortfolio) {
      const [totalInterest, payments, whtPayments] = await repaymentService.getPayments({
        report: loan,
        finalInterestRate,
        reportIdKey: 'loanId',
        basisPoints: finalInterestRate,
        estimationOfReferenceRate: null,
        isWhtEnabled,
        whtInterestRate,
        approach,
      });
      loan.totalInterest = totalInterest;
      const createdPayments = await paymentRepository.createPayments(payments, loan.type);
      if (isWhtEnabled && whtInterestRate !== 0) {
        paymentUtils.addPaymentIdsToWHTPayments(createdPayments, whtPayments, loan.type);
        await whtPaymentRepository.createWHTPayments(whtPayments);
      }
    }

    loan.isPortfolio = isPortfolio;

    await loanRepository.updateLoan(id, loan, req.user, false);
    return res.status(204).send();
  });
}

/* Anyone can delete loan while in analyses. After moving to portfolio only admins can. */
async function deleteLoan(req, res) {
  const { role, clientId } = req.user;
  const force = req.query.force === 'true';
  const loanId = req.params.id;
  const isUser = role === rolesEnum.USER;

  const loan = await loanRepository.getLoan(loanId, clientId);

  if (!loan) throw new NotFoundError('Loan');
  const feature = await featureRepository.getClientFeatureByName({
    clientId,
    featureName: featureNames.LOAN_NUMBER,
  });
  if (loan.isPortfolio && isUser) throw new ForbiddenError('Only admins can delete a loan in portfolio');
  if (force && isUser) throw new ForbiddenError('Only admins can permanently delete loans');
  if (feature.isEnabled && force) throw new ForbiddenError('Cannot permanently delete loan.');

  await sequelize.transaction(async () => {
    if (force) await Promise.all(loan.files.map((file) => azureService.deleteFile(`loan/${file.id}`)));

    await loanRepository.deleteLoan(loanId, clientId, force);
  });

  res.status(204).send();
}

async function restoreLoan(req, res) {
  const { role, clientId } = req.user;
  const loanId = req.params.id;
  const loan = await loanRepository.getLoan(loanId, clientId);
  const isUser = role === rolesEnum.USER;

  if (!loan) throw new NotFoundError('Loan');
  if (isUser) throw new ForbiddenError('Only admins can restore a loan');

  await loanRepository.restoreLoan(loanId, clientId);

  res.status(204).send();
}

module.exports = {
  getLoans: asyncControllerWrapper(getLoans),
  getDeletedLoans: asyncControllerWrapper(getDeletedLoans),
  getLoan: asyncControllerWrapper(getLoan),
  getUploadTemplate: asyncControllerWrapper(getUploadTemplate),
  getImportTemplate: asyncControllerWrapper(getImportTemplate),
  postLoan: asyncControllerWrapper(postLoan),
  postLoans: asyncControllerWrapper(postLoans),
  runAlgorithm: asyncControllerWrapper(runAlgorithm),
  postImportedLoan: asyncControllerWrapper(postImportedLoan),
  postImportedLoans: asyncControllerWrapper(postImportedLoans),
  putLoan: asyncControllerWrapper(putLoan),
  putLoanSaveUpdated: asyncControllerWrapper(putLoanSaveUpdated),
  putImportedLoan: asyncControllerWrapper(putImportedLoan),
  putLoanStatus: asyncControllerWrapper(putLoanStatus),
  putLoanNote: asyncControllerWrapper(putLoanNote),
  putLoanRates: asyncControllerWrapper(putLoanRates),
  generateAgreement: asyncControllerWrapper(generateAgreement),
  putLoanIsPortfolio: asyncControllerWrapper(putLoanIsPortfolio),
  generateTpReport: asyncControllerWrapper(generateTpReport),
  deleteLoan: asyncControllerWrapper(deleteLoan),
  restoreLoan: asyncControllerWrapper(restoreLoan),
};
