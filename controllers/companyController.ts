import { Request, Response, NextFunction } from 'express';
import _ from 'lodash';
import { literal } from 'sequelize';

import models from '../models';
import companyRepository from '../repositories/companyRepository';
import creditRatingRepository from '../repositories/creditRatingRepository';
import { BadRequestError, NotFoundError, InternalServerError } from '../utils/ErrorHandler';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import companyUtils from '../utils/companyUtils';
import { CompanyType } from '../types';

const { sequelize } = models;

type CreditRatingAdjustedType = { ratingAdj: number; probabilityOfDefaultAdj?: number };

async function getCompanies(req: Request, res: Response) {
  const companies = await companyRepository.getCompanies({ clientId: req.user?.clientId });
  if (companies.length !== 0) {
    const parentCompany = companies.find(
      (company: { dataValues: CompanyType }) => company.dataValues.parentCompanyId === company.dataValues.id,
    );
    if (parentCompany) {
      companies.splice(companies.indexOf(parentCompany), 1);
      companies.unshift(parentCompany);
    }
  }
  res.json(companies);
}

async function getCompany(req: Request, res: Response) {
  const id = req?.params?.id;
  const company = await companyRepository.getCompanyById(id, req.user?.clientId);
  if (!company) throw new NotFoundError('Company');

  company.availableCreditRatings = await creditRatingRepository.getCompanyCreditRatings(id);
  res.json(company);
}

async function getTemplate(_req: Request, res: Response) {
  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.download('./static/group_information_template.xlsx');
}

async function putCompany(req: Request, res: Response) {
  const companyId = parseInt(req.params.id);
  const { isParent, createAuditTrail, parentCompanyId, ...data } = req.body;
  const { clientId, username } = req.user!;

  const companyExists = await companyRepository.companyExists(companyId, clientId);
  if (!companyExists) throw new NotFoundError('Company');

  const company = await sequelize.transaction(async () => {
    const parentFlag = companyId === parentCompanyId;
    const isCompanyBecomingParent = isParent && !parentFlag;
    const isCompanyNoLongerParent = !isParent && parentFlag;

    let newParentCompanyId = undefined;
    if (isCompanyBecomingParent) {
      newParentCompanyId = companyId;
      data.creditRating.ratingAdj = null;
      data.assessment = null;
    }
    if (isCompanyNoLongerParent) {
      newParentCompanyId = null;
    }
    const isParentChanging = newParentCompanyId !== undefined; // important strict equal

    /**
     * Updates all client's companies if parent company is changed or unset
     * That's why `isParentChanging` does strict equal for undefined,
     * because `null` as a `newParentCompanyId` is unsetting the parent company
     */
    if (isParentChanging) {
      await companyRepository.updateCompany(
        { clientId, parentCompanyId },
        { parentCompanyId: newParentCompanyId },
        false,
        false,
        username,
      );
    }
    const parentCompany = await companyRepository.getCompanyById(
      isParentChanging ? newParentCompanyId : parentCompanyId,
      clientId,
    );

    const creditRatingAdj: Partial<CreditRatingAdjustedType> = {};
    if (parentCompany?.creditRating?.rating && data.assessment) {
      creditRatingAdj.ratingAdj = companyUtils.adjustCreditRating(
        data.creditRating.rating,
        parentCompany.creditRating.rating,
        data.assessment.name,
        data.assessment.answers.ringFencing,
      );
    }
    if (data.creditRating.probabilityOfDefault != null) {
      creditRatingAdj.probabilityOfDefaultAdj = companyUtils.estimateProbabilityOfDefaultAdj(
        data.creditRating.probabilityOfDefault,
        data.creditRating.rating,
        creditRatingAdj?.ratingAdj,
      );
    }

    // Update the company
    const company = (
      await companyRepository.updateCompany(
        { id: companyId },
        {
          ...data,
          parentCompanyId: isParentChanging ? newParentCompanyId : parentCompanyId,
          creditRating: { ...data.creditRating, ...creditRatingAdj },
        },
        createAuditTrail,
        true,
        username,
      )
    )[1][0];
    const dataValues = company.dataValues;

    // Calculate new adjusted ratings for the child companies
    if (dataValues.id === dataValues.parentCompanyId && data.creditRating) {
      const childCompanies = await companyRepository.getCompanies({ parentCompanyId: companyId });
      for (let i = 0, len = childCompanies.length; i < len; i++) {
        const childCompany = childCompanies[i].dataValues;
        const { id, creditRating, assessment } = childCompany;
        if (assessment) {
          const newAdjustedRating = companyUtils.adjustCreditRating(
            creditRating.rating,
            dataValues.creditRating.rating,
            assessment.name,
            assessment.answers.ringFencing,
          );

          if (newAdjustedRating !== creditRating.ratingAdj)
            await companyRepository.updateCompany(
              { id },
              {
                creditRating: {
                  ...creditRating,
                  ratingAdj: newAdjustedRating,
                },
              },
              createAuditTrail,
              false,
              username,
            );
        }
      }
    }

    return company;
  });

  res.json(company);
}

async function deleteCompany(req: Request, res: Response) {
  try {
    await companyRepository.deleteCompany(req.params.id, req.user?.clientId);
    res.status(204).send();
  } catch (err: any) {
    if (err?.original?.code === '23503') {
      if (err?.original?.constraint === 'Cash_Pool_Participants_companyId_fkey') {
        throw new BadRequestError('Cannot delete company that is a cash pool participant.');
      }
      if (err?.original?.constraint === 'CashPools_leaderId_fkey') {
        throw new BadRequestError('Cannot delete company that is a cash pool leader.');
      }
    }
    throw err;
  }
}

async function postCompany(req: Request, res: Response) {
  const { isParent, ...data } = req.body;
  const company = await sequelize.transaction(async () => {
    const company = await companyRepository.createCompany(data, req.user);

    if (isParent) {
      await companyRepository.updateCompany(
        { clientId: req.user?.clientId },
        { parentCompanyId: company.dataValues.id },
        false,
        false,
        null,
      );
      company.parentCompanyId = company.id;
    }

    return company;
  });

  res.json(company);
}

/**
 * This endpoint effectively combines the `postCompany`, `implicitSupport` and `adjustCreditRating` endpoints.
 * After getting the parent company it first checks there are no companies with the same name.
 * Then it makes sure that all parent companies exist.
 * `parentCompanyName` can either by empty or contain a name of a company. If it's empty it's assumed that the default
 * parent company is the parent. Same goes if the name of the parent company is explicity written.
 *
 * The iterative for loop with ARBITRARY_LIMIT is there to retry to create companies that were not created because
 * their parent company was not created yet. For example the first company that should be created is `Company A` whose
 * leader is `Company B`. But `Company B` is the next company to be created. So the first iteration will skip `Company A`
 * and create `Company B`. Then the second iteration will create `Company A` after `Company B` was created.
 * Map `seenCompanies` is used to keep track of which companies were already created.
 * Code exits the loop once the number of elements in `seenCompanies` is the same as the number of companies that came in the req.body.
 */
async function postCompanies(req: Request, res: Response) {
  const companies = req.body;
  const clientId = req.user?.clientId;

  await sequelize.transaction(async () => {
    const parentCompany = await companyRepository.getCompany({ clientId, id: literal(`id = "parentCompanyId"`) });
    if (!parentCompany) throw new BadRequestError('No parent company selected.');

    const currentCompanies = JSON.parse(JSON.stringify(await companyRepository.getCompanies({ clientId })));

    const newCompanies = _.keyBy(companies, 'name');
    if (Object.values(newCompanies).length !== companies.length) {
      throw new BadRequestError('Some companies have the same name.');
    }

    const companiesByName = _.merge(newCompanies, _.keyBy(currentCompanies, 'name'));

    // Skip companies that don't have a parent company specified and skip if the default parent company is specified
    for (const { name, parentCompanyName } of companies) {
      if (parentCompanyName == null) continue;
      if (parentCompanyName == parentCompany.name) continue;
      if (companiesByName[parentCompanyName] == null) {
        throw new BadRequestError(`Parent company of "${name}" named "${parentCompanyName}" not found.`);
      }
    }

    const companiesWithAssessment = companies.map((company: CompanyType) => {
      if (company.assessment == null) return company;

      const { ringFencing, ...implicitSupportAnswers } = company.assessment.answers;
      const assessmentName = companyUtils.calculateAssessment(implicitSupportAnswers);
      return { ...company, ringFencing, assessmentName };
    });

    const createdCompanies = [];
    const seenCompanies: Record<string, boolean> = {};
    const ARBITRARY_LIMIT = 200;
    for (let i = 0; i < ARBITRARY_LIMIT; i++) {
      if (Object.values(seenCompanies).length === companiesWithAssessment.length) break;

      for (const company of companiesWithAssessment) {
        if (seenCompanies[company.name]) continue;

        const { name, parentCompanyName, ringFencing, assessment, assessmentName, creditRating } = company;
        const { rating, probabilityOfDefault } = creditRating;

        if (name === parentCompanyName) throw new BadRequestError(`Company ${name} cannot be its own parent company.`);

        let parentCompanyCreditRating;
        if (parentCompanyName == null || parentCompanyName === parentCompany.name) {
          company.parentCompanyId = parentCompany.id;
          parentCompanyCreditRating = parentCompany.creditRating.rating;
        } else {
          const overrideParentCompany = await companyRepository.getCompany({ clientId, name: parentCompanyName });
          if (!overrideParentCompany) continue; // If company is not found it will be created later that why there is the iterative loop wrapping everything

          company.parentCompanyId = overrideParentCompany.id;
          parentCompanyCreditRating = overrideParentCompany.creditRating.rating;
        }

        if (!parentCompanyCreditRating) {
          throw new BadRequestError('Parent company needs to have credit rating defined.');
        }

        let creditRatingAdj = {} as CreditRatingAdjustedType;
        if (rating != null && assessment != null) {
          creditRatingAdj = {
            ratingAdj: companyUtils.adjustCreditRating(rating, parentCompanyCreditRating, assessmentName, ringFencing),
          };

          if (probabilityOfDefault != null) {
            creditRatingAdj.probabilityOfDefaultAdj = companyUtils.estimateProbabilityOfDefaultAdj(
              probabilityOfDefault,
              rating,
              creditRatingAdj?.ratingAdj,
            );
          }
        }

        const assessmentWithName = assessment ? { ...assessment, name: assessmentName } : null;

        const createdCompany = await companyRepository
          .createCompany(
            { ...company, assessment: assessmentWithName, creditRating: { ...creditRating, ...creditRatingAdj } },
            req.user,
          )
          .catch((error: any) => {
            if (Number(error?.original?.code) === 23505) {
              throw new BadRequestError(`Company ${company.name} already exists.`);
            }
            throw error;
          });

        seenCompanies[createdCompany.name] = true;
        createdCompanies.push(createdCompany);
      }
    }

    if (Object.values(seenCompanies).length !== companiesWithAssessment.length) {
      throw new InternalServerError('Some companies could not be created.');
    }

    res.json(createdCompanies);
  });
}

async function implicitSupport(req: Request, res: Response, next: NextFunction) {
  const { answers } = req.body;
  const { ringFencing, ...implicitSupportAnswers } = answers;

  const assessmentName = companyUtils.calculateAssessment(implicitSupportAnswers);

  req.body.ringFencing = ringFencing;
  req.body.assessmentName = assessmentName;
  next();
}

async function adjustCreditRating(req: Request, res: Response) {
  const { answers, ringFencing, assessmentName, creditRating, parentCompanyId } = req.body;
  const { rating, probabilityOfDefault } = creditRating;

  const parentCompany = await companyRepository.getCompanyById(parentCompanyId, req.user?.clientId);
  if (!parentCompany) throw new NotFoundError('Parent company');

  const parentCompanyCreditRating = parentCompany.creditRating.rating;
  if (!parentCompanyCreditRating) throw new BadRequestError('Parent company needs to have credit rating defined.');

  const creditRatingAdj: CreditRatingAdjustedType = {
    ratingAdj: companyUtils.adjustCreditRating(rating, parentCompanyCreditRating, assessmentName, ringFencing),
  };

  if (probabilityOfDefault != null) {
    creditRatingAdj.probabilityOfDefaultAdj = companyUtils.estimateProbabilityOfDefaultAdj(
      probabilityOfDefault,
      rating,
      creditRatingAdj?.ratingAdj,
    );
  }

  let response;
  if (answers) {
    response = { assessment: { name: assessmentName }, creditRating: creditRatingAdj };
  } else {
    response = { creditRating: creditRatingAdj };
  }

  res.json(response);
}

async function adjustProbabilityOfDefault(req: Request, res: Response) {
  const { creditRating } = req.body;
  const { rating, ratingAdj, probabilityOfDefault } = creditRating;
  res.json({
    creditRating: {
      probabilityOfDefaultAdj: companyUtils.estimateProbabilityOfDefaultAdj(probabilityOfDefault, rating, ratingAdj),
    },
  });
}

async function exportCompanies(req: Request, res: Response) {
  const companies = await companyRepository.getCompanies({ clientId: req.user?.clientId });

  const result = companyUtils.exportCompaniesAsExcel(companies);

  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.set('Content-Disposition', 'attachment; filename="Companies Export.xlsx"');

  return res.send(result);
}

export default {
  getCompanies: asyncControllerWrapper(getCompanies),
  getCompany: asyncControllerWrapper(getCompany),
  getTemplate: asyncControllerWrapper(getTemplate),
  putCompany: asyncControllerWrapper(putCompany),
  deleteCompany: asyncControllerWrapper(deleteCompany),
  postCompany: asyncControllerWrapper(postCompany),
  postCompanies: asyncControllerWrapper(postCompanies),
  implicitSupport: asyncControllerWrapper(implicitSupport),
  adjustCreditRating: asyncControllerWrapper(adjustCreditRating),
  adjustProbabilityOfDefault: asyncControllerWrapper(adjustProbabilityOfDefault),
  exportCompanies: asyncControllerWrapper(exportCompanies),
};
