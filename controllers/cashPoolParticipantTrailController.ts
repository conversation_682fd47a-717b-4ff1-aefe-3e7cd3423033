import { Request, Response } from 'express';

import { differenceInMonths, intervalToDuration, formatDuration } from 'date-fns';

import {
  cashPoolRepository,
  cashPoolParticipantAccountRepository,
  cashPoolLeaderBenefitRepository,
} from '../repositories';
import asyncControllerWrapper from '../utils/asyncControllerWrapper';
import * as cashPoolParticipantTrailUtils from '../utils/cashPoolParticipantTrail';
import { BadRequestError, NotFoundError } from '../utils/ErrorHandler';

async function getCashPoolParticipantTrails(req: Request, res: Response) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user!;
  const { body } = req;

  if (differenceInMonths(body.endDate, body.startDate) >= 30) {
    throw new BadRequestError('The maximum period allowed is 30 months.');
  }

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });
  if (!cashPool) throw new NotFoundError('Cash pool');

  const [trailsFilter, companyFilter, topCurrencyAccountFilter] = cashPoolParticipantTrailUtils.getFilterBy(body);
  const leaderBenefit = await cashPoolLeaderBenefitRepository.getLeaderBenefit({ cashPoolId, ...trailsFilter });

  const trails = await cashPoolParticipantAccountRepository.getCashPoolTrails({
    cashPoolId,
    whereTrails: trailsFilter,
    whereCompany: companyFilter,
    whereTopCurrencyAccount: topCurrencyAccountFilter,
  });

  return res.json({ trails, leaderBenefit });
}

/** Used for Structural Positions */
async function getCashPoolParticipantTrailsBalanceAndDate(req: Request, res: Response) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user!;
  const { body } = req;

  if (differenceInMonths(body.endDate, body.startDate) >= 30) {
    throw new BadRequestError('The maximum period allowed is 30 months.');
  }

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

  if (!cashPool) {
    throw new NotFoundError('Cash pool');
  }

  const [trailsFilter, companyFilter, topCurrencyAccountFilter] = cashPoolParticipantTrailUtils.getFilterBy(body);

  const trails = await cashPoolParticipantAccountRepository.getCashPoolTrails({
    cashPoolId,
    whereTrails: trailsFilter,
    whereCompany: companyFilter,
    whereTopCurrencyAccount: topCurrencyAccountFilter,
    trailAttributes: ['balance', 'date'],
  });
  return res.json(trails);
}

/**
 * Gets all cash pool trails, and then iterate through all
 * of them to find all period of at least 1 year in which
 * one company had debit or credit balance position.
 * Returns all structural positions, including company,
 * position, start and end date.
 */
async function getCashPoolStructuralPositions(req: Request, res: Response) {
  const { cashPoolId } = req.params;
  const { clientId } = req.user!;
  const { body } = req;

  const BALANCE_THRESHOLD = 0;

  const cashPool = await cashPoolRepository.getCashPool({ where: { id: cashPoolId, clientId } });

  if (!cashPool) throw new NotFoundError('Cash pool');

  const [trailsFilter, companyFilter, topCurrencyAccountFilter] = cashPoolParticipantTrailUtils.getFilterBy(body);

  const structuralThreshold = body.structuralThreshold || 9;

  const trails = await cashPoolParticipantAccountRepository.getCashPoolTrails({
    cashPoolId,
    whereTrails: trailsFilter,
    whereCompany: companyFilter,
    whereTopCurrencyAccount: topCurrencyAccountFilter,
    trailAttributes: ['balance', 'date'],
  });

  const structuralPositions = [];

  for (const participantData of trails) {
    const accountTrails = participantData.accountTrails;

    if (!accountTrails || accountTrails.length === 0) continue;

    let startDate: Date | undefined = undefined;
    let endDate: Date | undefined = undefined;
    let isPreviousPositive: boolean | undefined = undefined;

    for (const trail of accountTrails) {
      const { balance, date } = trail;

      if (balance === null || balance === BALANCE_THRESHOLD) continue;

      if (isPreviousPositive === undefined) {
        isPreviousPositive = balance > BALANCE_THRESHOLD;
        startDate = date;
        endDate = date;
        continue;
      }

      const isCurrentPositive = balance > BALANCE_THRESHOLD;
      if (isCurrentPositive !== isPreviousPositive) {
        if (startDate && endDate && differenceInMonths(endDate, startDate) >= structuralThreshold) {
          const duration = formatDuration(intervalToDuration({ start: new Date(startDate), end: new Date(endDate) }));

          structuralPositions.push({
            company: participantData.participant.company.name,
            position: isPreviousPositive ? 'Credit' : 'Debit',
            startDate,
            endDate,
            duration,
            positionStatus: 'Ended',
          });
        }

        startDate = date;
        endDate = date;
        isPreviousPositive = isCurrentPositive;
      } else {
        endDate = date;
      }
    }

    if (startDate && endDate && differenceInMonths(endDate, startDate) >= structuralThreshold) {
      const duration = formatDuration(intervalToDuration({ start: new Date(startDate), end: new Date(endDate) }));

      structuralPositions.push({
        company: participantData.participant.company.name,
        position: isPreviousPositive ? 'Credit' : 'Debit',
        startDate,
        endDate,
        duration,
        positionStatus: 'Ongoing',
      });
    }
  }

  return res.json(structuralPositions);
}


export default {
  getCashPoolParticipantTrails: asyncControllerWrapper(getCashPoolParticipantTrails),
  getCashPoolStructuralPositions: asyncControllerWrapper(getCashPoolStructuralPositions),
  getCashPoolParticipantTrailsBalanceAndDate: asyncControllerWrapper(getCashPoolParticipantTrailsBalanceAndDate),
};
