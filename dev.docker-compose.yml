version: '3'

services:
  app:
    image: nord-api:dev
    container_name: nord-api
    build:
      context: .
      dockerfile: dev.Dockerfile
    depends_on:
      - db
      - redis
    volumes:
      - .:/app
      - /app/node_modules
    links:
      - db
      - redis
    env_file:
      - .docker.env
    ports:
      - 4000:4000
    networks:
      - nord-api_default

  # For PostgreSQL to work after running `docker-compose up` database needs to be migrated by hand.
  # 1. Run `docker ps` to get container id
  # 2. Run `docker cp /path/to/sqlfile.sql CONTAINER_ID:/`
  # 3. Run `docker exec -it CONTAINER_ID sh` to connect to container
  # 4. Run `psql -U postgres` to connect to database, then run `\c template1` to change database
  # 5. Run `DROP DATABASE postgres;`, then `CREATE DATABASE postgres;` and then run `exit`
  # 6. Run `psql -U postgres -d postgres -f /path/to/sqlfile.sql` to import database
  db:
    image: postgres:14.8-alpine3.18
    container_name: nord-postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - 5432:5432
    networks:
      - nord-api_default

  redis:
    image: 'redis:6-alpine'
    container_name: nord-redis
    command: redis-server --requirepass $REDIS_AUTH
    env_file:
      - .docker.env
    ports:
      - 6379:6379
    networks:
      - nord-api_default

volumes:
  pgdata:

networks:
  nord-api_default:
    external: true
