{"name": "nord-api", "version": "1.0.0", "private": true, "type": "commonjs", "engines": {"node": ">=14.17.6"}, "scripts": {"build": "rimraf ./dist && tsc", "migrate:and:seed": "npx sequelize db:migrate && npx sequelize db:seed:all", "dev": "npm run build && npm run migrate:and:seed && nodemon", "dev:docker": "docker-compose -f dev.docker-compose.yml up", "start:prod": "npm run build && npm run migrate:and:seed && pm2 start pm2.prod.json", "start:stage": "npm run build && npm run migrate:and:seed && pm2 start pm2.stage.json", "test": "LANG=en_US.UTF-8 NODE_ENV=test jest", "test:e2e": "playwright test", "run:redis": "docker run --name nord-redis -p 6379:6379 -d redis:5.0.7-alpine", "lint": "eslint . --ext .js,.ts", "lint:fix": "npm run lint -- --fix"}, "dependencies": {"@azure/storage-blob": "^12.8.0", "@sentry/node": "^6.13.3", "@sentry/tracing": "^6.13.3", "abort-controller": "^3.0.0", "axios": "^0.21.1", "bcrypt": "^5.0.0", "chalk": "^4.1.0", "cls-hooked": "^4.2.2", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^1.8.2", "date-fns": "^2.29.1", "docxtemplater": "^3.21.1", "dotenv": "^8.2.0", "express": "^4.17.3", "form-data": "^4.0.0", "ftp": "^0.3.10", "helmet": "^4.4.1", "hpp": "^0.2.3", "joi": "^17.4.0", "jsftp": "^2.1.3", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "multer": "^1.4.2", "node-stream-zip": "^1.13.6", "pg": "^8.3.3", "pg-hstore": "^2.3.3", "pino": "^8.17.1", "pino-pretty": "^10.2.3", "pizzip": "^3.0.6", "pm2": "^5.0.0", "rate-limiter-flexible": "^2.2.1", "redis": "^3.1.2", "regression": "^2.0.1", "sequelize": "^6.29.0", "ssh2-sftp-client": "^9.0.4", "xlsx": "^0.17.0", "xml2js": "^0.6.2", "xss": "^1.0.10", "zod": "^3.22.4"}, "lint-staged": {"*.js": ["eslint --fix"], "*.+(json|md)": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "tsc && lint-staged", "pre-push": "npm test"}}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@playwright/test": "^1.26.0", "@swc/core": "^1.2.241", "@types/bcrypt": "^5.0.2", "@types/cls-hooked": "^4.3.3", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.12", "@types/cron": "^2.0.0", "@types/express": "^4.17.13", "@types/hpp": "^0.2.2", "@types/jest": "^28.1.7", "@types/jsonwebtoken": "^8.5.9", "@types/lodash": "^4.14.186", "@types/multer": "^1.4.7", "@types/node": "^18.7.8", "@types/pizzip": "^3.0.2", "@types/ssh2-sftp-client": "^9.0.0", "@types/supertest": "^2.0.12", "@types/validator": "^13.7.5", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^5.46.1", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.3.1", "husky": "^4.3.8", "jest": "^28.1.2", "lint-staged": "^10.5.4", "nodemon": "^2.0.19", "prettier": "^2.2.1", "rimraf": "^3.0.2", "sequelize-cli": "^6.2.0", "snyk": "^1.1187.0", "supertest": "^6.2.4", "ts-jest": "^28.0.8", "ts-node": "^10.9.1", "typescript": "^4.7.4"}}