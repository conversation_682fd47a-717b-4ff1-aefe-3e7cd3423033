//! Goes only up to 7 years, so for bigger tenors it will fail
const providerDataMock: any = [
  {
    dataValues: {
      id: 1,
      rating: 'AAA/Aaa',
      float3M: -1.01,
      yield3M: 0.14,
      float6M: 3.9,
      yield6M: 0.19,
      float1Y: 7.38,
      yield1Y: 0.26,
      float2Y: 4.49,
      yield2Y: 0.34,
      float3Y: 0.94,
      yield3Y: 0.58,
      float5Y: -1.49,
      yield5Y: 0.95,
      float7Y: 1.84,
      yield7Y: 1.25,
    },
  },
  {
    dataValues: {
      id: 2,
      rating: 'AA+/Aa1',
      float3M: -1.01,
      yield3M: 0.14,
      float6M: 3.9,
      yield6M: 0.19,
      float1Y: 7.38,
      yield1Y: 0.26,
      float2Y: 4.49,
      yield2Y: 0.34,
      float3Y: 0.94,
      yield3Y: 0.58,
      float5Y: -1.49,
      yield5Y: 0.95,
      float7Y: 1.84,
      yield7Y: 1.25,
    },
  },
  {
    dataValues: {
      id: 3,
      rating: 'AA/Aa2',
      float3M: 7.85,
      yield3M: 0.22,
      float6M: 15.6,
      yield6M: 0.31,
      float1Y: 22.18,
      yield1Y: 0.4,
      float2Y: 24.04,
      yield2Y: 0.53,
      float3Y: 22.71,
      yield3Y: 0.8,
      float5Y: 24.67,
      yield5Y: 1.22,
      float7Y: 29.5,
      yield7Y: 1.53,
    },
  },
  {
    dataValues: {
      id: 4,
      rating: 'AA-/Aa3',
      float3M: 7.85,
      yield3M: 0.22,
      float6M: 15.6,
      yield6M: 0.31,
      float1Y: 22.18,
      yield1Y: 0.4,
      float2Y: 24.04,
      yield2Y: 0.53,
      float3Y: 22.71,
      yield3Y: 0.8,
      float5Y: 24.67,
      yield5Y: 1.22,
      float7Y: 29.5,
      yield7Y: 1.53,
    },
  },
  {
    dataValues: {
      id: 5,
      rating: 'A+/A1',
      float3M: 7.85,
      yield3M: 0.22,
      float6M: 15.6,
      yield6M: 0.31,
      float1Y: 22.18,
      yield1Y: 0.4,
      float2Y: 24.04,
      yield2Y: 0.53,
      float3Y: 22.71,
      yield3Y: 0.8,
      float5Y: 24.67,
      yield5Y: 1.22,
      float7Y: 29.5,
      yield7Y: 1.53,
    },
  },
  {
    dataValues: {
      id: 6,
      rating: 'A/A2',
      float3M: 14.72,
      yield3M: 0.29,
      float6M: 20.6,
      yield6M: 0.36,
      float1Y: 28.38,
      yield1Y: 0.47,
      float2Y: 30.53,
      yield2Y: 0.6,
      float3Y: 31.65,
      yield3Y: 0.88,
      float5Y: 33.56,
      yield5Y: 1.3,
      float7Y: 35.24,
      yield7Y: 1.58,
    },
  },
  {
    dataValues: {
      id: 7,
      rating: 'A-/A3',
      float3M: 14.72,
      yield3M: 0.29,
      float6M: 20.6,
      yield6M: 0.36,
      float1Y: 28.38,
      yield1Y: 0.47,
      float2Y: 30.53,
      yield2Y: 0.6,
      float3Y: 31.65,
      yield3Y: 0.88,
      float5Y: 33.56,
      yield5Y: 1.3,
      float7Y: 35.24,
      yield7Y: 1.58,
    },
  },
  {
    dataValues: {
      id: 8,
      rating: 'BBB+/Baa1',
      float3M: 36.62,
      yield3M: 0.51,
      float6M: 42.74,
      yield6M: 0.58,
      float1Y: 50.2,
      yield1Y: 0.68,
      float2Y: 53.56,
      yield2Y: 0.83,
      float3Y: 57.58,
      yield3Y: 1.14,
      float5Y: 62.81,
      yield5Y: 1.6,
      float7Y: 64.9,
      yield7Y: 1.88,
    },
  },
  {
    dataValues: {
      id: 9,
      rating: 'BBB/Baa2',
      float3M: 36.62,
      yield3M: 0.51,
      float6M: 42.74,
      yield6M: 0.58,
      float1Y: 50.2,
      yield1Y: 0.68,
      float2Y: 53.56,
      yield2Y: 0.83,
      float3Y: 57.58,
      yield3Y: 1.14,
      float5Y: 62.81,
      yield5Y: 1.6,
      float7Y: 64.9,
      yield7Y: 1.88,
    },
  },
  {
    dataValues: {
      id: 10,
      rating: 'BBB-/Baa3',
      float3M: 36.62,
      yield3M: 0.51,
      float6M: 42.74,
      yield6M: 0.58,
      float1Y: 50.2,
      yield1Y: 0.68,
      float2Y: 53.56,
      yield2Y: 0.83,
      float3Y: 57.58,
      yield3Y: 1.14,
      float5Y: 62.81,
      yield5Y: 1.6,
      float7Y: 64.9,
      yield7Y: 1.88,
    },
  },
  {
    dataValues: {
      id: 11,
      rating: 'BB+/Ba1',
      float3M: 36.62,
      yield3M: 0.51,
      float6M: 42.74,
      yield6M: 0.58,
      float1Y: 50.2,
      yield1Y: 0.68,
      float2Y: 53.56,
      yield2Y: 0.83,
      float3Y: 57.58,
      yield3Y: 1.14,
      float5Y: 62.81,
      yield5Y: 1.6,
      float7Y: 64.9,
      yield7Y: 1.88,
    },
  },
  {
    dataValues: {
      id: 12,
      rating: 'BB/Ba2',
      float3M: 147.74,
      yield3M: 1.62,
      float6M: 158.58,
      yield6M: 1.74,
      float1Y: 167.39,
      yield1Y: 1.86,
      float2Y: 170.94,
      yield2Y: 2,
      float3Y: 185.27,
      yield3Y: 2.42,
      float5Y: 200.04,
      yield5Y: 2.97,
      float7Y: 195.66,
      yield7Y: 3.19,
    },
  },
  {
    dataValues: {
      id: 13,
      rating: 'BB-/Ba3',
      float3M: 147.74,
      yield3M: 1.62,
      float6M: 158.58,
      yield6M: 1.74,
      float1Y: 167.39,
      yield1Y: 1.86,
      float2Y: 170.94,
      yield2Y: 2,
      float3Y: 185.27,
      yield3Y: 2.42,
      float5Y: 200.04,
      yield5Y: 2.97,
      float7Y: 195.66,
      yield7Y: 3.19,
    },
  },
  {
    dataValues: {
      id: 14,
      rating: 'B+/B1',
      float3M: 147.74,
      yield3M: 1.62,
      float6M: 158.58,
      yield6M: 1.74,
      float1Y: 167.39,
      yield1Y: 1.86,
      float2Y: 170.94,
      yield2Y: 2,
      float3Y: 185.27,
      yield3Y: 2.42,
      float5Y: 200.04,
      yield5Y: 2.97,
      float7Y: 195.66,
      yield7Y: 3.19,
    },
  },
  {
    dataValues: {
      id: 15,
      rating: 'B/B2',
      float3M: 420.84,
      yield3M: 4.35,
      float6M: 422.44,
      yield6M: 4.38,
      float1Y: 440.46,
      yield1Y: 4.59,
      float2Y: 433.09,
      yield2Y: 4.62,
      float3Y: 409.01,
      yield3Y: 4.66,
      float5Y: 377.28,
      yield5Y: 4.74,
      float7Y: 348.9,
      yield7Y: 4.72,
    },
  },
  {
    dataValues: {
      id: 16,
      rating: 'B-/B3',
      float3M: 420.84,
      yield3M: 4.35,
      float6M: 422.44,
      yield6M: 4.38,
      float1Y: 440.46,
      yield1Y: 4.59,
      float2Y: 433.09,
      yield2Y: 4.62,
      float3Y: 409.01,
      yield3Y: 4.66,
      float5Y: 377.28,
      yield5Y: 4.74,
      float7Y: 348.9,
      yield7Y: 4.72,
    },
  },
  {
    dataValues: {
      id: 17,
      rating: 'CCC+/Caa1',
      float3M: 420.84,
      yield3M: 4.35,
      float6M: 422.44,
      yield6M: 4.38,
      float1Y: 440.46,
      yield1Y: 4.59,
      float2Y: 433.09,
      yield2Y: 4.62,
      float3Y: 409.01,
      yield3Y: 4.66,
      float5Y: 377.28,
      yield5Y: 4.74,
      float7Y: 348.9,
      yield7Y: 4.72,
    },
  },
  {
    dataValues: {
      id: 18,
      rating: 'CCC/Caa2',
      float3M: 998.14,
      yield3M: 10.13,
      float6M: 991.04,
      yield6M: 10.06,
      float1Y: 927.82,
      yield1Y: 9.46,
      float2Y: 826.02,
      yield2Y: 8.55,
      float3Y: 740.84,
      yield3Y: 7.98,
      float5Y: 645.98,
      yield5Y: 7.43,
      float7Y: 600.28,
      yield7Y: 7.23,
    },
  },
  {
    dataValues: {
      id: 19,
      rating: 'CCC-/Caa3',
      float3M: 998.14,
      yield3M: 10.13,
      float6M: 991.04,
      yield6M: 10.06,
      float1Y: 927.82,
      yield1Y: 9.46,
      float2Y: 826.02,
      yield2Y: 8.55,
      float3Y: 740.84,
      yield3Y: 7.98,
      float5Y: 645.98,
      yield5Y: 7.43,
      float7Y: 600.28,
      yield7Y: 7.23,
    },
  },
  {
    dataValues: {
      id: 20,
      rating: 'CC/Ca',
      float3M: 998.14,
      yield3M: 10.13,
      float6M: 991.04,
      yield6M: 10.06,
      float1Y: 927.82,
      yield1Y: 9.46,
      float2Y: 826.02,
      yield2Y: 8.55,
      float3Y: 740.84,
      yield3Y: 7.98,
      float5Y: 645.98,
      yield5Y: 7.43,
      float7Y: 600.28,
      yield7Y: 7.23,
    },
  },
  {
    dataValues: {
      id: 21,
      rating: 'C/Ca',
      float3M: 998.14,
      yield3M: 10.13,
      float6M: 991.04,
      yield6M: 10.06,
      float1Y: 927.82,
      yield1Y: 9.46,
      float2Y: 826.02,
      yield2Y: 8.55,
      float3Y: 740.84,
      yield3Y: 7.98,
      float5Y: 645.98,
      yield5Y: 7.43,
      float7Y: 600.28,
      yield7Y: 7.23,
    },
  },
];

export default providerDataMock;
