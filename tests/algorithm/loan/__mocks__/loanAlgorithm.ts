import { faker } from '@faker-js/faker';

import { paymentFrequency, creditRatingEnums, dayCountEnums } from '../../../../enums';
import { LoanType } from '../../../../types';

const loanAlgorithm: Partial<LoanType> = {
  clientId: 1,
  lender: {
    id: 4,
    parentCompanyId: 8,
    name: faker.company.name(),
    industry: 'Consumer Discretionary',
    country: 'Norway',
    creditRating: {
      rating: creditRatingEnums.CreditRatingValueEnum['B+/B1'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['A+/A1'],
      probabilityOfDefault: 3,
      probabilityOfDefaultAdj: 0.8,
    },
  },
  borrower: {
    id: 3,
    parentCompanyId: 8,
    name: faker.company.name(),
    industry: 'Industrials',
    country: 'Croatia',
    creditRating: {
      rating: creditRatingEnums.CreditRatingValueEnum['B+/B1'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['A+/A1'],
      probabilityOfDefault: 5.3,
      probabilityOfDefaultAdj: 0.48,
    },
  },
  issueDate: new Date(2021, 6, 29),
  maturityDate: new Date(2027, 5, 29),
  currency: 'USD',
  amount: 100000,
  paymentFrequency: paymentFrequency.MONTHLY,
  rateType: { type: 'fixed' },
  seniority: 'Senior Secured',
  pricingApproach: 'implicit non-standard',
  dayCount: dayCountEnums.dayCountMapper['ACT/365'],
  type: 'Bullet',
};

export default loanAlgorithm;
