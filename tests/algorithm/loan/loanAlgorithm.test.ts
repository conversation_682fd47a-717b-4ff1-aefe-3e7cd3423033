import _ from 'lodash';

import models from '../../../models';
import { paymentFrequency, creditRatingEnums, dayCountEnums } from '../../../enums';
import calculateLoanDataAlgorithm from '../../../controllers/algorithms/reportAlgorithms/loanAlgorithm';
import loanAlgorithmMock from './__mocks__/loanAlgorithm';
import { clientRepository } from '../../../repositories';

const mockRunCashPoolCreateUpdateGuards = jest.spyOn(clientRepository, 'getClient');
mockRunCashPoolCreateUpdateGuards.mockImplementation(() => ({ isLoanApproachCalculated: true } as any));

afterAll(async () => {
  await models.sequelize.close();
});

describe('Test the loan algorithm', () => {
  test('It should check the output of the loan algorithm 1', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.7, midPoint: 1.72, upperBound: 1.79 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 2', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.7, midPoint: 1.72, upperBound: 1.79 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 3', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.maturityDate = new Date(2029, 5, 29);

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 2, midPoint: 2.03, upperBound: 2.1 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 4', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.paymentFrequency = paymentFrequency.SEMI_ANNUAL;

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.7, midPoint: 1.73, upperBound: 1.8 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 5', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.seniority = 'Unsubordinated';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.73, midPoint: 1.75, upperBound: 1.83 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 6', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.seniority = 'Unsubordinated';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.73, midPoint: 1.75, upperBound: 1.83 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 7', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.currency = 'EUR';
    deepCopy.amount = 254000;
    deepCopy.maturityDate = new Date(2023, 5, 29);
    deepCopy.paymentFrequency = paymentFrequency.ANNUAL;
    deepCopy.pricingApproach = 'stand-alone';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 2.1, midPoint: 3.02, upperBound: 4.15 });
    expect(pricingApproach).toEqual('stand-alone');
  });

  test('It should check the output of the loan algorithm 8', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.currency = 'CAD';
    deepCopy.amount = 20000000;
    deepCopy.maturityDate = new Date(2030, 6, 7);
    deepCopy.paymentFrequency = paymentFrequency.ANNUAL;
    deepCopy.pricingApproach = 'stand-alone';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 4.93, midPoint: 5.56, upperBound: 6.26 });
    expect(pricingApproach).toEqual('stand-alone');
  });

  test('It should check the output of the loan algorithm 9', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.currency = 'CAD';
    deepCopy.amount = 20000000;
    deepCopy.maturityDate = new Date(2030, 6, 7);
    deepCopy.paymentFrequency = paymentFrequency.ANNUAL;
    deepCopy.pricingApproach = 'stand-alone';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });

    expect(report).toEqual({ lowerBound: 4.93, midPoint: 5.56, upperBound: 6.26 });
    expect(pricingApproach).toEqual('stand-alone');
  });

  test('It should check the output of the loan algorithm 10', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.seniority = 'Subordinated';
    deepCopy.currency = 'CHF';
    deepCopy.amount = 70000000;
    deepCopy.maturityDate = new Date(2030, 2, 7);
    deepCopy.paymentFrequency = paymentFrequency.MONTHLY;
    deepCopy.pricingApproach = 'stand-alone non-standard';
    deepCopy.type = 'Balloon';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 3.28, midPoint: 3.94, upperBound: 4.67 });
    expect(pricingApproach).toEqual('stand-alone');
  });

  test('It should check the output of the loan algorithm 11', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.seniority = 'Senior Secured';
    deepCopy.currency = 'CHF';
    deepCopy.amount = 70000000;
    deepCopy.maturityDate = new Date(2033, 2, 7);
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.pricingApproach = 'stand-alone non-standard';
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'float', referenceRate: 'USD SOFR', referenceRateMaturity: '3 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });
    const { report: report2, pricingApproach: pricingApproach2 } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    const expectedReport = { lowerBound: 218.62, midPoint: 268.96, upperBound: 326.3 };
    const expectedPricingApproach = 'stand-alone';

    expect(report).toEqual(expectedReport);
    expect(pricingApproach).toEqual(expectedPricingApproach);

    expect(report2).toEqual(expectedReport);
    expect(pricingApproach2).toEqual(expectedPricingApproach);
  });

  test('It should check the output of the loan algorithm 12', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.type = 'Bullet';
    deepCopy.rateType = { type: 'float', referenceRate: 'USD SOFR', referenceRateMaturity: '3 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 62.15, midPoint: 64.83, upperBound: 72.0 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 13', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'float', referenceRate: 'USD SOFR', referenceRateMaturity: '3 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 62.53, midPoint: 65.18, upperBound: 72.44 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 14', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'float', referenceRate: 'USD SOFR', referenceRateMaturity: '3 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });

    expect(report).toEqual({ lowerBound: 62.53, midPoint: 65.18, upperBound: 72.44 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 15', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.seniority = 'Senior Secured';
    deepCopy.currency = 'CHF';
    deepCopy.amount = 70000000;
    deepCopy.maturityDate = new Date(2033, 2, 7);
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.pricingApproach = 'stand-alone non-standard';
    deepCopy.type = 'Bullet';
    deepCopy.rateType = { type: 'float', referenceRate: 'USD SOFR', referenceRateMaturity: '3 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 218.68, midPoint: 269.74, upperBound: 328.2 });
    expect(pricingApproach).toEqual('stand-alone');
  });

  test('It should check the output of the loan algorithm 16', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'Netherlands';
    deepCopy.borrower!.industry = 'Materials';

    deepCopy.lender!.country = 'Brazil';
    deepCopy.lender!.industry = 'Financials';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.8, midPoint: 1.83, upperBound: 1.89 });
    expect(pricingApproach).toEqual('implicit non-standard');
  });

  test('It should check the output of the loan algorithm 17', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'Netherlands';
    deepCopy.borrower!.industry = 'Materials';

    deepCopy.lender!.country = 'Brazil';
    deepCopy.lender!.industry = 'Financials';

    deepCopy.paymentFrequency = paymentFrequency.ANNUAL;
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'float', referenceRate: 'USD SOFR', referenceRateMaturity: '12 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 75.64, midPoint: 77.92, upperBound: 84.59 });
    expect(pricingApproach).toEqual('implicit non-standard');
  });

  test('It should check the output of the loan algorithm 18', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'India';
    deepCopy.borrower!.industry = 'Utilities';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['AAA/Aaa'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 1.8,
    };

    deepCopy.lender!.country = 'Mexico';
    deepCopy.lender!.industry = 'Healthcare';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B/B2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['C/Ca'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 2.2,
    };

    deepCopy.maturityDate = new Date(2027, 8, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'float', referenceRate: 'EURIBOR', referenceRateMaturity: '3 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1115.05, midPoint: 1356.31, upperBound: 1597.58 });
    expect(pricingApproach).toEqual('implicit non-standard');
  });

  test('It should check the output of the loan algorithm 19', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'India';
    deepCopy.borrower!.industry = 'Utilities';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['AAA/Aaa'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 1.8,
    };

    deepCopy.lender!.country = 'Mexico';
    deepCopy.lender!.industry = 'Healthcare';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B/B2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['C/Ca'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 2.2,
    };

    deepCopy.maturityDate = new Date(2027, 8, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.type = 'Bullet';
    deepCopy.rateType = { type: 'float', referenceRate: 'EURIBOR', referenceRateMaturity: '3 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1142.88, midPoint: 1392.58, upperBound: 1642.28 });
    expect(pricingApproach).toEqual('implicit non-standard');
  });

  test('It should check the output of the loan algorithm 20', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'India';
    deepCopy.borrower!.industry = 'Utilities';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['AAA/Aaa'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 1.8,
    };

    deepCopy.lender!.country = 'Mexico';
    deepCopy.lender!.industry = 'Healthcare';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B/B2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['C/Ca'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 2.2,
    };

    deepCopy.maturityDate = new Date(2027, 8, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'fixed' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 11.94, midPoint: 14.36, upperBound: 16.78 });
    expect(pricingApproach).toEqual('implicit non-standard');
  });

  test('It should check the output of the loan algorithm 21', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'India';
    deepCopy.borrower!.industry = 'Utilities';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['AAA/Aaa'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 1.8,
    };

    deepCopy.lender!.country = 'Mexico';
    deepCopy.lender!.industry = 'Healthcare';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B/B2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['C/Ca'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 2.2,
    };

    deepCopy.maturityDate = new Date(2027, 8, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'fixed' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });

    expect(report).toEqual({ lowerBound: 11.48, midPoint: 13.82, upperBound: 16.17 });
    expect(pricingApproach).toEqual('implicit non-standard');
  });

  test('It should check the output of the loan algorithm 22', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'Colombia';
    deepCopy.borrower!.industry = 'Media & Communications';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.lender!.country = 'Saudi Arabia';
    deepCopy.lender!.industry = 'Consumer Staples';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.pricingApproach = 'stand-alone';
    deepCopy.maturityDate = new Date(2023, 11, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.SEMI_ANNUAL;
    deepCopy.seniority = 'Subordinated';
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'fixed' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });

    expect(report).toEqual({ lowerBound: 2.9, midPoint: 3.69, upperBound: 4.66 });
    expect(pricingApproach).toEqual('stand-alone non-standard');
  });

  test('It should check the output of the loan algorithm 23', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'Colombia';
    deepCopy.borrower!.industry = 'Media & Communications';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.lender!.country = 'Saudi Arabia';
    deepCopy.lender!.industry = 'Consumer Staples';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.pricingApproach = 'stand-alone';
    deepCopy.maturityDate = new Date(2023, 11, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'float', referenceRate: 'JPY LIBOR', referenceRateMaturity: '3 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });
    const { report: report2, pricingApproach: pricingApproach2 } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });
    const expectedReport = { lowerBound: 172.33, midPoint: 236.83, upperBound: 319.85 };
    const expectedPricingApproach = 'stand-alone non-standard';

    expect(report).toEqual(expectedReport);
    expect(pricingApproach).toEqual(expectedPricingApproach);

    expect(report2).toEqual(expectedReport);
    expect(pricingApproach2).toEqual(expectedPricingApproach);
  });

  test('It should check the output of the loan algorithm 24', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'Colombia';
    deepCopy.borrower!.industry = 'Media & Communications';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.lender!.country = 'Saudi Arabia';
    deepCopy.lender!.industry = 'Consumer Staples';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.pricingApproach = 'stand-alone';
    deepCopy.maturityDate = new Date(2023, 11, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.type = 'Bullet';
    deepCopy.rateType = { type: 'float', referenceRate: 'JPY LIBOR', referenceRateMaturity: '3 months' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });

    const { report: report2, pricingApproach: pricingApproach2 } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });
    const expectedReport = { lowerBound: 171.69, midPoint: 235.93, upperBound: 318.87 };
    const expectedPricingApproach = 'stand-alone non-standard';

    expect(report).toEqual(expectedReport);
    expect(pricingApproach).toEqual(expectedPricingApproach);

    expect(report2).toEqual(expectedReport);
    expect(pricingApproach2).toEqual(expectedPricingApproach);
  });

  test('It should check the output of the loan algorithm 25', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'Taiwan';
    deepCopy.borrower!.industry = 'Media & Communications';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.lender!.country = 'Denmark';
    deepCopy.lender!.industry = 'Consumer Staples';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.pricingApproach = 'stand-alone';
    deepCopy.maturityDate = new Date(2043, 11, 24);
    deepCopy.currency = 'CHF';
    deepCopy.paymentFrequency = paymentFrequency.MONTHLY;
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.type = 'Bullet';
    deepCopy.rateType = { type: 'float', referenceRate: 'CDI', referenceRateMaturity: '1 month' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });

    const { report: report2, pricingApproach: pricingApproach2 } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    const expectedPricingApproach = 'stand-alone non-standard';

    expect(report).toEqual({ lowerBound: 174.77, midPoint: 212.81, upperBound: 261.28 });
    expect(pricingApproach).toEqual(expectedPricingApproach);

    expect(report2).toEqual({ lowerBound: 156.02, midPoint: 193.25, upperBound: 240.3 });
    expect(pricingApproach2).toEqual(expectedPricingApproach);
  });

  test('It should check the output of the loan algorithm 26', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'Taiwan';
    deepCopy.borrower!.industry = 'Media & Communications';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.lender!.country = 'Denmark';
    deepCopy.lender!.industry = 'Consumer Staples';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.pricingApproach = 'stand-alone';
    deepCopy.maturityDate = new Date(2043, 11, 24);
    deepCopy.currency = 'CHF';
    deepCopy.paymentFrequency = paymentFrequency.MONTHLY;
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'float', referenceRate: 'CDI', referenceRateMaturity: '1 month' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });

    const { report: report2, pricingApproach: pricingApproach2 } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    const expectedPricingApproach = 'stand-alone non-standard';

    expect(report).toEqual({ lowerBound: 173.4, midPoint: 210.3, upperBound: 256.94 });
    expect(pricingApproach).toEqual(expectedPricingApproach);

    expect(report2).toEqual({ lowerBound: 154.35, midPoint: 190.37, upperBound: 235.53 });
    expect(pricingApproach2).toEqual(expectedPricingApproach);
  });

  test('It should check the output of the loan algorithm 27', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.borrower!.country = 'Taiwan';
    deepCopy.borrower!.industry = 'Media & Communications';
    deepCopy.borrower!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B-/B3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      probabilityOfDefault: 4.9,
      probabilityOfDefaultAdj: 5.7,
    };

    deepCopy.lender!.country = 'Denmark';
    deepCopy.lender!.industry = 'Consumer Staples';
    deepCopy.lender!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA+/Aa1'],
      probabilityOfDefault: 2.4,
      probabilityOfDefaultAdj: 4.2,
    };

    deepCopy.pricingApproach = 'implicit';
    deepCopy.maturityDate = new Date(2024, 11, 24);
    deepCopy.currency = 'EUR';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.type = 'Balloon';
    deepCopy.rateType = { type: 'fixed' };

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: true,
    });

    const { report: report2, pricingApproach: pricingApproach2 } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    const expectedPricingApproach = 'implicit';

    expect(report).toEqual({ lowerBound: 1.79, midPoint: 2.38, upperBound: 3.15 });
    expect(pricingApproach).toEqual(expectedPricingApproach);

    expect(report2).toEqual({ lowerBound: 1.41, midPoint: 1.99, upperBound: 2.73 });
    expect(pricingApproach2).toEqual(expectedPricingApproach);
  });

  test('It should check the output of the loan algorithm 28', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.dayCount = dayCountEnums.dayCountMapper['ACT/360'];

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.67, midPoint: 1.7, upperBound: 1.77 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 29', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.dayCount = dayCountEnums.dayCountMapper['ACT/360'];
    deepCopy.type = 'Balloon';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.7, midPoint: 1.72, upperBound: 1.79 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 30', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.dayCount = dayCountEnums.dayCountMapper['ACT/252'];

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.17, midPoint: 1.19, upperBound: 1.24 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 31', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.dayCount = dayCountEnums.dayCountMapper['ACT/252'];
    deepCopy.type = 'Balloon';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 1.19, midPoint: 1.21, upperBound: 1.26 });
    expect(pricingApproach).toEqual('implicit');
  });

  test('It should check the output of the loan algorithm 32', async () => {
    const deepCopy = _.cloneDeep(loanAlgorithmMock);
    deepCopy.dayCount = dayCountEnums.dayCountMapper['ACT/252'];
    deepCopy.rateType = { type: 'float', referenceRate: 'USD SOFR', referenceRateMaturity: '3 months' };
    deepCopy.type = 'Balloon';

    const { report, pricingApproach } = await calculateLoanDataAlgorithm({
      ...deepCopy,
      isUsingRegionalProviderData: false,
    });

    expect(report).toEqual({ lowerBound: 43.17, midPoint: 45.0, upperBound: 50.01 });
    expect(pricingApproach).toEqual('implicit');
  });
});
