import { faker } from '@faker-js/faker';

import { paymentFrequency, creditRatingEnums } from '../../../../enums';
import { GuaranteeType } from '../../../../types';

const guaranteeAlgorithm: Partial<GuaranteeType> = {
  guarantor: {
    id: 4,
    parentCompanyId: 8,
    name: faker.company.name(),
    industry: 'Quasi Government',
    country: 'Norway',
    creditRating: {
      rating: creditRatingEnums.CreditRatingValueEnum['B+/B1'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['B+/B1'],
      probabilityOfDefault: 4.3,
      probabilityOfDefaultAdj: 4.3,
    },
  },
  principal: {
    id: 6,
    parentCompanyId: 8,
    name: faker.company.name(),
    industry: 'Consumer Discretionary',
    country: 'Spain',
    creditRating: {
      rating: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      probabilityOfDefault: 3,
      probabilityOfDefaultAdj: 3,
    },
  },
  issueDate: new Date(2021, 5, 29),
  terminationDate: new Date(2027, 7, 18),
  currency: 'USD',
  amount: 200000,
  paymentFrequency: paymentFrequency.MONTHLY,
  seniority: 'Unsubordinated',
  pricingApproach: 'implicit',
  pricingMethodology: 'Yield - Expected Loss Approach',
};

export default guaranteeAlgorithm;
