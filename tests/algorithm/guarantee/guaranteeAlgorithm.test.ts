import _ from 'lodash';

import { paymentFrequency, creditRatingEnums } from '../../../enums';

import { providerDataRepository } from '../../../repositories';
import calculateGuaranteeDataAlgorithm from '../../../controllers/algorithms/reportAlgorithms/guaranteeAlgorithm';
import guaranteeAlgorithmMock from './__mocks__/guaranteeAlgorithm';
import providerDataMock from '../__mocks__/providerData';

jest.mock('../../../repositories/providerDataRepository');

const mockGetProviderDataIssueDate = jest.spyOn(providerDataRepository, 'getProviderDataIssueDate');
mockGetProviderDataIssueDate.mockResolvedValue('2021-06-29');

const mockGetProviderData = jest.spyOn(providerDataRepository, 'getProviderData');
mockGetProviderData.mockResolvedValue(providerDataMock);

describe('Test the guarantee algorithm', () => {
  test('It should check the output of the guarantee algorithm 1', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 6.82, midPoint: 2.06, upperBound: -2.7 });
  });

  test('It should check the output of the guarantee algorithm 2', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.issueDate = new Date(2022, 5, 29);

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 5.79, midPoint: 1.49, upperBound: -2.81 });
  });

  test('It should check the output of the guarantee algorithm 3', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.issueDate = new Date(2022, 5, 29);
    deepCopy.terminationDate = new Date(2025, 5, 18);

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 3.46, midPoint: 0.25, upperBound: -2.95 });
  });

  test('It should check the output of the guarantee algorithm 4', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.paymentFrequency = paymentFrequency.SEMI_ANNUAL;

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 6.82, midPoint: 2.04, upperBound: -2.73 });
  });

  test('It should check the output of the guarantee algorithm 5', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 6.82, midPoint: 2.06, upperBound: -2.7 });
  });

  test('It should check the output of the guarantee algorithm 6', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 6.82, midPoint: 2.06, upperBound: -2.7 });
  });

  test('It should check the output of the guarantee algorithm 7', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.currency = 'EUR';
    deepCopy.amount = 254000;
    deepCopy.issueDate = new Date(2021, 5, 29);
    deepCopy.terminationDate = new Date(2023, 5, 29);
    deepCopy.paymentFrequency = paymentFrequency.ANNUAL;

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 2.36, midPoint: -0.35, upperBound: -3.06 });
  });

  test('It should check the output of the guarantee algorithm 8', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.seniority = 'Unsubordinated';
    deepCopy.currency = 'CAD';
    deepCopy.amount = 20000000;
    deepCopy.issueDate = new Date(2022, 3, 15);
    deepCopy.terminationDate = new Date(2025, 6, 7);
    deepCopy.paymentFrequency = paymentFrequency.ANNUAL;

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 3.75, midPoint: 0.37, upperBound: -3 });
  });

  test('It should check the output of the guarantee algorithm 9', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.seniority = 'Senior Secured';
    deepCopy.currency = 'USD';
    deepCopy.amount = 3000000;
    deepCopy.issueDate = new Date(2022, 3, 15);
    deepCopy.terminationDate = new Date(2023, 6, 7);
    deepCopy.paymentFrequency = paymentFrequency.MONTHLY;

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 0.55, midPoint: -1.24, upperBound: -3.02 });
  });

  test('It should check the output of the guarantee algorithm 10', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.seniority = 'Subordinated';
    deepCopy.currency = 'CHF';
    deepCopy.amount = 70000000;
    deepCopy.issueDate = new Date(2022, 3, 15);
    deepCopy.terminationDate = new Date(2025, 2, 7);
    deepCopy.paymentFrequency = paymentFrequency.MONTHLY;

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 6.33, midPoint: 1.69, upperBound: -2.95 });
  });

  test('It should check the output of the guarantee algorithm 11', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.seniority = 'Senior Secured';
    deepCopy.currency = 'CHF';
    deepCopy.amount = 70000000;
    deepCopy.issueDate = new Date(2022, 3, 15);
    deepCopy.terminationDate = new Date(2026, 2, 7);
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 1.68, midPoint: -0.61, upperBound: -2.9 });
  });

  test('It should check the output of the guarantee algorithm 12', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 6.82, midPoint: 2.06, upperBound: -2.7 });
  });

  test('It should check the output of the guarantee algorithm 13', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 6.82, midPoint: 2.06, upperBound: -2.7 });
  });

  test('It should check the output of the guarantee algorithm 14', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 6.82, midPoint: 2.06, upperBound: -2.7 });
  });

  test('It should check the output of the guarantee algorithm 15', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.seniority = 'Senior Secured';
    deepCopy.currency = 'CHF';
    deepCopy.amount = 70000000;
    deepCopy.issueDate = new Date(2022, 3, 15);
    deepCopy.terminationDate = new Date(2023, 2, 7);
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 0.4, midPoint: -1.31, upperBound: -3.02 });
  });

  test('It should check the output of the guarantee algorithm 16', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'Netherlands';
    deepCopy.guarantor!.industry = 'Materials';

    deepCopy.principal!.country = 'Brazil';
    deepCopy.principal!.industry = 'Financials';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 7.67, midPoint: 2.49, upperBound: -2.7 });
  });

  test('It should check the output of the guarantee algorithm 17', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'Netherlands';
    deepCopy.guarantor!.industry = 'Materials';

    deepCopy.principal!.country = 'Brazil';
    deepCopy.principal!.industry = 'Financials';

    deepCopy.paymentFrequency = paymentFrequency.ANNUAL;

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: 7.67, midPoint: 2.45, upperBound: -2.77 });
  });

  test('It should check the output of the guarantee algorithm 18', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'India';
    deepCopy.guarantor!.industry = 'Utilities';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['AAA/Aaa'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 1.8,
    };

    deepCopy.principal!.country = 'Mexico';
    deepCopy.principal!.industry = 'Healthcare';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B/B2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['C/Ca'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 2.2,
    };

    deepCopy.issueDate = new Date(2021, 3, 20);
    deepCopy.issueDate = new Date(2027, 8, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: -0.09, midPoint: 10.46, upperBound: 21.02 });
  });

  test('It should check the output of the guarantee algorithm 19', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'India';
    deepCopy.guarantor!.industry = 'Utilities';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['AAA/Aaa'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 1.8,
    };

    deepCopy.principal!.country = 'Mexico';
    deepCopy.principal!.industry = 'Healthcare';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B/B2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['C/Ca'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 2.2,
    };

    deepCopy.issueDate = new Date(2021, 3, 20);
    deepCopy.issueDate = new Date(2027, 8, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: -0.09, midPoint: 10.46, upperBound: 21.02 });
  });

  test('It should check the output of the guarantee algorithm 20', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'India';
    deepCopy.guarantor!.industry = 'Utilities';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['AAA/Aaa'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 1.8,
    };

    deepCopy.principal!.country = 'Mexico';
    deepCopy.principal!.industry = 'Healthcare';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B/B2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['C/Ca'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 2.2,
    };

    deepCopy.issueDate = new Date(2021, 3, 20);
    deepCopy.issueDate = new Date(2027, 8, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: false });

    expect(report).toEqual({ lowerBound: -0.09, midPoint: 10.46, upperBound: 21.02 });
  });

  test('It should check the output of the guarantee algorithm 21', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'India';
    deepCopy.guarantor!.industry = 'Utilities';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['AAA/Aaa'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA-/Aa3'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 1.8,
    };

    deepCopy.principal!.country = 'Mexico';
    deepCopy.principal!.industry = 'Healthcare';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B/B2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['C/Ca'],
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 2.2,
    };

    deepCopy.issueDate = new Date(2021, 3, 20);
    deepCopy.issueDate = new Date(2027, 8, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: -0.09, midPoint: 10.46, upperBound: 21.02 });
  });

  test('It should check the output of the guarantee algorithm 22', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'Colombia';
    deepCopy.guarantor!.industry = 'Media & Communications';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.principal!.country = 'Saudi Arabia';
    deepCopy.principal!.industry = 'Consumer Staples';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.issueDate = new Date(2023, 3, 20);
    deepCopy.issueDate = new Date(2023, 11, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.SEMI_ANNUAL;
    deepCopy.seniority = 'Subordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 17.91, midPoint: 8.18, upperBound: -1.55 });
  });

  test('It should check the output of the guarantee algorithm 23', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'Colombia';
    deepCopy.guarantor!.industry = 'Media & Communications';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.principal!.country = 'Saudi Arabia';
    deepCopy.principal!.industry = 'Consumer Staples';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.issueDate = new Date(2023, 3, 20);
    deepCopy.issueDate = new Date(2023, 11, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 9.55, midPoint: 4.02, upperBound: -1.51 });
  });

  test('It should check the output of the guarantee algorithm 24', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'Colombia';
    deepCopy.guarantor!.industry = 'Media & Communications';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.principal!.country = 'Saudi Arabia';
    deepCopy.principal!.industry = 'Consumer Staples';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.issueDate = new Date(2023, 3, 20);
    deepCopy.issueDate = new Date(2023, 11, 24);
    deepCopy.currency = 'GBP';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 9.55, midPoint: 4.02, upperBound: -1.51 });
  });

  test('It should check the output of the guarantee algorithm 25', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'Taiwan';
    deepCopy.guarantor!.industry = 'Media & Communications';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.principal!.country = 'Denmark';
    deepCopy.principal!.industry = 'Consumer Staples';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.issueDate = new Date(2023, 3, 20);
    deepCopy.issueDate = new Date(2025, 11, 24);
    deepCopy.currency = 'CHF';
    deepCopy.paymentFrequency = paymentFrequency.MONTHLY;
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 4.64, midPoint: 1.44, upperBound: -1.76 });
  });

  test('It should check the output of the guarantee algorithm 26', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'Taiwan';
    deepCopy.guarantor!.industry = 'Media & Communications';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CC/Ca'],
      probabilityOfDefault: 3.9,
      probabilityOfDefaultAdj: 2.7,
    };

    deepCopy.principal!.country = 'Denmark';
    deepCopy.principal!.industry = 'Consumer Staples';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['CCC-/Caa3'],
      probabilityOfDefault: 4.4,
      probabilityOfDefaultAdj: 7.2,
    };

    deepCopy.issueDate = new Date(2023, 3, 20);
    deepCopy.issueDate = new Date(2025, 11, 24);
    deepCopy.currency = 'CHF';
    deepCopy.paymentFrequency = paymentFrequency.MONTHLY;
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 4.64, midPoint: 1.44, upperBound: -1.76 });
  });

  test('It should check the output of the guarantee algorithm 27', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'Taiwan';
    deepCopy.guarantor!.industry = 'Media & Communications';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['B-/B3'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['BB-/Ba3'],
      probabilityOfDefault: 4.9,
      probabilityOfDefaultAdj: 5.7,
    };

    deepCopy.principal!.country = 'Denmark';
    deepCopy.principal!.industry = 'Consumer Staples';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A/A2'],
      ratingAdj: creditRatingEnums.CreditRatingValueEnum['AA+/Aa1'],
      probabilityOfDefault: 2.4,
      probabilityOfDefaultAdj: 4.2,
    };

    deepCopy.issueDate = new Date(2023, 3, 20);
    deepCopy.issueDate = new Date(2024, 11, 24);
    deepCopy.currency = 'EUR';
    deepCopy.paymentFrequency = paymentFrequency.QUARTERLY;
    deepCopy.seniority = 'Unsubordinated';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 4.3, midPoint: 1.02, upperBound: -2.27 });
  });

  test('It should check the output of the guarantee algorithm 28', async () => {
    const deepCopy = _.cloneDeep(guaranteeAlgorithmMock);
    deepCopy.guarantor!.country = 'Thailand';
    deepCopy.guarantor!.industry = 'Consumer Discretionary';
    deepCopy.guarantor!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['AAA/Aaa'],
      ratingAdj: null,
      probabilityOfDefault: 2,
      probabilityOfDefaultAdj: null,
    };

    deepCopy.principal!.country = 'Nigeria';
    deepCopy.principal!.industry = 'Consumer Discretionary';
    deepCopy.principal!.creditRating = {
      rating: creditRatingEnums.CreditRatingValueEnum['A+/A1'],
      ratingAdj: null,
      probabilityOfDefault: 5.3,
      probabilityOfDefaultAdj: 2.65,
    };

    deepCopy.amount = 250000;
    deepCopy.issueDate = new Date(2022, 8, 22);
    deepCopy.terminationDate = new Date(2026, 8, 21);
    deepCopy.currency = 'USD';
    deepCopy.paymentFrequency = paymentFrequency.MONTHLY;
    deepCopy.seniority = 'Senior Secured';
    deepCopy.pricingApproach = 'stand-alone';

    const { report } = await calculateGuaranteeDataAlgorithm({ ...deepCopy, isUsingRegionalProviderData: true });

    expect(report).toEqual({ lowerBound: 2.93, midPoint: 1.6, upperBound: 0.26 });
  });
});
