import { TemplateFileType } from '../../../types';
import { TemplateFileLabelsEnum } from '../../../enums/templateFiles';
import { FileContentTypeEnum } from '../../../enums/files';

const clientTemplate: Required<TemplateFileType> = {
  id: 1,
  clientId: 1,
  name: 'Fixed Loan Agreement',
  label: TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT,
  type: 'loan',
  extension: '.docx',
  mimeType: FileContentTypeEnum.DOCX,
  country: 'Croatia',
  companyId: 3,
  company: {},
  createdAt: new Date(),
  updatedAt: new Date(),
};

export default clientTemplate;
