import fs from 'fs/promises';
import request from 'supertest';

import app from '../../app';
import { authSetup, azureSetup, rateLimiterSetup, transactionSetup } from '../setup';
import { FileContentTypeEnum } from '../../enums/files';
import { TemplateFileLabelsEnum } from '../../enums/templateFiles';
import models from '../../models';
import { clientTemplateRepository } from '../../repositories';
import redisService from '../../services/redis';
import { templateConsts } from '../../utils/templateFilesUtils';
import clientTemplate from './__mocks__/clientTemplate';

jest.mock('../../repositories/clientTemplateRepository');
jest.mock('../../services/azureService/azureService');
jest.mock('../../middlewares/authMethodMiddleware');
jest.mock('../../middlewares/rateLimiter');
jest.mock('../../models');

beforeAll(() => {
  rateLimiterSetup.basicRateLimiterSetup();
  authSetup.superadminAuthSetup();
  azureSetup.basicAzureSetup();
});

afterAll(async () => {
  await redisService.quit();
});

describe('Client Template Feature Controller', () => {
  test('It should get file template from db', async () => {
    const mockGetClientTemplate = jest.spyOn(clientTemplateRepository, 'getTemplateFile');
    mockGetClientTemplate.mockImplementation(() => Promise.resolve(clientTemplate));

    const response = await request(app).get('/api/client-template/template/:id');

    expect(response.statusCode).toBe(200);
    expect(response.headers['content-type']).toEqual(FileContentTypeEnum.DOCX);
    expect(response.headers['content-disposition']).toEqual(
      `attachment; filename="${clientTemplate.name}${clientTemplate.extension}"`,
    );
  });

  test('It should return 404 when client file template is not found', async () => {
    const mockGetClientTemplate = jest.spyOn(clientTemplateRepository, 'getTemplateFile');
    mockGetClientTemplate.mockImplementation(() => Promise.resolve(null));

    const response = await request(app).get('/api/client-template/template/:id');

    expect(response.statusCode).toBe(404);
  });

  test('It should get standard file template from file system', async () => {
    const response = await request(app).get(
      `/api/client-template/standard-template/${TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT}`,
    );

    expect(response.statusCode).toBe(200);
    expect(response.headers['content-type']).toEqual(FileContentTypeEnum.DOCX);
    expect(response.headers['content-disposition']).toEqual(
      `attachment; filename="Intercompany_Loan_Agreement_(fixed).docx"`,
    );
  });

  test('It should return 404 when label does not exist', async () => {
    const response = await request(app).get(`/api/client-template/standard-template/non-existent-label`);
    expect(response.statusCode).toBe(400);
  });

  test('It should check all standard templates exist', async () => {
    await Promise.all([
      fs.access(`static/${templateConsts.templateFilenames.LOAN_INTERCOMPANY_AGREEMENT_FIXED}`),
      fs.access(`static/${templateConsts.templateFilenames.LOAN_INTERCOMPANY_AGREEMENT_FLOAT}`),
      fs.access(`static/${templateConsts.templateFilenames.LOAN_STANDALONE}`),
      fs.access(`static/${templateConsts.templateFilenames.LOAN_IMPLICIT}`),

      fs.access(`static/${templateConsts.templateFilenames.GUARANTEE_INTERCOMPANY_AGREEMENT}`),
      fs.access(`static/${templateConsts.templateFilenames.GUARANTEE_YIELD_EL_STANDALONE}`),
      fs.access(`static/${templateConsts.templateFilenames.GUARANTEE_YIELD_EL_IMPLICIT}`),
      fs.access(`static/${templateConsts.templateFilenames.GUARANTEE_SECURITY_STANDALONE}`),
      fs.access(`static/${templateConsts.templateFilenames.GUARANTEE_SECURITY_IMPLICIT}`),
    ]);
  });

  test('It should get all client template files', async () => {
    const mockGetAllClientTemplates = jest.spyOn(clientTemplateRepository, 'getTemplateFiles');
    mockGetAllClientTemplates.mockImplementation(() => Promise.resolve([clientTemplate]));

    const response = await request(app).get('/api/client-template/template');

    expect(response.statusCode).toBe(200);
    expect(response.body[0]).toHaveProperty('id');
    expect(response.body[0]).toHaveProperty('clientId');
    expect(response.body[0]).toHaveProperty('name');
    expect(response.body[0]).toHaveProperty('label');
    expect(response.body[0]).toHaveProperty('extension');
    expect(response.body[0]).toHaveProperty('mimeType');
    expect(response.body[0]).toHaveProperty('createdAt');
    expect(response.body[0]).toHaveProperty('updatedAt');
  });

  test('It should upload client template', async () => {
    const mockCreateClientTemplate = jest.spyOn(clientTemplateRepository, 'createTemplateFile');
    mockCreateClientTemplate.mockImplementation(() => Promise.resolve(clientTemplate));

    transactionSetup.mockTransaction(models);

    const response = await request(app)
      .post('/api/client-template/template')
      .attach('file', 'static/Intercompany_Loan_Agreement_(fixed).docx')
      .field('type', 'loan')
      .field('label', TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT);

    expect(response.statusCode).toBe(200);
  });

  test('It should return 400 when file or type or label was not provided when creating client template', async () => {
    const response1 = await request(app)
      .post('/api/client-template/template')
      .field('type', 'loan')
      .field('label', TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT);

    const response2 = await request(app)
      .post('/api/client-template/template')
      .attach('file', 'static/Intercompany_Loan_Agreement_(fixed).docx')
      .field('label', TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT);

    const response3 = await request(app)
      .post('/api/client-template/template')
      .attach('file', 'static/Intercompany_Loan_Agreement_(fixed).docx')
      .field('type', 'loan');

    expect(response1.statusCode).toBe(400);
    expect(response2.statusCode).toBe(400);
    expect(response3.statusCode).toBe(400);
  });

  test('It should return 400 if type or label was not expected when creating client template', async () => {
    const response1 = await request(app)
      .post('/api/client-template/template')
      .attach('file', 'static/Intercompany_Loan_Agreement_(fixed).docx')
      .field('type', 'non-existent-type')
      .field('label', TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT);

    const response2 = await request(app)
      .post('/api/client-template/template')
      .attach('file', 'static/Intercompany_Loan_Agreement_(fixed).docx')
      .field('type', 'loan')
      .field('label', 'non-existent-label');

    expect(response1.statusCode).toBe(400);
    expect(response1.body.message).toBe('Invalid type.');

    expect(response2.statusCode).toBe(400);
    expect(response2.body.message).toBe('Invalid label.');
  });

  test('It should return 400 if label already used', async () => {
    const mockCreateClientTemplate = jest.spyOn(clientTemplateRepository, 'createTemplateFile');
    mockCreateClientTemplate.mockImplementation(() => {
      throw { original: { code: 23505 } };
    });

    const response = await request(app)
      .post('/api/client-template/template')
      .attach('file', 'static/Intercompany_Loan_Agreement_(fixed).docx')
      .field('type', 'loan')
      .field('label', TemplateFileLabelsEnum.LOAN_FIXED_AGREEMENT);

    expect(response.statusCode).toBe(400);
    expect(response.body.message).toBe('Label already used. To upload a new template first delete the existing one.');
  });

  test('It should delete client template file', async () => {
    const mockDeleteClientTemplate = jest.spyOn(clientTemplateRepository, 'deleteTemplateFile');
    mockDeleteClientTemplate.mockImplementation(() => Promise.resolve(1));

    const response = await request(app).delete('/api/client-template/template/:id');

    expect(response.statusCode).toBe(204);
  });
});
