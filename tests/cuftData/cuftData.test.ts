import request from 'supertest';

import app from '../../app';
import { cuftDataRepository } from '../../repositories';
import redisService from '../../services/redis';
import { authSetup, azureSetup, featureSetup, rateLimiterSetup } from '../setup';
import cuftDataWithCreditRatings from './__mocks__/cuftDataWithCreditRatings';

jest.mock('../../repositories/countryRepository');
jest.mock('../../services/azureService');
jest.mock('../../middlewares/authMethodMiddleware');
jest.mock('../../middlewares/rateLimiter');

beforeAll(() => {
  rateLimiterSetup.basicRateLimiterSetup();
  authSetup.superadminAuthSetup();
  azureSetup.basicAzureSetup();
  featureSetup.enabledFeatureSetup();
});

afterAll(async () => {
  await redisService.quit();
});

describe('CUFT Data controller', () => {
  test('It should get CUFT data ', async () => {
    const mockCuftDataFile = jest.spyOn(cuftDataRepository, 'getCuftData');
    mockCuftDataFile.mockImplementation(() => Promise.resolve([cuftDataWithCreditRatings]));

    const response = await request(app)
      .post('/api/cuft-data')
      .send({
        limit: 10,
        offset: 0,
        issueDate: '2025-09-12',
        dateMonthsBeforeIssueDate: '2020-06-12',
        tenor: '1',
        numberOfYearsBeforeAndAfterTenor: '100',
        creditRatings: ['BB/Ba2', 'B/B2', 'BBB+/Baa1'],
        countries: [],
        currencies: [],
        trancheAssetClasses: [],
        excludedCreditRatingIds: [3],
        sortBy: 'creditRating',
      });
    expect(response.statusCode).toBe(200);
    expect(response.body).toHaveProperty('totalNumberOfCuftData');
    expect(response.body).toHaveProperty('cuftData');

    expect(response.body.cuftData[0]).toHaveProperty('id');
    expect(response.body.cuftData[0]).toHaveProperty('cuftDataFileId');
    expect(response.body.cuftData[0]).toHaveProperty('filingCompanyName');
    expect(response.body.cuftData[0]).toHaveProperty('cuftBorrowerName');
    expect(response.body.cuftData[0]).toHaveProperty('cuftBorrowerCountry');
    expect(response.body.cuftData[0]).toHaveProperty('creditRatings');
    expect(response.body.cuftData[0].creditRatings).toHaveProperty('id');
    expect(response.body.cuftData[0].creditRatings).toHaveProperty('creditRatingName');
    expect(response.body.cuftData[0].creditRatings).toHaveProperty('creditRatingValue');

    expect(response.body).toHaveProperty('summaryStatistics');
    expect(response.body.summaryStatistics).toHaveProperty('maximum');
    expect(response.body.summaryStatistics).toHaveProperty('upperQuartile');
    expect(response.body.summaryStatistics).toHaveProperty('median');
    expect(response.body.summaryStatistics).toHaveProperty('lowerQuartile');
    expect(response.body.summaryStatistics).toHaveProperty('minimum');
    expect(response.body.summaryStatistics).toHaveProperty('numberOfObservations');
  });
});
