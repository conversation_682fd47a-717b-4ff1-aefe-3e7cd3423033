import request from 'supertest';

import app from '../../app';
import models from '../../models';
import { cuftDataFileRepository } from '../../repositories';
import redisService from '../../services/redis';
import cuftService from '../../services/cuftService';
import { authSetup, azureSetup, featureSetup, rateLimiterSetup, transactionSetup } from '../setup';
import { FileContentTypeEnum } from '../../enums/files';
import cuftDataFileMock from './__mocks__/cuftDataFile';

jest.mock('../../services/azureService');
jest.mock('../../middlewares/authMethodMiddleware');
jest.mock('../../middlewares/rateLimiter');
jest.mock('../../models');

beforeAll(() => {
  rateLimiterSetup.basicRateLimiterSetup();
  authSetup.superadminAuthSetup();
  azureSetup.basicAzureSetup();
  featureSetup.enabledFeatureSetup();
});

afterAll(async () => {
  await redisService.quit();
});

describe('CUFT Data File Controller', () => {
  test('It should get cuft data file', async () => {
    const mockCuftDataFile = jest.spyOn(cuftDataFileRepository, 'getCuftDataFile');
    mockCuftDataFile.mockImplementation(() => Promise.resolve(cuftDataFileMock));

    const response = await request(app).get('/api/cuft-data/file/:id');

    expect(response.statusCode).toBe(200);
    expect(response.headers['content-type']).toEqual(FileContentTypeEnum.XLSX);
    expect(response.headers['content-disposition']).toEqual(
      `attachment; filename="${cuftDataFileMock.name}${cuftDataFileMock.extension}"`,
    );
  });

  test('It should return 404 when cuft file is not found', async () => {
    const mockCuftDataFile = jest.spyOn(cuftDataFileRepository, 'getCuftDataFile');
    mockCuftDataFile.mockImplementation(() => Promise.resolve(null));

    const response = await request(app).get('/api/cuft-data/file/:id');

    expect(response.statusCode).toBe(404);
  });

  test('It should return all cuft data files', async () => {
    const mockCuftDataFiles = jest.spyOn(cuftDataFileRepository, 'getCuftDataFiles');
    mockCuftDataFiles.mockImplementation(() => Promise.resolve([cuftDataFileMock]));

    const response = await request(app).get('/api/cuft-data/file');

    expect(response.statusCode).toBe(200);
    expect(response.body[0]).toHaveProperty('id');
    expect(response.body[0]).toHaveProperty('name');
    expect(response.body[0]).toHaveProperty('extension');
    expect(response.body[0]).toHaveProperty('mimeType');
    expect(response.body[0]).toHaveProperty('createdAt');
    expect(response.body[0]).toHaveProperty('updatedAt');
  });

  test('It should upload cuft data file', async () => {
    transactionSetup.mockTransaction(models);

    const mockCreateCuftFile = jest.spyOn(cuftDataFileRepository, 'createCuftFile');
    mockCreateCuftFile.mockImplementation(() => Promise.resolve(cuftDataFileMock));

    const mockParseAndInsertCuftData = jest.spyOn(cuftService, 'parseAndInsertCuftData');
    mockParseAndInsertCuftData.mockImplementation(() => Promise.resolve());

    const response = await request(app).post('/api/cuft-data/file').attach('file', 'static/CuftDataExample.xlsx');

    expect(response.statusCode).toBe(200);
  });

  test('It should return 404 if file was not attached', async () => {
    const mockCreateCuftFile = jest.spyOn(cuftDataFileRepository, 'createCuftFile');
    mockCreateCuftFile.mockImplementation(() => Promise.resolve(cuftDataFileMock));

    const response = await request(app).post('/api/cuft-data/file');

    expect(response.statusCode).toBe(400);
  });

  test('It should delete client template file', async () => {
    transactionSetup.mockTransaction(models);

    const mockDeleteCuftFile = jest.spyOn(cuftDataFileRepository, 'deleteCuftFile');
    mockDeleteCuftFile.mockImplementation(() => Promise.resolve(1));

    const response = await request(app).delete('/api/cuft-data/file/:id');

    expect(response.statusCode).toBe(204);
  });
});
