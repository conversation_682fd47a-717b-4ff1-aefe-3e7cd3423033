import { DbCuftDataType } from '../../../types';

const cuftData: Required<DbCuftDataType> = {
  id: 1,
  cuftDataFileId: 1,
  filingCompanyName: 'Company name',
  cuftBorrowerName: 'Other Company name',
  cuftBorrowerCountry: 'USA',
  primarySic: 2390,
  moodyPrincipalObligorCreditRating: 'Ba2',
  spyPrincipalObligorCreditRating: 'BB',
  cuftTrancheExecutionDate: new Date(),
  cuftTrancheMaturityDate: new Date(),
  cuftTrancheTenor: 3.4,
  cuftTrancheAssetClass: 'Senior Secured',
  cuftTrancheType: 'Term',
  cuftTranchePrimaryReferenceRate: 'SOFR',
  allCurrencies: 'EUR; GBP; JPY',
  trancheOrderID: '00011901370223581010.1-T1',
  reviewCtrlrID: '00025683952223581010.1',
  exhibitLink: 'https://www.sec.gov/Archives/edgar/data/1122976/0001158425-22-369810-index.htm',
  deliveryDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
};

export default cuftData;
