import jsonwebtoken from 'jsonwebtoken';

import { builders } from '../setup';
import { authMethodMiddleware } from '../../middlewares';
import { ForbiddenError, UnauthorizedError } from '../../utils/ErrorHandler';
import { tokenRepository } from '../../repositories';
import userDataAccessToken from './__mocks__/userDataAccessToken';

const res = builders.buildRes();

describe('Auth Method Middleware', () => {
  test('It should fail if there is no authorization header', async () => {
    const req = builders.buildReq({ user: null, headers: {} });
    const next = builders.buildNext(() => new UnauthorizedError());

    await authMethodMiddleware(req, res, next);
    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalledWith(new UnauthorizedError());
  });

  test('It should fail if there is no bearer token', async () => {
    const req = builders.buildReq({ user: null, headers: { authorization: 'SomeRandomToken' } });
    const next = builders.buildNext(() => new UnauthorizedError());

    await authMethodMiddleware(req, res, next);
    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalledWith(new UnauthorizedError());
  });

  test('It should fail if there is no token in the database', async () => {
    const req = builders.buildReq({ user: null, headers: { authorization: 'Bearer to.k.en' } });
    const next = builders.buildNext(() => new UnauthorizedError());

    jest.spyOn(tokenRepository, 'getTokenByUserId').mockImplementation(() => Promise.resolve(null));

    await authMethodMiddleware(req, res, next);
    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalledWith(new UnauthorizedError());
  });

  test('It should delete token if token verification fails', async () => {
    const req = builders.buildReq({ user: null, headers: { authorization: 'Bearer to.k.en' } });
    const next = builders.buildNext(() => new UnauthorizedError());

    jest.spyOn(tokenRepository, 'getTokenByUserId').mockImplementation(() => Promise.resolve('to.k.en'));
    jest.spyOn(tokenRepository, 'deleteToken').mockImplementation(() => Promise.resolve({}));
    jest.spyOn(jsonwebtoken, 'verify').mockImplementation(() => {
      throw new jsonwebtoken.JsonWebTokenError('invalid signature');
    });

    await authMethodMiddleware(req, res, next);
    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalledWith(new UnauthorizedError());
  });

  test('It should set the req.user to the user object if token is valid', async () => {
    const req = builders.buildReq({ user: null, headers: { authorization: 'Bearer to.k.en' } });
    const next = builders.buildNext(() => new UnauthorizedError());

    jest.spyOn(tokenRepository, 'getTokenByUserId').mockImplementation(() => Promise.resolve('to.k.en'));
    jest.spyOn(jsonwebtoken, 'verify').mockImplementation(() => userDataAccessToken);

    await authMethodMiddleware(req, res, next);

    expect(req.user).toEqual(userDataAccessToken);
    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalled();
  });

  test('It should fail if the user is not a part of the organization for a given client specific url', async () => {
    const fakeOrigin = 'https://fakeclient.app.tpaccurate.com';

    const req = builders.buildReq({
      user: null,
      headers: { authorization: 'Bearer to.k.en' },
      get: jest.fn((headerName) => {
        if (headerName === 'X-Request-Origin') {
          return fakeOrigin;
        }
        return undefined;
      }),
    });
    const next = builders.buildNext(() => new ForbiddenError('Invalid url'));

    jest.spyOn(jsonwebtoken, 'verify').mockImplementation(() => userDataAccessToken);

    await authMethodMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalledWith(new ForbiddenError('Invalid url'));
  });

  test('It should set the req.user to the user object if the user is a part of the organization for a given client specific url', async () => {
    const fakeOrigin = 'https://fakeclient.app.tpaccurate.com';
    const fakeUser = { ...userDataAccessToken, clientName: 'fakeclient' };

    const req = builders.buildReq({
      user: null,
      headers: { authorization: 'Bearer to.k.en' },
      get: jest.fn((headerName) => {
        if (headerName === 'X-Request-Origin') {
          return fakeOrigin;
        }
        return undefined;
      }),
    });
    const next = builders.buildNext(() => new ForbiddenError());

    jest.spyOn(jsonwebtoken, 'verify').mockImplementation(() => fakeUser);

    await authMethodMiddleware(req, res, next);

    expect(req.user).toEqual(fakeUser);
    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalled();
  });

  test('It should not allow Mars users to access the default URL', async () => {
    const fakeOrigin = 'https://app.tpaccurate.com';
    const fakeUser = { ...userDataAccessToken, clientName: 'Mars' };

    const req = builders.buildReq({
      user: null,
      headers: { authorization: 'Bearer to.k.en' },
      get: jest.fn((headerName) => {
        if (headerName === 'X-Request-Origin') {
          return fakeOrigin;
        }
        return undefined;
      }),
    });
    const next = builders.buildNext(() => new ForbiddenError('Invalid url'));

    jest.spyOn(jsonwebtoken, 'verify').mockImplementation(() => fakeUser);

    await authMethodMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalledWith(new ForbiddenError('Invalid url'));
  });

  test('It should allow the superadmin to access any client specific url', async () => {
    const fakeOrigin = 'https://fakeclient.app.tpaccurate.com';
    const fakeUser = { ...userDataAccessToken, role: 'superadmin' };

    const req = builders.buildReq({
      user: null,
      headers: { authorization: 'Bearer to.k.en' },
      get: jest.fn((headerName) => {
        if (headerName === 'X-Request-Origin') {
          return fakeOrigin;
        }
        return undefined;
      }),
    });
    const next = builders.buildNext(() => new ForbiddenError());

    jest.spyOn(jsonwebtoken, 'verify').mockImplementation(() => fakeUser);

    await authMethodMiddleware(req, res, next);

    expect(req.user).toEqual(fakeUser);
    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalled();
  });
});
