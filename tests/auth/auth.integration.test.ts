import request from 'supertest';

import app from '../../app';
import models from '../../models';
import redisService from '../../services/redis';

afterAll(async () => {
  await models.sequelize.close();
  await redisService.quit();
});

describe('Username/password login integration test', () => {
  test('It should login user and make authenticated request', async () => {
    const username = 'admin';
    const password = 'nord2021';

    const loginResponse = await request(app).post('/api/user').send({ username, password });
    const { accessToken } = loginResponse.body;

    const meResponse = await request(app).get('/api/user/me').set('Authorization', `Bearer ${accessToken}`);
    expect(meResponse.body).toHaveProperty('id');
    expect(meResponse.body).toHaveProperty('clientId');
    expect(meResponse.body).toHaveProperty('client');
    expect(meResponse.body).toHaveProperty('features');
    expect(meResponse.body.username).toBe(username);
  });
});
