import { faker } from '@faker-js/faker';

import { dateFormats, decimalPoints, rolesEnum } from '../../../enums';
import { UserWithClientType } from '../../../types';

const clientId = 6;

const userWithClient: Required<UserWithClientType> = {
  id: 7,
  clientId,
  role: rolesEnum.ADMIN,
  username: faker.name.firstName(),
  areNotificationsMuted: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  dateFormat: dateFormats['DD-MM-YYYY'],
  decimalPoint: decimalPoints.COMMA,
  email: faker.internet.email(),
  fullName: faker.name.fullName(),
  timezone: 'Etc/GMT',
  client: {
    id: clientId,
    name: faker.company.name(),
  },
};

export default userWithClient;
