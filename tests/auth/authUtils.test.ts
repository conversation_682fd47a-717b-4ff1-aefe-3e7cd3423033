import * as authUtils from '../../auth/authUtils';
import userWithClient from './__mocks__/userWithClient';
import userDataAccessToken from './__mocks__/userDataAccessToken';
import { UserDataAccessTokenType } from '../../types';
import { tokenRepository } from '../../repositories';

describe('Auth Utils', () => {
  const env = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...env };
  });

  afterEach(() => {
    process.env = env;
  });

  test('It should return access token data based on the user provided', () => {
    const accessTokenData: UserDataAccessTokenType = authUtils.getAccessTokenData(userWithClient);

    expect(accessTokenData).toHaveProperty('id');
    expect(accessTokenData).toHaveProperty('clientId');
    expect(accessTokenData).toHaveProperty('clientName');
    expect(accessTokenData).toHaveProperty('role');
    expect(accessTokenData).toHaveProperty('username');
  });

  test('It fail to generate if the userId is not in the database', async () => {
    const mockCreateToken = jest.spyOn(tokenRepository, 'createToken');
    mockCreateToken.mockImplementation(() => {
      throw new Error('Key (userId)=(7) is not present in table "Users".');
    });

    try {
      await authUtils.generateAccessToken(userDataAccessToken);
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
    }
  });

  test('It fail to generate if token secret is not defined', async () => {
    const mockCreateToken = jest.spyOn(tokenRepository, 'createToken');
    mockCreateToken.mockImplementation(() => Promise.resolve());
    process.env.TOKEN_SECRET = undefined;

    try {
      await authUtils.generateAccessToken(userDataAccessToken);
    } catch (error: any) {
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('secretOrPrivateKey must have a value');
    }
  });

  test('It should generate an access token', async () => {
    const mockCreateToken = jest.spyOn(tokenRepository, 'createToken');
    mockCreateToken.mockImplementation(() => Promise.resolve());
    const jwtRegex = /^([A-Za-z0-9-_=]+\.)+([A-Za-z0-9-_=])+(\s?[A-Za-z0-9-_.+/=]*)?$/;

    const accessToken: string = await authUtils.generateAccessToken(userDataAccessToken);

    expect(accessToken).toMatch(jwtRegex);
  });
});
