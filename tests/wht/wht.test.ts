import request from 'supertest';

import app from '../../app';
import { authSetup, rateLimiterSetup } from '../setup';
import { whtRecipientRepository } from '../../repositories';
import redisService from '../../services/redis';
import recipientWithOriginWht from './__mocks__/recipientWithOriginWht';
import originWhtMock from './__mocks__/originWht';

jest.mock('../../repositories/countryRepository');
jest.mock('../../middlewares/authMethodMiddleware');
jest.mock('../../middlewares/rateLimiter');

beforeAll(() => {
  rateLimiterSetup.basicRateLimiterSetup();
  authSetup.superadminAuthSetup();
});

afterAll(async () => {
  await redisService.quit();
});

describe('WHT Controller', () => {
  describe('getWht', () => {
    test('It should return treaty WHT data based on origin and recipient', async () => {
      const mockGetRecipientWhtData = jest.spyOn(whtRecipientRepository, 'getLatestRecipientWhtData');
      mockGetRecipientWhtData.mockImplementation(() => Promise.resolve(recipientWithOriginWht));

      const body = { origin: 'United States of America', recipient: 'Australia' };

      const response = await request(app).post('/api/withholding-tax').send(body);

      expect(response.statusCode).toBe(200);
      expect(response.body).toHaveProperty('whtData');
      expect(response.body).toHaveProperty('isDefault');
      expect(response.body.isDefault).toBe(false);
      expect(response.body.whtData).toHaveProperty('id');
      expect(response.body.whtData).toHaveProperty('originId');
      expect(response.body.whtData).toHaveProperty('createdAt');
      expect(response.body.whtData).toHaveProperty('country');
      expect(response.body.whtData).toHaveProperty('dividend');
      expect(response.body.whtData).toHaveProperty('interest');
      expect(response.body.whtData).toHaveProperty('royalty');
      expect(response.body.whtData.origin).toHaveProperty('id');
      expect(response.body.whtData.origin).toHaveProperty('country');
      expect(response.body.whtData.origin).toHaveProperty('dividend');
      expect(response.body.whtData.origin).toHaveProperty('interest');
      expect(response.body.whtData.origin).toHaveProperty('royalty');
      expect(response.body.whtData.origin).toHaveProperty('date');
    });

    test('It should return headline WHT data based on origin and recipient', async () => {
      const mockGetRecipientWhtData = jest.spyOn(whtRecipientRepository, 'getLatestRecipientWhtData');
      mockGetRecipientWhtData.mockImplementation(() => Promise.resolve(null));
      const mockGetOriginWhtData = jest.spyOn(whtRecipientRepository, 'getOriginWhtData');
      mockGetOriginWhtData.mockImplementation(() => Promise.resolve(originWhtMock));

      const body = { origin: 'United States of America', recipient: 'Saudi Arabia' };

      const response = await request(app).post('/api/withholding-tax').send(body);

      expect(response.statusCode).toBe(200);
      expect(response.body).toHaveProperty('whtData');
      expect(response.body).toHaveProperty('isDefault');
      expect(response.body.isDefault).toBe(true);
      expect(response.body.whtData).toHaveProperty('id');
      expect(response.body.whtData).toHaveProperty('date');
      expect(response.body.whtData).toHaveProperty('country');
      expect(response.body.whtData).toHaveProperty('dividend');
      expect(response.body.whtData).toHaveProperty('interest');
      expect(response.body.whtData).toHaveProperty('royalty');
      expect(response.body.whtData).toHaveProperty('createdAt');
      expect(response.body.whtData).toHaveProperty('updatedAt');
    });
  });
});
