import { WHTRecipientWithOriginType } from '../../../types';

const recipientWithOriginWht: Required<WHTRecipientWithOriginType> = {
  id: 66013,
  originId: 1604,
  countryId: 13,
  country: 'Australia',
  dividend: '0/5/15',
  interest: '0/10',
  royalty: '5',
  interestExplanation:
    'The 0% rate applies to interest paid to a financial institution that is unrelated to and dealing wholly independently with the payer; otherwise, the rate is 10%.\n\nThe interest article of US treaties should be consulted for special rules that may apply to residual interest from real estate mortgage investment conduits, certain contingent interest, and interest on certain back-to-back loans.',
  createdAt: new Date('2022-10-04'),
  updatedAt: new Date('2022-10-04'),
  origin: {
    id: 1604,
    countryId: 238,
    country: 'United States',
    dividend: '30',
    interest: '0/30',
    royalty: '30',
    date: new Date('2022-10-04'),
    createdAt: new Date('2022-10-04'),
    updatedAt: new Date('2022-10-04'),
  },
};

export default recipientWithOriginWht;
