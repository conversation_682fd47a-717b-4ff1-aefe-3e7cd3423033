import request from 'supertest';

import app from '../app';
import redisService from '../services/redis';
import { rateLimiterSetup } from './setup';

jest.mock('../middlewares/rateLimiter');

beforeAll(() => {
  rateLimiterSetup.basicRateLimiterSetup();
});

afterAll(async () => {
  await redisService.quit();
});

describe('Node version', () => {
  test('It should be 14', () => {
    expect(process.versions.node).toMatch(/^14\./);
  });
});

describe('Test the root endpoint', () => {
  test('It should response the GET method', async () => {
    const response = await request(app).get('/');
    expect(response.statusCode).toBe(200);
  });
});
