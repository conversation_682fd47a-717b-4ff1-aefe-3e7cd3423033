import { Buff<PERSON> } from 'buffer';
import { BlobUploadCommonResponse } from '@azure/storage-blob';

import * as azureService from '../../services/azureService/azureService';

export const basicAzureSetup = () => {
  const mockAzureServiceUploadFile = jest.spyOn(azureService, 'uploadFile');
  mockAzureServiceUploadFile.mockImplementation(() => Promise.resolve({} as BlobUploadCommonResponse));

  const mockAzureServiceGetFile = jest.spyOn(azureService, 'getFile');
  mockAzureServiceGetFile.mockImplementation(() => Promise.resolve(Buffer.allocUnsafe(1)));

  const mockAzureServiceDeleteFile = jest.spyOn(azureService, 'deleteFile');
  mockAzureServiceDeleteFile.mockImplementation(() => Promise.resolve());
};
