import { Request, Response, NextFunction } from 'express';

import { rolesEnum } from '../../enums';
import { UserDataAccessTokenType, RoleType } from '../../types';

type BuildUserOverrides = {
  id?: number;
  clientId?: number;
  clientName?: string;
  username?: string;
  role?: RoleType;
};

export const buildUser = (overrides: BuildUserOverrides = {}): Partial<UserDataAccessTokenType> => {
  return {
    id: 1,
    clientId: 1,
    clientName: 'DemoClient',
    username: 'demouser',
    role: rolesEnum.SUPERADMIN,
    ...overrides,
  };
};

export function buildReq({ user = buildUser(), ...overrides }: Record<string, any> = {}) {
  const req = { user, body: {}, params: {}, get: jest.fn(), ...overrides } as unknown as Request;
  return req;
}

export function buildRes(overrides = {}) {
  const res = {
    json: jest.fn(() => res).mockName('json'),
    status: jest.fn(() => res).mockName('status'),
    clearCookie: jest.fn(),
    ...overrides,
  } as unknown as Response;

  return res;
}

export function buildNext(impl?: (...args: any[]) => unknown): NextFunction {
  return jest.fn(impl).mockName('next');
}
