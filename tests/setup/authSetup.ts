import * as authMethodMiddleware from '../../middlewares/authMethodMiddleware';
import { rolesEnum } from '../../enums';

export const superadminAuthSetup = () => {
  const authMethodMiddlewareMock = jest.spyOn(authMethodMiddleware, 'default');
  authMethodMiddlewareMock.mockImplementation(async (req, _, next) => {
    req.user = { clientId: 1, role: rolesEnum.SUPERADMIN };
    next();
  });
};

export const adminAuthSetup = () => {
  const authMethodMiddlewareMock = jest.spyOn(authMethodMiddleware, 'default');
  authMethodMiddlewareMock.mockImplementation(async (req, _, next) => {
    req.user = { clientId: 1, role: rolesEnum.ADMIN };
    next();
  });
};

export const userAuthSetup = () => {
  const authMethodMiddlewareMock = jest.spyOn(authMethodMiddleware, 'default');
  authMethodMiddlewareMock.mockImplementation(async (req, _, next) => {
    req.user = { clientId: 1, role: rolesEnum.USER };
    next();
  });
};
