import _ from 'lodash';

import models from '../../models';
import { getCountries } from '../../singletons';
import { countryToISOMapping } from '../../utils/creditRatingUtils';

const MAX_COUNTRY_INDEX = 250;

afterAll(async () => {
  await models.sequelize.close();
});

describe('Country Singleton', () => {
  test('It should check countries exists', async () => {
    const { countries } = await getCountries();

    expect(countries[0].name).toBeDefined();
    expect(countries[0].isoCode).toBeDefined();
    expect(countries[0].region).toBeDefined();
    expect(countries[0].cuftCode).toBeDefined();
    expect(countries[0].flagEmoji).toBeDefined();

    expect(countries[MAX_COUNTRY_INDEX].name).toBeDefined();
    expect(countries[MAX_COUNTRY_INDEX].isoCode).toBeDefined();
    expect(countries[MAX_COUNTRY_INDEX].region).toBeDefined();
    expect(countries[MAX_COUNTRY_INDEX].cuftCode).toBeDefined();
    expect(countries[MAX_COUNTRY_INDEX].flagEmoji).toBeDefined();
  });

  test('It should check countriesByName exists', async () => {
    const { countries, countriesByName } = await getCountries();

    const countryName = countries[0].name;
    const lastCountryName = countries[MAX_COUNTRY_INDEX].name;

    expect(countriesByName[countryName]?.name).toBeDefined();
    expect(countriesByName[countryName]?.isoCode).toBeDefined();
    expect(countriesByName[countryName]?.region).toBeDefined();
    expect(countriesByName[countryName]?.cuftCode).toBeDefined();
    expect(countriesByName[countryName]?.flagEmoji).toBeDefined();

    expect(countriesByName[lastCountryName]?.name).toBeDefined();
    expect(countriesByName[lastCountryName]?.isoCode).toBeDefined();
    expect(countriesByName[lastCountryName]?.region).toBeDefined();
    expect(countriesByName[lastCountryName]?.cuftCode).toBeDefined();
    expect(countriesByName[lastCountryName]?.flagEmoji).toBeDefined();
  });

  test('It should check if countries in the database match the countries hardcoded in code', async () => {
    const { countriesByName } = await getCountries();

    const countryNamesFromMapping = _.sortBy(Object.keys(countryToISOMapping));
    const countryNamesFromDatabase = _.sortBy(Object.keys(countriesByName));

    expect(countryNamesFromMapping).toEqual(countryNamesFromDatabase);
  });
});
