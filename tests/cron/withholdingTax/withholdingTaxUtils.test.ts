import { subHours } from 'date-fns';

import models from '../../../models';
import {
  checkOriginModifyDate,
  checkRecipientModifyDate,
  mapOriginObject,
  mapRecipientObject,
} from '../../../cron/withholdingTaxCron/withholdingTaxCronJobUtils';
import {
  CountryType,
  DeloitteApiWHTOriginType,
  DeloitteApiWHTRecipientType,
  DeloitteOriginType,
  DeloitteRecipientType,
} from '../../../types';
import { whtOriginRepository } from '../../../repositories';
import deloitteOriginMock from './__mocks__/deloitteOrigin';
import deloitteRecipientMock from './__mocks__/deloitteRecipient';
import dbOriginTypeMock from './__mocks__/dbOriginType';
import deloitteWhtOrigin from './__mocks__/deloitteWhtOrigin';
import { getCountries } from '../../../singletons';

afterAll(async () => {
  await models.sequelize.close();
});

describe('Withholding Tax Utils', () => {
  describe('Object mappers', () => {
    test('It should map to DeloitteApiWHTOriginType', async () => {
      const originCountryId = 54;
      const mappedOriginObject: DeloitteApiWHTOriginType = mapOriginObject(originCountryId, deloitteOriginMock);

      expect(mappedOriginObject.countryId).toBe(originCountryId);
      expect(mappedOriginObject).toHaveProperty('dividend');
      expect(mappedOriginObject).toHaveProperty('interest');
      expect(mappedOriginObject).toHaveProperty('royalty');
      expect(mappedOriginObject).toHaveProperty('date');
    });

    test('It should map to DeloitteApiWHTRecipientType', async () => {
      const originId = 7;
      const recipientCountryId = 12;
      const mappedOriginObject: DeloitteApiWHTRecipientType = mapRecipientObject(
        originId,
        recipientCountryId,
        deloitteRecipientMock,
      );

      expect(mappedOriginObject.originId).toBe(originId);
      expect(mappedOriginObject).toHaveProperty('countryId');
      expect(mappedOriginObject).toHaveProperty('dividend');
      expect(mappedOriginObject).toHaveProperty('interest');
      expect(mappedOriginObject).toHaveProperty('royalty');
    });
  });

  describe('Check origin modify date', () => {
    test('It should return undefined when modifyDate or jurisdictionName does not exist', async () => {
      const originCountryId = 7;
      const originMockWithoutModifyDate: DeloitteOriginType = { ...deloitteOriginMock, modifyDate: null };
      const mappedOriginObject1 = await checkOriginModifyDate(originMockWithoutModifyDate, originCountryId);

      const originMockWithoutJurisdictionName: DeloitteOriginType = { ...deloitteOriginMock, jurisdictionName: null };
      const mappedOriginObject2 = await checkOriginModifyDate(originMockWithoutJurisdictionName, originCountryId);

      expect(mappedOriginObject1).toBeUndefined();
      expect(mappedOriginObject2).toBeUndefined();
    });

    test('It should return undefined if the modifyDate was not in the last day', async () => {
      const originCountryId = 7;
      const originMock: DeloitteOriginType = {
        ...deloitteOriginMock,
        modifyDate: new Date('2022-04-20'),
      };
      const mappedOriginObject = await checkOriginModifyDate(originMock, originCountryId);

      expect(mappedOriginObject).toBeUndefined();
    });

    test('It should return DeloitteOriginMailDataType if the modifyDate was in the last day', async () => {
      const originCountryId = 7;
      const mockGetClientsFeatures = jest.spyOn(whtOriginRepository, 'getLatestOriginWhtData');
      mockGetClientsFeatures.mockImplementation(() => Promise.resolve(dbOriginTypeMock));

      const originMock: DeloitteOriginType = {
        ...deloitteOriginMock,
        modifyDate: new Date(),
      };
      const mappedOriginObject = await checkOriginModifyDate(originMock, originCountryId);

      expect(mappedOriginObject).toHaveProperty('country');
      expect(mappedOriginObject).toHaveProperty('previousInterestRate');
      expect(mappedOriginObject).toHaveProperty('currentInterestRate');
    });

    test('It should return DeloitteOriginMailDataType if the modifyDate was in the last day', async () => {
      const originCountryId = 7;
      const mockGetClientsFeatures = jest.spyOn(whtOriginRepository, 'getLatestOriginWhtData');
      mockGetClientsFeatures.mockImplementation(() => Promise.resolve(dbOriginTypeMock));

      const originMock: DeloitteOriginType = {
        ...deloitteOriginMock,
        modifyDate: subHours(new Date(), 23),
      };
      const mappedOriginObject = await checkOriginModifyDate(originMock, originCountryId);

      expect(mappedOriginObject).toHaveProperty('country');
      expect(mappedOriginObject).toHaveProperty('previousInterestRate');
      expect(mappedOriginObject).toHaveProperty('currentInterestRate');
    });

    test('It should return undefined if the modifyDate was in the last day but interest has not changed', async () => {
      const originCountryId = 7;
      const mockGetClientsFeatures = jest.spyOn(whtOriginRepository, 'getLatestOriginWhtData');
      mockGetClientsFeatures.mockImplementation(() => Promise.resolve(dbOriginTypeMock));

      const originMockWithSameInterest: Required<DeloitteOriginType> = {
        ...deloitteOriginMock,
        interest: dbOriginTypeMock.interest,
        modifyDate: subHours(new Date(), 23),
      };
      const mappedOriginObject = await checkOriginModifyDate(originMockWithSameInterest, originCountryId);

      expect(mappedOriginObject).toBeUndefined();
    });

    test('It should return undefined if the modifyDate was 24 hours or more before current time', async () => {
      const originCountryId = 7;
      const originMock: DeloitteOriginType = {
        ...deloitteOriginMock,
        modifyDate: subHours(new Date(), 24),
      };
      const mappedOriginObject = await checkOriginModifyDate(originMock, originCountryId);

      expect(mappedOriginObject).toBeUndefined();
    });
  });

  describe('Check recipient modify date', () => {
    test('It should return undefined when modifyDate or jurisdictionName does not exist', async () => {
      const { countriesByName } = await getCountries();
      const recipientMockWithoutModifyDate: DeloitteRecipientType = { ...deloitteRecipientMock, modifyDate: null };
      const recipientCountry = countriesByName[String(recipientMockWithoutModifyDate.longDesc)];
      if (!recipientCountry) {
        throw new Error('No recipient country found');
      }

      const mappedRecipientObject1 = await checkRecipientModifyDate(
        deloitteWhtOrigin,
        { name: 'Algeria' } as CountryType,
        recipientMockWithoutModifyDate,
        recipientCountry,
      );

      const recipientMockWithoutLongDesc: DeloitteRecipientType = { ...deloitteRecipientMock, longDesc: null };
      const mappedRecipientObject2 = await checkRecipientModifyDate(
        deloitteWhtOrigin,
        { name: 'Algeria' } as CountryType,
        recipientMockWithoutLongDesc,
        recipientCountry,
      );

      expect(mappedRecipientObject1).toBeUndefined();
      expect(mappedRecipientObject2).toBeUndefined();
    });
  });
});
