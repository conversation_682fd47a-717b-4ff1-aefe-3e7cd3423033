import { builders } from '../setup';
import { featureNames } from '../../enums';
import { features } from '../../middlewares';
import { featureRepository } from '../../repositories';
import { ForbiddenError, InternalServerError } from '../../utils/ErrorHandler';
import { ClientFeatureWithFeatureType } from '../../types';

const req = builders.buildReq();
const res = builders.buildRes();

describe('Features', () => {
  test('It should throw InternalServerError if incorrect feature name is passed', async () => {
    const next = builders.buildNext();

    await expect(features(featureNames.GEOGRAPHY_DATA)(req, res, next)).rejects.toThrow(InternalServerError);
    await expect(features('' as featureNames)(req, res, next)).rejects.toThrow(InternalServerError);

    expect(next).not.toHaveBeenCalled();
  });

  test('It should call next with ForbiddenError if feature is not enabled', async () => {
    const next = builders.buildNext();

    const mockGetClientFeature = jest.spyOn(featureRepository, 'getClientFeatureByName');
    mockGetClientFeature.mockImplementationOnce(() =>
      Promise.resolve({ isEnabled: false } as ClientFeatureWithFeatureType),
    );

    await features(featureNames.LOAN)(req, res, next);

    expect(next).toHaveBeenCalledWith(new ForbiddenError());
  });

  test('It should call next if feature is enabled', async () => {
    const next = builders.buildNext();

    const mockGetClientFeature = jest.spyOn(featureRepository, 'getClientFeatureByName');
    mockGetClientFeature.mockImplementationOnce(() =>
      Promise.resolve({ isEnabled: true } as ClientFeatureWithFeatureType),
    );

    await features(featureNames.LOAN)(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
  });
});
