import { handleError } from '../../middlewares';
import logger from '../../utils/logger';
import { builders } from '../setup';
import { EnvironmentType } from '../../types';

const error = { statusCode: 500, message: 'Error message', name: 'CustomError' };
const req = builders.buildReq();

describe('Handle error', () => {
  /** Necessary so the test runs don't interfere with each other */
  let originalLoggerError: any;
  let originalNodeEnv: EnvironmentType;

  beforeEach(() => {
    originalLoggerError = logger.error;
    logger.error = jest.fn();

    originalNodeEnv = process.env.NODE_ENV;
  });

  afterEach(() => {
    logger.error = originalLoggerError;

    process.env.NODE_ENV = originalNodeEnv;
  });

  test('It should call the next middleware', () => {
    const res = builders.buildRes();
    const next = builders.buildNext();

    handleError(error, req, res, next);

    expect(logger.error).toBeCalledTimes(1);
    expect(logger.error).toBeCalledWith({ message: error.message, error, payload: [] });
    expect(next).toBeCalledTimes(1);
  });

  test('It should call the logger error function for error with status >= 500', () => {
    const res = builders.buildRes();
    const next = builders.buildNext();

    handleError(error, req, res, next);

    expect(logger.error).toBeCalledTimes(1);
    expect(logger.error).toBeCalledWith({ message: error.message, error, payload: [] });
    expect(next).toBeCalledTimes(1);
  });

  test('It should call the res.json and res.status with proper attributes', () => {
    const res = builders.buildRes();
    const next = builders.buildNext();

    handleError(error, req, res, next);

    expect(res.status).toBeCalledWith(error.statusCode);
    expect(res.json).toBeCalledWith({
      status: 'error',
      statusCode: error.statusCode,
      message: error.message,
      name: error.name,
      payload: [],
    });
    expect(logger.error).toBeCalledTimes(1);
    expect(res.status).toBeCalledTimes(1);
    expect(res.json).toBeCalledTimes(1);
    expect(next).toBeCalledTimes(1);
  });

  test('It should call the res.json with certain attributes omitted for production errors with status >= 500', () => {
    const res = builders.buildRes();
    const next = builders.buildNext();
    process.env.NODE_ENV = 'production';

    handleError(error, req, res, next);

    expect(logger.error).toBeCalledTimes(1);
    expect(logger.error).toBeCalledWith({ message: error.message, error, payload: [] });
    expect(res.json).toBeCalledWith({ status: 'error', statusCode: error.statusCode });
    expect(next).toBeCalledTimes(1);
  });

  test('It should not call the logger error function for error with status < 500', () => {
    const res = builders.buildRes();
    const next = builders.buildNext();
    const errorClone = { ...error, statusCode: 400 };

    handleError(errorClone, req, res, next);

    expect(logger.error).not.toBeCalled();
    expect(res.json).toBeCalledWith({
      status: 'error',
      statusCode: errorClone.statusCode,
      message: errorClone.message,
      name: errorClone.name,
      payload: [],
    });
    expect(next).toBeCalledTimes(1);
  });

  test('It should call the res.json with certain arguments for production errors with status < 500', () => {
    const res = builders.buildRes();
    const next = builders.buildNext();
    const errorClone = { ...error, statusCode: 400 };
    process.env.NODE_ENV = 'production';

    handleError(errorClone, req, res, next);

    expect(logger.error).not.toBeCalled();
    expect(res.json).toBeCalledWith({
      status: 'error',
      statusCode: errorClone.statusCode,
      message: errorClone.message,
      name: errorClone.name,
      payload: [],
    });
    expect(next).toBeCalledTimes(1);
  });
});
