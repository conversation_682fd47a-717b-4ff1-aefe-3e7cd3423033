import { builders } from '../setup';
import { rolesEnum } from '../../enums';
import { permissions } from '../../middlewares';
import { ForbiddenError, InternalServerError } from '../../utils/ErrorHandler';

const res = builders.buildRes();

describe('Permissions', () => {
  test('It should throw InternalServerError if array is not passed for roles', () => {
    const req = builders.buildReq();
    const next = builders.buildNext();

    expect(() => permissions('' as any)(req, res, next)).toThrow(InternalServerError);
    expect(() => permissions(null as any)(req, res, next)).toThrow(InternalServerError);
    expect(() => permissions(undefined as any)(req, res, next)).toThrow(InternalServerError);
    expect(() => permissions({} as any)(req, res, next)).toThrow(InternalServerError);

    expect(next).not.toHaveBeenCalled();
  });

  test('It should throw InternalServerError if an empty array is passed for roles', () => {
    const req = builders.buildReq();
    const next = builders.buildNext();

    expect(() => permissions([])(req, res, next)).toThrow(InternalServerError);

    expect(next).not.toHaveBeenCalled();
  });

  test('It should throw ForbiddenError if there is no user role in req object', () => {
    const req = builders.buildReq({ user: { role: undefined } });
    const next = builders.buildNext();

    expect(() => permissions([rolesEnum.SUPERADMIN])(req, res, next)).toThrow(ForbiddenError);

    expect(next).not.toHaveBeenCalled();
  });

  test("It should throw ForbiddenError if user's role is not in the allowed roles 1", () => {
    const req = builders.buildReq({ user: { role: rolesEnum.ADMIN } });
    const next = builders.buildNext();

    expect(() => permissions([rolesEnum.SUPERADMIN])(req, res, next)).toThrow(ForbiddenError);

    expect(next).not.toHaveBeenCalled();
  });

  test("It should throw ForbiddenError if user's role is not in the allowed roles 2", () => {
    const req = builders.buildReq({ user: { role: rolesEnum.USER } });
    const next = builders.buildNext();

    expect(() => permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN])(req, res, next)).toThrow(ForbiddenError);

    expect(next).not.toHaveBeenCalled();
  });

  test("It should not throw if user's role is in the allowed roles 1", () => {
    const req = builders.buildReq({ user: { role: rolesEnum.ADMIN } });
    const next = builders.buildNext();

    permissions([rolesEnum.ADMIN])(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
  });

  test("It should not throw if user's role is in the allowed roles 2", () => {
    const req = builders.buildReq({ user: { role: rolesEnum.ADMIN } });
    const next = builders.buildNext();

    permissions([rolesEnum.ADMIN, rolesEnum.SUPERADMIN])(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
  });
});
