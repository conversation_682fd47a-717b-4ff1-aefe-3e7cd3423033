import { builders } from '../setup';
import { xssMiddleware } from '../../middlewares';
import { XSSError } from '../../utils/ErrorHandler';
import logger from '../../utils/logger';

const res = builders.buildRes();

describe('XSS', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('It should just call next if req.body is empty', () => {
    const req = builders.buildReq();
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).not.toHaveBeenCalled();
  });

  test('It should just call next if ignored path is used', () => {
    const strippedString = 'some value';
    const value = `<script>${strippedString}</script>`;

    const req = builders.buildReq({ body: { a: 'a1', b: 'b1', c: { c1: 'c2', value } }, path: '/api/user' });
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).not.toHaveBeenCalled();
  });

  test('It should just call next if req.body does not contain any XSS', () => {
    const req = builders.buildReq({ body: { value: 'test' } });
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).not.toHaveBeenCalled();
  });

  test('It should log an error if there is XSS in req.body 1', () => {
    const value = '<script>test';
    const strippedString = '[removed]test';

    const req = builders.buildReq({ body: { value } });
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).toHaveBeenCalledWith({
      message: 'XSS attempted',
      error: new XSSError(),
      payload: { value, strippedString },
    });
  });

  test('It should log an error if there is XSS in req.body 2', () => {
    const strippedString = 'test';
    const value = `<b>${strippedString}`;

    const req = builders.buildReq({ body: { value } });
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).toHaveBeenCalledWith({
      message: 'XSS attempted',
      error: new XSSError(),
      payload: { value, strippedString },
    });
  });

  test('It should log an error if there is XSS in req.body 3', () => {
    const value = '<b>test</b>something else';
    const strippedString = 'testsomething else';

    const req = builders.buildReq({ body: { value } });
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).toHaveBeenCalledWith({
      message: 'XSS attempted',
      error: new XSSError(),
      payload: { value, strippedString },
    });
  });

  test('It should log an error if there is XSS in req.body and completely remove value for <script> tag', () => {
    const randomValue = 'some value';
    const value = `<script>${randomValue}</script>`;

    const req = builders.buildReq({ body: { value } });
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).toHaveBeenCalledWith({
      message: 'XSS attempted',
      error: new XSSError(),
      payload: { value, strippedString: '' },
    });
  });

  test('It should log an error if there is XSS in req.body with non-existent tag 1', () => {
    const strippedString = 'test';
    const value = `<aaa>${strippedString}`;

    const req = builders.buildReq({ body: { value } });
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).toHaveBeenCalledWith({
      message: 'XSS attempted',
      error: new XSSError(),
      payload: { value, strippedString },
    });
  });

  test('It should log an error if there is XSS in req.body with non-existent tag 2', () => {
    const strippedString = 'some value';
    const value = `<NOT_REAL_TAG>${strippedString}</NOT_REAL_TAG>`;

    const req = builders.buildReq({ body: { value } });
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).toHaveBeenCalledWith({
      message: 'XSS attempted',
      error: new XSSError(),
      payload: { value, strippedString },
    });
  });

  test('It should log an error if there is XSS in deeper level of req.body', () => {
    const strippedString = 'some value';
    const value = `<NOT_REAL_TAG>${strippedString}</NOT_REAL_TAG>`;

    const req = builders.buildReq({ body: { a: 'a1', b: 'b1', c: { c1: 'c2', value } } });
    const next = builders.buildNext();
    jest.spyOn(logger, 'error').mockImplementation(jest.fn);

    xssMiddleware(req, res, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(logger.error).toHaveBeenCalledWith({
      message: 'XSS attempted',
      error: new XSSError(),
      payload: { value, strippedString },
    });
  });
});
