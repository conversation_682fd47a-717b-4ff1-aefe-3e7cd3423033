import request from 'supertest';

import app from '../../app';
import { authSetup, rateLimiterSetup } from '../setup';
import { featureRepository } from '../../repositories';
import redisService from '../../services/redis';
import clientFeatureMock from './__mocks__/clientFeature';
import featureMock from './__mocks__/feature';

jest.mock('../../middlewares/authMethodMiddleware');
jest.mock('../../middlewares/rateLimiter');

beforeAll(() => {
  rateLimiterSetup.basicRateLimiterSetup();
  authSetup.superadminAuthSetup();
});

afterAll(async () => {
  await redisService.quit();
});

describe('Client Feature Controller', () => {
  test('It should get all client features', async () => {
    const mockGetClientsFeatures = jest.spyOn(featureRepository, 'getAllClientFeatures');
    mockGetClientsFeatures.mockImplementation(() => Promise.resolve([{ ...clientFeatureMock, feature: featureMock }]));

    const response = await request(app).get('/api/feature');

    expect(response.statusCode).toBe(200);
    expect(response.body).toBeInstanceOf(Array);
    expect(response.body[0]).toHaveProperty('id');
    expect(response.body[0]).toHaveProperty('clientId');
    expect(response.body[0]).toHaveProperty('featureId');
    expect(response.body[0]).toHaveProperty('isEnabled');
    expect(response.body[0]).toHaveProperty('values');
    expect(response.body[0]).toHaveProperty('feature');
  });

  test('It should get features of a specific client', async () => {
    const mockGetClientsFeatures = jest.spyOn(featureRepository, 'getAllClientFeatures');
    mockGetClientsFeatures.mockImplementation(() => Promise.resolve([{ ...clientFeatureMock, feature: featureMock }]));

    const response = await request(app).get('/api/feature/client/:clientId');

    expect(response.statusCode).toBe(200);
    expect(response.body).toBeInstanceOf(Array);
    expect(response.body[0]).toHaveProperty('id');
    expect(response.body[0]).toHaveProperty('clientId');
    expect(response.body[0]).toHaveProperty('featureId');
    expect(response.body[0]).toHaveProperty('isEnabled');
    expect(response.body[0]).toHaveProperty('values');
    expect(response.body[0]).toHaveProperty('feature');
  });
});
