import { Page, expect } from '@playwright/test';

const USERNAME = process.env.TEST_USERNAME!;
const PASSWORD = process.env.TEST_PASSWORD!;

const loginAsAdmin = async (page: Page) => {
  await page.goto('/login');
  await expect(page).toHaveURL('/login');
  await page.locator('input[type="text"]').click();
  await page.locator('input[type="text"]').fill(USERNAME);
  await page.locator('input[type="password"]').click();
  await page.locator('input[type="password"]').fill(PASSWORD);
  await page.locator('input[type="password"]').click();
  await page.locator('text=Log In').first().click();
  await page.waitForURL('/dashboard');
  await expect(page).toHaveURL('/dashboard');
};

export { loginAsAdmin };
