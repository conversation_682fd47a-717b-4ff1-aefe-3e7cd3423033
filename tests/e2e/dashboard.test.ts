import { test, expect } from '@playwright/test';

import { loginAsAdmin } from './utils';

test.beforeEach(async ({ page }) => {
  await loginAsAdmin(page);
});

test.describe('Dashboard', () => {
  test('Dashboard elements', async ({ page }) => {
    await expect(page.locator('[data-test-id=exchangeRatesContainer]')).toBeVisible();
    await expect(page.locator('[data-test-id=worldMapContainer]')).toBeVisible();
    await expect(page.locator('text=Recently Priced Loans')).toBeVisible();
  });
});
