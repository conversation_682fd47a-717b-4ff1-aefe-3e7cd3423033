import { test, expect } from '@playwright/test';

import { loginAsAdmin, singleSelectFirstElementSelector, currencySingleSelectFirstElementSelector } from './utils';

test.beforeEach(async ({ page }) => {
  await loginAsAdmin(page);
});

test.describe('Cash Pool', () => {
  test('Create and Delete Physical Cash Pool', async ({ page }) => {
    await page.goto('/cash-pools/new');
    await expect(page).toHaveURL('/cash-pools/new');
    await page.locator('[data-test-id=cashPoolNameInput] >> input').fill('E2E Physical Cash Pool');

    await page.locator('[data-test-id=cashPoolCurrencySelect]').click();
    await page.locator(`[data-test-id=cashPoolCurrencySelect] ${currencySingleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=cashPoolCountrySelect]').click();
    await page.locator(`[data-test-id=cashPoolCountrySelect] ${singleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=cashPoolLeaderSelect]').click();
    await page.locator(`[data-test-id=cashPoolLeaderSelect] ${singleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=cirInput] >> input').fill('4');
    await page.locator('[data-test-id=dirInput] >> input').fill('8');
    await page.locator('[data-test-id=operatingExpensesInput] >> input').fill('200');
    await page.locator('[data-test-id=operatingExpensesMarkupInput] >> input').fill('6');

    await page.locator('[data-test-id=functionalAnalysisCollapsible]').click();
    await page.locator('[data-test-id=functionalAnalysisCalculateButton]').click();

    await page.locator('[data-test-id=participantsActionMenu] >> nth=0').first().click();
    await page.locator('text=Edit credit and debit rates').first().click();
    await page.locator('[data-test-id=standaloneCirInput] >> input').fill('3');
    await page.locator('[data-test-id=standaloneDirInput] >> input').fill('9');
    await page.locator('[data-test-id=cirDirEditSubmitButton]').click();

    await page.locator('[data-test-id=participantsActionMenu] >> nth=1').first().click();
    await page.locator('text=Edit credit and debit rates').first().click();
    await page.locator('[data-test-id=standaloneCirInput] >> input').fill('3');
    await page.locator('[data-test-id=standaloneDirInput] >> input').fill('9');
    await page.locator('[data-test-id=cirDirEditSubmitButton]').click();

    await page.locator('[data-test-id=createCashPoolButton]').click();

    await expect(page).toHaveURL(/cash-pools\/[0-9]+/);

    await page.locator('[data-test-id=cashPoolViewActionMenu]').click();
    // plus sign is for getting adjacent div
    await page.locator('[data-test-id=cashPoolViewActionMenu] + div >> text=delete').click();

    await page.locator('[data-test-id=confirmDeleteCashPoolButton]').click();

    await expect(page).toHaveURL(/cash-pools/);
  });
});
