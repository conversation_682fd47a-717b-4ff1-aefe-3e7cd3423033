import { test, expect } from '@playwright/test';

import { loginAsAdmin, singleSelectFirstElementSelector, currencySingleSelectFirstElementSelector } from './utils';

test.beforeEach(async ({ page }) => {
  await loginAsAdmin(page);
});

test.describe('Price and Benchmark', () => {
  test('Price and Benchmark Loan elements', async ({ page }) => {
    await page.goto('/price-and-benchmark');
    await expect(page).toHaveURL('/price-and-benchmark');
    await expect(page.locator('[data-test-id=lenderCompanySelect]')).toBeVisible();
    await expect(page.locator('[data-test-id=borrowerCompanySelect]')).toBeVisible();
    await expect(page.locator('[data-test-id=issueDateInput]')).toBeVisible();

    const calendarButton = page.locator('[data-test-id=calendarButton]');
    await expect(page.locator('[data-test-id=tenorYearInput]')).toBeVisible();
    await expect(page.locator('[data-test-id=tenorMonthInput]')).toBeVisible();
    await expect(calendarButton).toBeVisible();
    await calendarButton.click();
    await expect(page.locator('[data-test-id=endDateInput]')).toBeVisible();

    await expect(page.locator('[data-test-id=currencySelect]')).toBeVisible();
    await expect(page.locator('[data-test-id=principalAmountInput]')).toBeVisible();
    await expect(page.locator('[data-test-id=paymentFrequencySelect]')).toBeVisible();
    await expect(page.locator('[data-test-id=senioritySelect]')).toBeVisible();
    await expect(page.locator('[data-test-id=loanTypeSelect]')).toBeVisible();
  });

  test('Price and Benchmark Loan Create and Delete', async ({ page }) => {
    await page.goto('/price-and-benchmark');
    await expect(page).toHaveURL('/price-and-benchmark');
    await page.locator('[data-test-id=lenderCompanySelect]').click();
    await page.locator(`[data-test-id=lenderCompanySelect] ${singleSelectFirstElementSelector}`).click();
    await page.locator('[data-test-id=borrowerCompanySelect]').click();
    await page.locator(`[data-test-id=borrowerCompanySelect] ${singleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=issueDateInput]').click();
    await page.locator('.react-datepicker >> :nth-match(select, 1)').selectOption('September');
    await page.locator('.react-datepicker >> :nth-match(select, 2)').selectOption('2022');
    await page.locator('[aria-label="Choose Tuesday\\, August 30th\\, 2022"]').click();

    await page.locator('[data-test-id=calendarButton]').click();
    await page.locator('[data-test-id=endDateInput]').click();
    await page.locator('.react-datepicker >> :nth-match(select, 1)').selectOption('September');
    await page.locator('.react-datepicker >> :nth-match(select, 2)').selectOption('2022');
    await page.locator('[aria-label="Choose Friday\\, September 30th\\, 2022"]').click();

    await page.locator('[data-test-id=currencySelect]').click();
    await page.locator(`[data-test-id=currencySelect] ${currencySingleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=principalAmountInput] >> input').fill('200000');

    page.locator('[data-test-id=paymentFrequencySelect]').click();
    await page.locator(`[data-test-id=paymentFrequencySelect] ${singleSelectFirstElementSelector}`).click();

    page.locator('[data-test-id=senioritySelect]').click();
    await page.locator(`[data-test-id=senioritySelect] ${singleSelectFirstElementSelector}`).click();

    page.locator('[data-test-id=loanTypeSelect]').click();
    await page.locator(`[data-test-id=loanTypeSelect] ${singleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=priceLoanGuaranteeButton]').click();

    await page.locator('[data-test-id=submitPricingButton]').click();

    await expect(page).toHaveURL(/analyses\/[0-9]+\?reportType=loan/);
    await page.locator('[data-test-id=successToast]').click();

    await page.locator('[data-test-id=reportViewActionMenu]').click();
    await page.locator('text=Delete').click();

    await page.locator('[data-test-id=confirmDeleteReportButton]').click();

    await page.goto('/price-and-benchmark');
  });

  test('Price and Benchmark Loan Create - BRL and COP not available', async ({ page }) => {
    const currencyBRL = 'BRL - Brazilian Real';
    const currencyCOP = 'COP - Colombian Peso';
    const partOfErrorMessage = 'data is not available before 22 September 2022.';

    await page.goto('/price-and-benchmark');
    await expect(page).toHaveURL('/price-and-benchmark');
    await page.locator('[data-test-id=lenderCompanySelect]').click();
    await page.locator(`[data-test-id=lenderCompanySelect] ${singleSelectFirstElementSelector}`).click();
    await page.locator('[data-test-id=borrowerCompanySelect]').click();
    await page.locator(`[data-test-id=borrowerCompanySelect] ${singleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=issueDateInput]').click();
    await page.locator('.react-datepicker >> :nth-match(select, 1)').selectOption('September');
    await page.locator('.react-datepicker >> :nth-match(select, 2)').selectOption('2022');
    await page.locator('[aria-label="Choose Wednesday\\, September 21st\\, 2022"]').click();

    await page.locator('[data-test-id=tenorYearInput] >> input').fill('7');

    await page.locator('[data-test-id=currencySelect]').click();
    await page.locator(`text=${currencyBRL}`).click();

    await page.locator('[data-test-id=principalAmountInput] >> input').fill('200000');

    page.locator('[data-test-id=paymentFrequencySelect]').click();
    await page.locator(`[data-test-id=paymentFrequencySelect] ${singleSelectFirstElementSelector}`).click();

    page.locator('[data-test-id=senioritySelect]').click();
    await page.locator(`[data-test-id=senioritySelect] ${singleSelectFirstElementSelector}`).click();

    page.locator('[data-test-id=loanTypeSelect]').click();
    await page.locator(`[data-test-id=loanTypeSelect] ${singleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=priceLoanGuaranteeButton]').click();
    await page.locator('[data-test-id=submitPricingButton]').click();

    await expect(page.locator(`text=${partOfErrorMessage}`)).toBeVisible();
    // errorToast click clears the submit modal and error toast
    // but it doesn't disappear immediately because of the animation so wait until { visible: false } is necessary
    await page.locator('[data-test-id=errorToast]').click();
    await expect(page.locator('[data-test-id=errorToast]')).toBeVisible({ visible: false });

    await page.locator('[data-test-id=currencySelect]').click();
    await page.locator(`text=${currencyCOP}`).click();

    await page.locator('[data-test-id=priceLoanGuaranteeButton]').click();

    await page.locator('[data-test-id=submitPricingButton]').click();
    await expect(page.locator(`text=${partOfErrorMessage}`)).toBeVisible();
  });

  test('Price and Benchmark Guarantee elements', async ({ page }) => {
    await page.goto('/price-and-benchmark');
    await expect(page).toHaveURL('/price-and-benchmark');
    await page.locator('[data-test-id=guaranteeTab]').click();
    await expect(page.locator('[data-test-id=guarantorCompanySelect]')).toBeVisible();
    await expect(page.locator('[data-test-id=principalCompanySelect]')).toBeVisible();
    await expect(page.locator('[data-test-id=issueDateInput]')).toBeVisible();

    const calendarButton = page.locator('[data-test-id=calendarButton]');
    await expect(page.locator('[data-test-id=tenorYearInput]')).toBeVisible();
    await expect(page.locator('[data-test-id=tenorMonthInput]')).toBeVisible();
    await expect(calendarButton).toBeVisible();
    await calendarButton.click();
    await expect(page.locator('[data-test-id=endDateInput]')).toBeVisible();

    await expect(page.locator('[data-test-id=currencySelect]')).toBeVisible();
    await expect(page.locator('[data-test-id=principalAmountInput]')).toBeVisible();
    await expect(page.locator('[data-test-id=paymentFrequencySelect]')).toBeVisible();
    await expect(page.locator('[data-test-id=senioritySelect]')).toBeVisible();
  });

  test('Price and Benchmark Guarantee Create and Delete', async ({ page }) => {
    await page.goto('/price-and-benchmark');
    await expect(page).toHaveURL('/price-and-benchmark');
    await page.locator('[data-test-id=guaranteeTab]').click();
    await page.locator('[data-test-id=guarantorCompanySelect]').click();
    await page.locator(`[data-test-id=guarantorCompanySelect] ${singleSelectFirstElementSelector}`).click();
    await page.locator('[data-test-id=principalCompanySelect]').click();
    await page.locator(`[data-test-id=principalCompanySelect] ${singleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=issueDateInput]').click();
    await page.locator('.react-datepicker >> :nth-match(select, 1)').selectOption('September');
    await page.locator('.react-datepicker >> :nth-match(select, 2)').selectOption('2022');
    await page.locator('[aria-label="Choose Tuesday\\, August 30th\\, 2022"]').click();

    await page.locator('[data-test-id=calendarButton]').click();
    await page.locator('[data-test-id=endDateInput]').click();
    await page.locator('.react-datepicker >> :nth-match(select, 1)').selectOption('September');
    await page.locator('.react-datepicker >> :nth-match(select, 2)').selectOption('2022');
    await page.locator('[aria-label="Choose Friday\\, September 30th\\, 2022"]').click();

    await page.locator('[data-test-id=currencySelect]').click();
    await page.locator(`[data-test-id=currencySelect] ${currencySingleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=principalAmountInput] >> input').fill('200000');

    page.locator('[data-test-id=paymentFrequencySelect]').click();
    await page.locator(`[data-test-id=paymentFrequencySelect] ${singleSelectFirstElementSelector}`).click();

    page.locator('[data-test-id=senioritySelect]').click();
    await page.locator(`[data-test-id=senioritySelect] ${singleSelectFirstElementSelector}`).click();

    await page.locator('[data-test-id=priceLoanGuaranteeButton]').click();

    await page.locator('[data-test-id=submitPricingButton]').click();

    await expect(page).toHaveURL(/analyses\/[0-9]+\?reportType=guarantee/);
    await page.locator('[data-test-id=successToast]').click();

    await page.locator('[data-test-id=reportViewActionMenu]').click();
    await page.locator('text=Delete').click();

    await page.locator('[data-test-id=confirmDeleteReportButton]').click();

    await page.goto('/price-and-benchmark');
  });
});
