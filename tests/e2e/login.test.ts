import { test, expect } from '@playwright/test';

import { loginAsAdmin } from './utils';

test.beforeEach(async ({ page }) => {
  await loginAsAdmin(page);
});

test.describe('Login', () => {
  test('Login', async ({ page }) => {
    await expect(page).toHaveURL('/dashboard');
  });

  test('Logout', async ({ page }) => {
    await page.locator('[data-test-id=userMenuButton]').click();
    await page.locator('text=Logout').click();
    await expect(page).toHaveURL('/login');
  });
});
