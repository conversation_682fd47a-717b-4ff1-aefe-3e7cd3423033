import _ from 'lodash';

import { assessmentConstants } from '../../../utils/companyUtils/constants';
import calculateAssessment from '../../../utils/companyUtils/assessment';

const implicitSupportAnswers = {
  question1: true,
  question2: false,
  question3: true,
  question4: false,
  question5: true,
  question6: false,
  question7: true,
  question8: false,
  question9: true,
  question10: false,
};

describe('Assessment', () => {
  test('It should test calculateAssessment with false firstQuestion', () => {
    const implicitSupportAnswers = { question1: false };
    expect(calculateAssessment(implicitSupportAnswers)).toBe(assessmentConstants.peripheral);
  });

  test('It should test calculateAssessment with true first two questions', () => {
    const implicitSupportAnswers = { question1: true, question2: true };
    expect(calculateAssessment(implicitSupportAnswers)).toBe(assessmentConstants.central);
  });

  test('It should test calculateAssessment with true first two questions', () => {
    const implicitSupportAnswersClone = _.cloneDeep(implicitSupportAnswers);
    implicitSupportAnswersClone.question3 = false;
    implicitSupportAnswersClone.question5 = false;
    implicitSupportAnswersClone.question7 = false;
    implicitSupportAnswersClone.question9 = false;
    expect(calculateAssessment(implicitSupportAnswersClone)).toBe(assessmentConstants.peripheral);
  });

  test('It should test calculateAssessment with true first two questions', () => {
    const implicitSupportAnswersClone = _.cloneDeep(implicitSupportAnswers);
    implicitSupportAnswersClone.question3 = false;
    expect(calculateAssessment(implicitSupportAnswersClone)).toBe(assessmentConstants.integral);
  });

  test('It should test calculateAssessment with true first two questions', () => {
    const implicitSupportAnswersClone = _.cloneDeep(implicitSupportAnswers);
    implicitSupportAnswersClone.question3 = false;
    implicitSupportAnswersClone.question5 = false;
    expect(calculateAssessment(implicitSupportAnswersClone)).toBe(assessmentConstants.weaklyIntegral);
  });

  test('It should test calculateAssessment with true first two questions', () => {
    const implicitSupportAnswersClone = _.cloneDeep(implicitSupportAnswers);
    implicitSupportAnswersClone.question3 = false;
    implicitSupportAnswersClone.question5 = false;
    implicitSupportAnswersClone.question7 = false;
    expect(calculateAssessment(implicitSupportAnswersClone)).toBe(assessmentConstants.peripheral);
  });

  test('It should test calculateAssessment', () => {
    const implicitSupportAnswersClone = _.cloneDeep(implicitSupportAnswers);
    expect(calculateAssessment(implicitSupportAnswersClone)).toBe(assessmentConstants.stronglyIntegral);
  });

  test('It should test calculateAssessment', () => {
    const implicitSupportAnswersClone = _.cloneDeep(implicitSupportAnswers);
    implicitSupportAnswersClone.question4 = true;
    implicitSupportAnswersClone.question6 = true;
    expect(calculateAssessment(implicitSupportAnswersClone)).toBe(assessmentConstants.nearlyCentral);
  });

  test('It should test calculateAssessment', () => {
    const implicitSupportAnswersClone = _.cloneDeep(implicitSupportAnswers);
    implicitSupportAnswersClone.question4 = true;
    implicitSupportAnswersClone.question6 = true;
    implicitSupportAnswersClone.question8 = true;
    expect(calculateAssessment(implicitSupportAnswersClone)).toBe(assessmentConstants.central);
  });

  test('It should test calculateAssessment', () => {
    const implicitSupportAnswersClone = _.cloneDeep(implicitSupportAnswers);
    implicitSupportAnswersClone.question4 = true;
    implicitSupportAnswersClone.question6 = true;
    implicitSupportAnswersClone.question8 = true;
    implicitSupportAnswersClone.question10 = true;
    expect(calculateAssessment(implicitSupportAnswersClone)).toBe(assessmentConstants.central);
  });
});
