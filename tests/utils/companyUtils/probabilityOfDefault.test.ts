import { CreditRatingValueEnum } from '../../../enums/creditRating';
import estimateProbabilityOfDefaultAdj from '../../../utils/companyUtils/estimateProbabilityOfDefaultAdj';

describe('Estimate probability of default', () => {
  test('It should test estimate probability of default', () => {
    expect(estimateProbabilityOfDefaultAdj(3, CreditRatingValueEnum['AA+/Aa1'], CreditRatingValueEnum['AA+/Aa1'])).toBe(
      3,
    );

    expect(estimateProbabilityOfDefaultAdj(3, CreditRatingValueEnum['AA+/Aa1'], CreditRatingValueEnum['B-/B3'])).toBe(
      100,
    );

    expect(estimateProbabilityOfDefaultAdj(12, CreditRatingValueEnum['CC/Ca'], CreditRatingValueEnum['B-/B3'])).toBe(
      3.090867,
    );

    expect(estimateProbabilityOfDefaultAdj(12, CreditRatingValueEnum['B+/B1'], CreditRatingValueEnum['AAA/Aaa'])).toBe(
      0.093168,
    );

    expect(estimateProbabilityOfDefaultAdj(12, CreditRatingValueEnum['CC/Ca'], CreditRatingValueEnum['AAA/Aaa'])).toBe(
      0.013873,
    );

    expect(
      estimateProbabilityOfDefaultAdj(3, CreditRatingValueEnum['CCC/Caa2'], CreditRatingValueEnum['AAA/Aaa']),
    ).toBe(0.006993);
  });
});
