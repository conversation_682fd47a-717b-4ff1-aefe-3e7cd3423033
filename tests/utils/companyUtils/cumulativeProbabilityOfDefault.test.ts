import getCumulativeProbabilityOfDefault from '../../../utils/companyUtils/cumulativeProbabilityOfDefault';

describe('Estimate probability of default', () => {
  test('It should test estimate probability of default', () => {
    const principal = { creditRating: { probabilityOfDefault: 3 } };
    const tenor = 3;
    const pricingApproach = 'stand-alone';
    expect(getCumulativeProbabilityOfDefault(principal, tenor, pricingApproach)).toEqual({
      probabilityOfDefaultPercentage: 8.7327,
    });
  });

  test('It should test estimate probability of default', () => {
    const principal = { creditRating: { probabilityOfDefault: 2.4 } };
    const tenor = 6.75;
    const pricingApproach = 'stand-alone';

    const { probabilityOfDefaultPercentage } = getCumulativeProbabilityOfDefault(principal, tenor, pricingApproach);
    expect(probabilityOfDefaultPercentage).toBeCloseTo(15.1237);
  });

  test('It should test estimate probability of default', () => {
    const principal = { creditRating: { probabilityOfDefaultAdj: 4.7 } };
    const tenor = 12;
    const pricingApproach = 'implicit non-standard';

    expect(getCumulativeProbabilityOfDefault(principal, tenor, pricingApproach)).toEqual({
      probabilityOfDefaultPercentage: 43.8804,
    });
  });

  test('It should test estimate probability of default', () => {
    const principal = { creditRating: { probabilityOfDefaultAdj: 3.6 } };
    const tenor = 4.75;
    const pricingApproach = 'implicit non-standard';

    expect(getCumulativeProbabilityOfDefault(principal, tenor, pricingApproach)).toEqual({
      probabilityOfDefaultPercentage: 15.9832,
    });
  });
});
