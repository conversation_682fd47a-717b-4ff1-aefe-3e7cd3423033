import {
  getReportUnit,
  getTypes,
  getCalculationLogRedisKey,
  checkCurrencyProviderDataAvailability,
  issueDateUpdateCheck,
  runReportUpdateGuards,
} from '../../../utils/reportUtils';
import reportsEnum from '../../../enums/reports';
import { BadRequestError, NotFoundError, UpdateFinalizedError, MoveUneditableError } from '../../../utils/ErrorHandler';

describe('Report Utils', () => {
  describe('Test getReportUnit function', () => {
    test('It should return `%` for fixed report type', () => {
      const reportUnit = getReportUnit({ type: 'fixed' });
      expect(reportUnit).toBe('%');
    });

    test('It should return ` basis points` for float report type', () => {
      const reportUnit = getReportUnit({ type: 'float' });
      expect(reportUnit).toBe(' basis points');
    });

    test('It should return `%` for no arguments provided', () => {
      const reportUnit = getReportUnit();
      expect(reportUnit).toBe('%');
    });
  });

  describe('Test getTypes function', () => {
    test('It should return fixed and Bullet for guarantee', () => {
      const guarantee = { type: 'Bullet', rateType: { type: 'fixed' } };
      const [rateType, type] = getTypes(guarantee);
      expect(rateType).toBe('fixed');
      expect(type).toBe('Bullet');
    });

    test("It should return loan's rateType and type 1", () => {
      const loan = { type: 'Bullet', rateType: { type: 'fixed' } };
      const [rateType, type] = getTypes(loan);
      expect(rateType).toBe(loan.rateType.type);
      expect(type).toBe(loan.type);
    });

    test("It should return loan's rateType and type 2", () => {
      const loan = { type: 'Ballon', rateType: { type: 'fixed' } };
      const [rateType, type] = getTypes(loan);
      expect(rateType).toBe(loan.rateType.type);
      expect(type).toBe(loan.type);
    });

    test("It should return loan's rateType and type 3", () => {
      const loan = {
        type: 'Bullet',
        rateType: { type: 'float', referenceRate: 'CDI', referenceRateMaturity: '1 month' },
      };
      const [rateType, type] = getTypes(loan);
      expect(rateType).toBe(loan.rateType.type);
      expect(type).toBe(loan.type);
    });

    test("It should return loan's rateType and type 4", () => {
      const loan = {
        type: 'Balloon',
        rateType: { type: 'float', referenceRate: 'CDI', referenceRateMaturity: '1 month' },
      };
      const [rateType, type] = getTypes(loan);
      expect(rateType).toBe(loan.rateType.type);
      expect(type).toBe(loan.type);
    });
  });

  describe('Test getCalculationLogRedisKey function', () => {
    test('It should return key based on reportType and reportId', () => {
      const guaranteeId = 12;
      expect(getCalculationLogRedisKey(reportsEnum.REPORT_TYPES.GUARANTEE, guaranteeId)).toBe(
        `${reportsEnum.REPORT_TYPES.GUARANTEE}:${guaranteeId}:calculationLog`,
      );
    });

    test('It should return key based on reportType and reportId', () => {
      const loanId = 12;
      expect(getCalculationLogRedisKey(reportsEnum.REPORT_TYPES.LOAN, loanId)).toBe(
        `${reportsEnum.REPORT_TYPES.LOAN}:${loanId}:calculationLog`,
      );
    });
  });

  describe('Test checkCurrencyProviderDataAvailability function', () => {
    test('It should throw for issue date on or after 2022-03-15 and RUB currency', () => {
      expect(() =>
        checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-03-15'), currency: 'RUB' }),
      ).toThrow(BadRequestError);
      expect(() =>
        checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-03-16'), currency: 'RUB' }),
      ).toThrow(BadRequestError);
    });

    test('It should not throw for issue date on or before 2022-03-14 and RUB currency', () => {
      checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-03-14'), currency: 'RUB' });
      checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-03-13'), currency: 'RUB' });
    });

    test('It should throw for issue date on or before 2022-09-21 and BRL or COP currency', () => {
      expect(() =>
        checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-09-21'), currency: 'BRL' }),
      ).toThrow(BadRequestError);
      expect(() =>
        checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-09-20'), currency: 'BRL' }),
      ).toThrow(BadRequestError);

      expect(() =>
        checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-09-21'), currency: 'COP' }),
      ).toThrow(BadRequestError);
      expect(() =>
        checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-09-20'), currency: 'COP' }),
      ).toThrow(BadRequestError);
    });

    test('It should not throw for issue date on or after 2022-09-22 and BRL or COP currency', () => {
      checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-09-22'), currency: 'BRL' });
      checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-09-23'), currency: 'BRL' });

      checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-09-22'), currency: 'COP' });
      checkCurrencyProviderDataAvailability({ issueDate: new Date('2022-09-23'), currency: 'COP' });
    });
  });

  describe('Test issueDateUpdateCheck function', () => {
    test('It should not throw if clientFeature is not enabled', () => {
      issueDateUpdateCheck(
        { originalIssueDate: new Date(2022, 3, 1) },
        { issueDate: new Date(2024, 9, 1) },
        { isEnabled: false },
      );
    });

    test('It should not throw for two months or less in the future for updated issueDate', () => {
      issueDateUpdateCheck(
        { originalIssueDate: new Date(2022, 6, 1) },
        { issueDate: new Date(2022, 7, 30) },
        { isEnabled: true },
      );
    });

    test('It should not throw for two months or less in the future for updated issueDate', () => {
      issueDateUpdateCheck(
        { originalIssueDate: new Date(2022, 6, 1) },
        { issueDate: new Date(2022, 8, 1) },
        { isEnabled: true },
      );
    });

    test('It should throw for more then two months in the future for updated issueDate', () => {
      expect(() =>
        issueDateUpdateCheck(
          { originalIssueDate: new Date(2022, 6, 1) },
          { issueDate: new Date(2022, 8, 2) },
          { isEnabled: true },
        ),
      ).toThrow(BadRequestError);
    });

    test('It should throw for more then two months in the future for updated issueDate', () => {
      expect(() =>
        issueDateUpdateCheck(
          { originalIssueDate: new Date(2022, 6, 1) },
          { issueDate: new Date(2022, 9, 1) },
          { isEnabled: true },
        ),
      ).toThrow(BadRequestError);
    });
  });

  describe('Test issueDateUpdateCheck function', () => {
    test('It should throw if no report is provided', () => {
      expect(() => runReportUpdateGuards(null, reportsEnum.REPORT_TYPES.LOAN)).toThrow(NotFoundError);
    });

    test('It should throw if report is finalized', () => {
      expect(() => runReportUpdateGuards({ status: 'Final' }, reportsEnum.REPORT_TYPES.LOAN)).toThrow(
        UpdateFinalizedError,
      );
    });

    test('It should throw if report is uneditable', () => {
      expect(() => runReportUpdateGuards({ editable: false }, reportsEnum.REPORT_TYPES.LOAN)).toThrow(
        MoveUneditableError,
      );
    });

    test('It should not throw for a editable, not finalized report', () => {
      runReportUpdateGuards({ editable: true, status: 'Draft' }, reportsEnum.REPORT_TYPES.LOAN);
    });
  });
});
