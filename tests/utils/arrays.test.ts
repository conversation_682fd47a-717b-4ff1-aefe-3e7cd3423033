import { getLabelFromRangeArray } from '../../utils/arrays';
import { assessmentConstants, pointsLabelMapper } from '../../utils/companyUtils/constants';

describe('Arrays Utils', () => {
  test('It should get points label from points range', () => {
    expect(getLabelFromRangeArray(1, pointsLabelMapper)).toBe(assessmentConstants.peripheral);
    expect(getLabelFromRangeArray(1.25, pointsLabelMapper)).toBe(assessmentConstants.peripheral);
    expect(getLabelFromRangeArray(2.5, pointsLabelMapper)).toBe(assessmentConstants.weaklyIntegral);
    expect(getLabelFromRangeArray(3.5, pointsLabelMapper)).toBe(assessmentConstants.integral);
    expect(getLabelFromRangeArray(4, pointsLabelMapper)).toBe(assessmentConstants.integral);
    expect(getLabelFromRangeArray(4.01, pointsLabelMapper)).toBe(assessmentConstants.stronglyIntegral);
  });
});
