import { azureSetup } from '../../setup';
import * as guaranteeUtils from '../../../utils/guaranteeUtils';
import { seniorityEnum } from '../../../utils/providerDataUtils/constants';

beforeAll(() => {
  azureSetup.basicAzureSetup();
});

describe('Guarantee Utils', () => {
  describe('Tests for calculateSecurityApproachBounds', () => {
    test('It should calculate Security Approach Bounds (POSITIVE-POSITIVE)', () => {
      const result = guaranteeUtils.calculateSecurityApproachBounds({
        seniorSecuredMidPoint: 2.5,
        unsubordinatedMidPoint: 3.75,
        subordinatedMidPoint: 3.75,
      });
      expect(result).toEqual([1.25, 1.25, 1.25]);
    });

    test('It should calculate Security Approach Bounds (POSITIVE2-POSITIVE2)', () => {
      try {
        guaranteeUtils.calculateSecurityApproachBounds({
          seniorSecuredMidPoint: 3.75,
          unsubordinatedMidPoint: 2.5,
          subordinatedMidPoint: 2.5,
        });
      } catch (err: any) {
        expect(err?.message).toBe('Bounds are not correct.');
      }
    });

    test('It should calculate Security Approach Bounds (NEGATIVE-POSITIVE)', () => {
      const result = guaranteeUtils.calculateSecurityApproachBounds({
        seniorSecuredMidPoint: -2.5,
        unsubordinatedMidPoint: 3.75,
        subordinatedMidPoint: 3.75,
      });
      expect(result).toEqual([6.25, 6.25, 6.25]);
    });

    test('It should calculate Security Approach Bounds (NEGATIVE-NEGATIVE)', () => {
      const result = guaranteeUtils.calculateSecurityApproachBounds({
        seniorSecuredMidPoint: -3.75,
        unsubordinatedMidPoint: -2.5,
        subordinatedMidPoint: -2.5,
      });
      expect(result).toEqual([1.25, 1.25, 1.25]);
    });

    test('It should calculate Security Approach Bounds (POSITIVE-NEGATIVE)', () => {
      try {
        guaranteeUtils.calculateSecurityApproachBounds({
          seniorSecuredMidPoint: 2.5,
          unsubordinatedMidPoint: -3.75,
          subordinatedMidPoint: -3.75,
        });
      } catch (err: any) {
        expect(err?.message).toBe('Bounds are not correct.');
      }
    });
  });

  describe('Tests for getLossGivenDefault', () => {
    test('It should return 0.15 for Senior Secured', () => {
      const lossGivenDefault = guaranteeUtils.getLossGivenDefault(seniorityEnum.seniorSecured, 'ANY_INDUSTRY');
      expect(lossGivenDefault).toBe(0.15);
    });

    test('It should return 0.45 for Unsubordinated and Financials as industry', () => {
      const lossGivenDefault = guaranteeUtils.getLossGivenDefault(seniorityEnum.unsubordinated, 'Financials');
      expect(lossGivenDefault).toBe(0.45);
    });

    test('It should return 0.4 for Unsubordinated and not Financials as industry', () => {
      const lossGivenDefault = guaranteeUtils.getLossGivenDefault(seniorityEnum.unsubordinated, 'ANY_INDUSTRY');
      expect(lossGivenDefault).toBe(0.4);
    });

    test('It should return 0.75 for Subordinated', () => {
      const lossGivenDefault = guaranteeUtils.getLossGivenDefault(seniorityEnum.subordinated, 'ANY_INDUSTRY');
      expect(lossGivenDefault).toBe(0.75);
    });
  });
});
