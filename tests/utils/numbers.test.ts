import _ from 'lodash';
import {
  calculateMaximumQuartile,
  calculateUpperQuartile,
  calculateMedianQuartile,
  calculateLowerQuartile,
  calculateMinimumQuartile,
} from '../../utils/numbers';

const input = [
  384, 247, 278, 268, 331, 189, 79, 318, 228, 130, 192, 94, 400, 158, 264, 355, 251, 303, 93, 417, 177, 94, 302, 153,
  206, 426, 426, 406, 348, 127,
];

describe('Number Utils', () => {
  test('It should check quartile results', () => {
    const sortedInput = _.sortBy(input);
    expect(calculateMaximumQuartile(sortedInput)).toBe(426);
    expect(calculateUpperQuartile(sortedInput)).toBe(343.75);
    expect(calculateMedianQuartile(sortedInput)).toBe(257.5);
    expect(calculateLowerQuartile(sortedInput)).toBe(162.75);
    expect(calculateMinimumQuartile(sortedInput)).toBe(79);
  });
});
