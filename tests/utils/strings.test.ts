import { groupByThreeCharacters, capitalize } from '../../utils/strings';

describe('Arrays Utils', () => {
  test('It should test groupByThreeCharacters', () => {
    expect(groupByThreeCharacters(1)).toBe('1');
    expect(groupByThreeCharacters('1')).toBe('1');

    expect(groupByThreeCharacters(12)).toBe('12');
    expect(groupByThreeCharacters('12')).toBe('12');

    expect(groupByThreeCharacters(123)).toBe('123');
    expect(groupByThreeCharacters('123')).toBe('123');

    expect(groupByThreeCharacters(1234)).toBe('1 234');
    expect(groupByThreeCharacters('1234')).toBe('1 234');

    expect(groupByThreeCharacters(123456)).toBe('123 456');
    expect(groupByThreeCharacters('123456')).toBe('123 456');

    expect(groupByThreeCharacters(1234567)).toBe('1 234 567');
    expect(groupByThreeCharacters('1234567')).toBe('1 234 567');

    expect(groupByThreeCharacters({ key: 'value' })).toBe('[object Object]');

    expect(groupByThreeCharacters(null)).toBe('');
    expect(groupByThreeCharacters(undefined)).toBe('');

    expect(groupByThreeCharacters('')).toBe('');
    expect(groupByThreeCharacters(0)).toBe('0');
  });

  test('It should test capitalize', () => {
    expect(capitalize('a')).toBe('A');
    expect(capitalize('aaaa')).toBe('Aaaa');
    expect(capitalize('first second')).toBe('First second');
    expect(capitalize('First second')).toBe('First second');

    expect(capitalize(null)).toBe('');
    expect(capitalize(undefined)).toBe('');
  });
});
