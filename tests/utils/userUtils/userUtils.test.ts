import { getUsername, checkEmailDomainMatchesClientDomain } from '../../../utils/userUtils/userUtils';
import { userRepository } from '../../../repositories';
import { BadRequestError, ConflictError } from '../../../utils/ErrorHandler';

jest.mock('../../../repositories/userRepository');

describe('User utils', () => {
  describe('Tests for getUsername', () => {
    test('It should return the username passed in', async () => {
      const username = await getUsername('username', 'usernameFromEmail');
      expect(username).toBe('username');
    });

    test('It should return the usernameFromEmail when it is not in the database', async () => {
      const mockGetUser = jest.spyOn(userRepository, 'getUser');
      mockGetUser.mockImplementationOnce(() => Promise.resolve(null));

      const username = await getUsername(undefined, 'usernameFromEmail');
      expect(username).toBe('usernameFromEmail');
    });

    test('It should return the usernameFromEmail with 1 appended because usernameFromEmail exists in the database', async () => {
      const mockGetUser = jest.spyOn(userRepository, 'getUser');
      mockGetUser.mockImplementationOnce(() => Promise.resolve({ username: 'usernameFromEmail' }));
      mockGetUser.mockImplementationOnce(() => Promise.resolve(null));

      const username = await getUsername(undefined, 'usernameFromEmail');
      expect(username).toBe('usernameFromEmail1');
    });

    test('It should return the usernameFromEmail with 2 appended because usernameFromEmail and usernameFromEmail1 exist in the database', async () => {
      const mockGetUser = jest.spyOn(userRepository, 'getUser');
      mockGetUser.mockImplementationOnce(() => Promise.resolve({ username: 'usernameFromEmail' }));
      mockGetUser.mockImplementationOnce(() => Promise.resolve({ username: 'usernameFromEmail1' }));
      mockGetUser.mockImplementationOnce(() => Promise.resolve(null));

      const username = await getUsername(undefined, 'usernameFromEmail');
      expect(username).toBe('usernameFromEmail2');
    });
  });

  describe('Tests for checkEmailDomainMatchesFirstClientUser', () => {
    test('It should throw BadRequestError if emailDomain are null', async () => {
      const domain = 'tpaccurate.com';
      const emailDomains = null;
      const confirm = false;
      const isSuperAdmin = false;

      await expect(checkEmailDomainMatchesClientDomain(domain, emailDomains, isSuperAdmin, confirm)).rejects.toThrow(
        BadRequestError,
      );
    });

    test("It should return undefined for admin if domain is in client's email domains", async () => {
      const domain = 'tpaccurate.com';
      const emailDomains = ['tpaccurate.com'];
      const confirm = false;
      const isSuperAdmin = false;

      const result = await checkEmailDomainMatchesClientDomain(domain, emailDomains, isSuperAdmin, confirm);
      expect(result).toBe(undefined);
    });

    test("It should throw BadRequestError for admin if domain is in client's email domains", async () => {
      const domain = 'tpaccurate.com';
      const emailDomains = ['arsfutura.com'];
      const confirm = false;
      const isSuperAdmin = false;

      await expect(checkEmailDomainMatchesClientDomain(domain, emailDomains, isSuperAdmin, confirm)).rejects.toThrow(
        BadRequestError,
      );
    });

    test("It should return undefined for superadmin if domain is in client's email domains and confirm can be either true or false", async () => {
      const domain = 'tpaccurate.com';
      const emailDomains = ['tpaccurate.com'];
      const confirm = false;
      const isSuperAdmin = true;

      const result = await checkEmailDomainMatchesClientDomain(domain, emailDomains, isSuperAdmin, confirm);
      expect(result).toBe(undefined);
    });

    test("It should return undefined for superadmin if domain is not in user's email and confirm is true", async () => {
      const domain = 'tpaccurate.com';
      const emailDomains: Array<string> = [];
      const confirm = true;
      const isSuperAdmin = true;

      const result = await checkEmailDomainMatchesClientDomain(domain, emailDomains, isSuperAdmin, confirm);
      expect(result).toBe(undefined);
    });

    test("It should throw ConflictError for superadmin if domain is not in user's email and confirm is false", async () => {
      const domain = 'tpaccurate.com';
      const emailDomains: Array<string> = ['arsfutura.com'];
      const confirm = false;
      const isSuperAdmin = true;

      await expect(checkEmailDomainMatchesClientDomain(domain, emailDomains, isSuperAdmin, confirm)).rejects.toThrow(
        ConflictError,
      );
    });
  });
});
