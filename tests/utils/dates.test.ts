import * as dateUtils from '../../utils/dates';

describe('Date Utils', () => {
  test('It should test format date', () => {
    expect(dateUtils.formatDate('2019-12-31')).toBe('31.12.2019');
    expect(dateUtils.formatDate(new Date(2019, 11, 31))).toBe('31.12.2019');
  });

  test('It should test parse date', () => {
    expect(dateUtils.parseDate('20191231')).toBe('2019-12-31');
    expect(dateUtils.parseDate('20230301')).toBe('2023-03-01');
  });

  test('It should test calculate tenor', () => {
    expect(dateUtils.calculateTenor(new Date(2019, 11, 31), new Date(2024, 11, 31))).toBe(5);
    expect(dateUtils.calculateTenor(new Date(2019, 5, 31), new Date(2024, 11, 31))).toBeCloseTo(5.4973);
    expect(dateUtils.calculateTenor(new Date(2019, 0, 1), new Date(2024, 5, 1))).toBeCloseTo(5.4166);
    expect(dateUtils.calculateTenor(new Date(2019, 0, 1), new Date(2021, 0, 1))).toBeCloseTo(2);
    expect(dateUtils.calculateTenor(new Date(2019, 0, 1), new Date(2021, 5, 15))).toBeCloseTo(2.4543);
    expect(dateUtils.calculateTenor(new Date(2019, 7, 12), new Date(2028, 3, 19))).toBeCloseTo(8.6854);
  });
});
