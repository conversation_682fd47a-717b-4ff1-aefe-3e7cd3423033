import { calculateLegInterestRates } from '../../../utils/b2bLoanUtils';

import { fixedB2BLoan } from './__mocks__/fixedB2BLoan';
import { floatB2BLoan } from './__mocks__/floatB2BLoan';

describe('Number Utils', () => {
  test('It should check quartile results', () => {
    expect(calculateLegInterestRates(fixedB2BLoan, 5.5)).toEqual([1.814751719006269, 4.614571428571429, 5.5]);
  });

  test('It should check quartile results', () => {
    expect(calculateLegInterestRates(floatB2BLoan, 300)).toEqual([62.91802904348398, 192.89999999999998, 300]);
  });
});
