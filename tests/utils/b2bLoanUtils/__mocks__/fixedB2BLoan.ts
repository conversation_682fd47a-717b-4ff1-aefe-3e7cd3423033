import { B2BLoanType } from '../../../../types';
import * as sharedB2BLoanAttributes from './sharedB2BLoanAttributes';

export const fixedB2BLoan: B2BLoanType = {
  ...sharedB2BLoanAttributes.irrelevantLoanAttributes,
  amount: 350000,
  rateType: { type: 'fixed' },
  borrowers: sharedB2BLoanAttributes.borrowers,
  riskTakerId: 142,
  isThirdParty: false,
  capm: {
    requiredRateOfReturn: 0.06751099999999999,
    riskFreeRate: 0.0245,
    riskFreeRateSource: 'Damodaran',
    beta: 0.729,
    betaSource: 'Damodaran',
    betaIndustrySector: 'Power',
    betaRegion: 'global',
    betaType: 'Beta',
    equityRiskPremium: 0.059,
    equityRiskPremiumSource: 'Damodaran',
    equityRiskPremiumCountry: 'Australia',
  },
  expectedLoss: {
    expectedLoss: 0.014785695600000003,
    probabilityOfDefault: 0.09857130400000003,
    probabilityOfDefaultSource: 'modeFinance',
    probabilityOfDefaultType: 'Cumulative',
    lossGivenDefault: 0.15,
    lossGivenDefaultSource: 'Basel III',
  },
  standardRemuneration: {
    32: {
      id: 32,
      name: 'Apple Tree AS',
      type: 'Operating cost & Markup',
      operationalCost: 3000,
      markup: 3.3,
    },
    142: {
      id: 142,
      name: 'Apricot Tree Inc.',
      type: '%',
      margin: 2.7,
    },
  },
};
