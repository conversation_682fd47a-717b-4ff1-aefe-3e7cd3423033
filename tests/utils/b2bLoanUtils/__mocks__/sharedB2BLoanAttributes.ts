import { EmbeddedCompanyType, B2BLoanFileType, B2BLoanLegType } from '../../../../types';

export const lenders: Array<EmbeddedCompanyType> = [
  {
    id: 118,
    parentCompanyId: 167,
    name: 'AAAA',
    industry: 'Consumer Discretionary',
    country: 'Thailand',
    creditRating: {
      rating: 'AAA/Aaa',
      ratingAdj: 'AAA/Aaa',
      probabilityOfDefault: 2.77,
      probabilityOfDefaultAdj: 2.77,
    },
  },
  {
    id: 142,
    parentCompanyId: 167,
    name: 'Apricot Tree Inc.',
    industry: 'Technology',
    country: 'India',
    creditRating: {
      rating: 'B/B2',
      ratingAdj: null,
      probabilityOfDefault: 3,
      probabilityOfDefaultAdj: null,
    },
  },
  {
    id: 32,
    parentCompanyId: 32,
    name: 'Apple Tree AS',
    industry: 'Consumer Discretionary',
    country: 'United States of America',
    creditRating: {
      rating: 'A+/A1',
      ratingAdj: null,
      probabilityOfDefault: 5.3,
      probabilityOfDefaultAdj: 2.65,
    },
  },
];

export const borrowers: Array<EmbeddedCompanyType> = [
  {
    id: 142,
    parentCompanyId: 167,
    name: 'Apricot Tree Inc.',
    industry: 'Technology',
    country: 'India',
    creditRating: {
      rating: 'B/B2',
      ratingAdj: null,
      probabilityOfDefault: 3,
      probabilityOfDefaultAdj: null,
    },
  },
  {
    id: 32,
    parentCompanyId: 32,
    name: 'Apple Tree AS',
    industry: 'Consumer Discretionary',
    country: 'United States of America',
    creditRating: {
      rating: 'A+/A1',
      ratingAdj: null,
      probabilityOfDefault: 5.3,
      probabilityOfDefaultAdj: 2.65,
    },
  },
  {
    id: 156,
    parentCompanyId: 32,
    name: 'AustraliaCompany',
    industry: 'Consumer Discretionary',
    country: 'Australia',
    creditRating: {
      rating: 'AA-/Aa3',
      ratingAdj: 'AA-/Aa3',
      probabilityOfDefault: 3.4,
      probabilityOfDefaultAdj: 3.4,
    },
  },
];

export const irrelevantLoanAttributes = {
  id: 8,
  issueDate: new Date(),
  maturityDate: new Date(),
  currency: 'USD',
  paymentFrequency: 'Monthly',
  seniority: 'Subordinated',
  report: {},
  createdAt: new Date(),
  updatedAt: new Date(),
  pricingApproach: 'implicit',
  ultimateLender: lenders[0],
  ultimateBorrower: borrowers[borrowers.length - 1],
  lenders,
  capmRecommendation: {},
  capmOverride: {},
  expectedLossRecommendation: {},
  expectedLossOverride: {},
  overrideToggles: {
    riskFreeRate: false,
    beta: false,
    equityRiskPremium: false,
    probabilityOfDefault: false,
    lossGivenDefault: false,
  },
  clientId: 1,
  editable: true,
  status: 'Draft',
  note: '',
  files: [] as B2BLoanFileType[],
  legs: [] as B2BLoanLegType[],
  isPortfolio: false,
  createdBy: 'admin',
  updatedBy: 'admin',
  finalizedBy: 'admin',
  calculationLog: {},
  type: 'Bullet',
  movedToAnalysesDate: new Date(),
  totalInterest: 0,
  originalIssueDate: new Date(),
  deletedAt: new Date(),
} as const;
