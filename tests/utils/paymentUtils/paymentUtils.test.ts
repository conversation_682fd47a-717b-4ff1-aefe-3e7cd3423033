import { WHT_APPROACHES } from '../../../enums/wht';
import { calculateWhtInterestRateBasedOnApproach } from '../../../utils/payments/paymentUtils';

const interestPerYear = {
  '2022': 12.5,
  '2023': 50,
  '2024': 50,
  '2025': 50,
  '2026': 50,
  '2027': 50,
  '2028': 50,
  '2029': 37.6359,
};

describe('Payment Utils', () => {
  describe('Test calculating wht interest rates based on approach', () => {
    test('It should test calculating whtInterestPerYear for BORROWER_PAYS approach', () => {
      const [whtInterestPerYear, newInterestPerYearDependentOnWht] = calculateWhtInterestRateBasedOnApproach(
        WHT_APPROACHES.BORROWER_PAYS,
        10,
        interestPerYear,
      );

      expect(whtInterestPerYear['2022']).toBe(1.25);
      expect(whtInterestPerYear['2023']).toBe(5);
      expect(whtInterestPerYear['2024']).toBe(5);
      expect(whtInterestPerYear['2025']).toBe(5);
      expect(whtInterestPerYear['2026']).toBe(5);
      expect(whtInterestPerYear['2027']).toBe(5);
      expect(whtInterestPerYear['2028']).toBe(5);
      expect(whtInterestPerYear['2029']).toBeCloseTo(3.7635);

      expect(newInterestPerYearDependentOnWht['2022']).toBe(11.25);
      expect(newInterestPerYearDependentOnWht['2023']).toBe(45);
      expect(newInterestPerYearDependentOnWht['2024']).toBe(45);
      expect(newInterestPerYearDependentOnWht['2025']).toBe(45);
      expect(newInterestPerYearDependentOnWht['2026']).toBe(45);
      expect(newInterestPerYearDependentOnWht['2027']).toBe(45);
      expect(newInterestPerYearDependentOnWht['2028']).toBe(45);
      expect(newInterestPerYearDependentOnWht['2029']).toBeCloseTo(33.872);
    });

    test('It should test calculating whtInterestPerYear for GROSS_UP approach', () => {
      const [whtInterestPerYear, newInterestPerYearDependentOnWht] = calculateWhtInterestRateBasedOnApproach(
        WHT_APPROACHES.GROSS_UP,
        10,
        interestPerYear,
      );

      expect(whtInterestPerYear['2022']).toBeCloseTo(1.3889);
      expect(whtInterestPerYear['2023']).toBeCloseTo(5.5556);
      expect(whtInterestPerYear['2024']).toBeCloseTo(5.5556);
      expect(whtInterestPerYear['2025']).toBeCloseTo(5.5556);
      expect(whtInterestPerYear['2026']).toBeCloseTo(5.5556);
      expect(whtInterestPerYear['2027']).toBeCloseTo(5.5556);
      expect(whtInterestPerYear['2028']).toBeCloseTo(5.5556);
      expect(whtInterestPerYear['2029']).toBeCloseTo(4.1818);

      expect(newInterestPerYearDependentOnWht['2022']).toBeCloseTo(12.5);
      expect(newInterestPerYearDependentOnWht['2023']).toBeCloseTo(50);
      expect(newInterestPerYearDependentOnWht['2024']).toBeCloseTo(50);
      expect(newInterestPerYearDependentOnWht['2025']).toBeCloseTo(50);
      expect(newInterestPerYearDependentOnWht['2026']).toBeCloseTo(50);
      expect(newInterestPerYearDependentOnWht['2027']).toBeCloseTo(50);
      expect(newInterestPerYearDependentOnWht['2028']).toBeCloseTo(50);
      expect(newInterestPerYearDependentOnWht['2029']).toBeCloseTo(37.635);
    });
  });
});
