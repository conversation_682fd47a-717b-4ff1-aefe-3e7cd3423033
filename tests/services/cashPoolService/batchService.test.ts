import { createBatchParticipantPayments, updateParticipantAccountsWithSolverData } from '../../../services/cashPoolService/runBatch/batchService';
import { cashPoolParticipantAccountRepository, cashPoolBatchPaymentsRepository, cashPoolLeaderBenefitRepository } from '../../../repositories';
import { transformSolverDataForDb } from '../../../utils/cashPool/cashPoolUtils';

// Mock the repositories
jest.mock('../../../repositories', () => ({
  cashPoolParticipantAccountRepository: {
    getCashPoolParticipantAccounts: jest.fn(),
    updateCashPoolParticipantAccount: jest.fn(),
  },
  cashPoolBatchPaymentsRepository: {
    createCashPoolPayment: jest.fn(),
  },
  cashPoolLeaderBenefitRepository: {
    createLeaderBenefit: jest.fn(),
  },
}));

jest.mock('../../../utils/cashPool/cashPoolUtils', () => ({
  transformSolverDataForDb: jest.fn(),
}));

describe('batchService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createBatchParticipantPayments', () => {
    const mockAccounts = [
      {
        id: 1,
        participant: {
          id: 101,
          company: { id: 1001 }
        }
      },
      {
        id: 2,
        participant: {
          id: 102,
          company: { id: 1002 }
        }
      }
    ];

    beforeEach(() => {
      (cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts as jest.Mock).mockResolvedValue(mockAccounts);
      (cashPoolBatchPaymentsRepository.createCashPoolPayment as jest.Mock).mockResolvedValue({});
    });

    it('should create payments with zero interest for companies with overridden negative debit rates', async () => {
      const participantsPayableOrReceivable = {
        '1001': 100, // Company 1001 has positive interest
        '1002': -50  // Company 1002 has negative interest but will be overridden
      };

      const companiesWithOverriddenNegativeDebitRates = new Set([1002]);

      await createBatchParticipantPayments({
        participantsPayableOrReceivable,
        cashPoolId: 1,
        batchId: 1,
        currency: 'EUR',
        cashPoolLeaderId: 999,
        companiesWithOverriddenNegativeDebitRates,
      });

      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenCalledTimes(2);

      // First call - company 1001 with positive interest (should receive)
      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenNthCalledWith(1, {
        currency: 'EUR',
        cashPoolBatchId: 1,
        cashPoolParticipantAccountId: 1,
        interestPayable: null,
        interestReceivable: 100,
        creditorId: 101,
        debtorId: 999,
      });

      // Second call - company 1002 with overridden negative debit rate (should have zero interest)
      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenNthCalledWith(2, {
        currency: 'EUR',
        cashPoolBatchId: 1,
        cashPoolParticipantAccountId: 2,
        interestPayable: 0,
        interestReceivable: null,
        creditorId: 999,
        debtorId: 102,
      });
    });

    it('should create normal payments when no companies have overridden negative debit rates', async () => {
      const participantsPayableOrReceivable = {
        '1001': 100,  // Company 1001 receives interest
        '1002': -50   // Company 1002 pays interest
      };

      await createBatchParticipantPayments({
        participantsPayableOrReceivable,
        cashPoolId: 1,
        batchId: 1,
        currency: 'EUR',
        cashPoolLeaderId: 999,
      });

      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenCalledTimes(2);

      // First call - company 1001 receives interest
      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenNthCalledWith(1, {
        currency: 'EUR',
        cashPoolBatchId: 1,
        cashPoolParticipantAccountId: 1,
        interestPayable: null,
        interestReceivable: 100,
        creditorId: 101,
        debtorId: 999,
      });

      // Second call - company 1002 pays interest
      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenNthCalledWith(2, {
        currency: 'EUR',
        cashPoolBatchId: 1,
        cashPoolParticipantAccountId: 2,
        interestPayable: 50,
        interestReceivable: null,
        creditorId: 999,
        debtorId: 102,
      });
    });
  });

  describe('updateParticipantAccountsWithSolverData', () => {
    const mockCashPoolDate = {
      companies: [
        {
          accountId: 1,
          companyId: 1001,
          adjDir: 0.05, // Positive debit rate
          netInterestBenefit: 100,
        },
        {
          accountId: 2,
          companyId: 1002,
          adjDir: -0.02, // Negative debit rate - should be overridden
          netInterestBenefit: 50,
        }
      ],
      cashPoolGrossBenefit: 200,
    };

    beforeEach(() => {
      (cashPoolParticipantAccountRepository.updateCashPoolParticipantAccount as jest.Mock).mockResolvedValue({});
      (cashPoolLeaderBenefitRepository.createLeaderBenefit as jest.Mock).mockResolvedValue({});
      (transformSolverDataForDb as jest.Mock).mockImplementation((account) => ({ transformed: account }));
    });

    it('should identify companies with negative debit rates that need to be overridden', async () => {
      const result = updateParticipantAccountsWithSolverData(mockCashPoolDate as any, 1, 1);

      expect(result.companiesWithOverriddenNegativeDebitRates).toEqual(new Set([1002]));
      expect(result.companiesWithOverriddenNegativeDebitRates.has(1001)).toBe(false);
      expect(result.companiesWithOverriddenNegativeDebitRates.has(1002)).toBe(true);
    });

    it('should return empty set when no companies have negative debit rates', async () => {
      const mockCashPoolDateNoNegative = {
        companies: [
          {
            accountId: 1,
            companyId: 1001,
            adjDir: 0.05, // Positive debit rate
            netInterestBenefit: 100,
          },
          {
            accountId: 2,
            companyId: 1002,
            adjDir: 0.03, // Positive debit rate
            netInterestBenefit: 50,
          }
        ],
        cashPoolGrossBenefit: 200,
      };

      const result = updateParticipantAccountsWithSolverData(mockCashPoolDateNoNegative as any, 1, 1);

      expect(result.companiesWithOverriddenNegativeDebitRates).toEqual(new Set());
    });
  });
});
