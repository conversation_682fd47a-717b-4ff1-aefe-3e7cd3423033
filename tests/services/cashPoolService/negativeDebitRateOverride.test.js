const { createBatchParticipantPayments } = require('../../../services/cashPoolService/runBatch/batchService');

// Mock the repositories
const mockGetCashPoolParticipantAccounts = jest.fn();
const mockCreateCashPoolPayment = jest.fn();

jest.mock('../../../repositories/cashPoolParticipantAccountRepository', () => ({
  getCashPoolParticipantAccounts: mockGetCashPoolParticipantAccounts,
}));

jest.mock('../../../repositories/cashPoolBatchPaymentsRepository', () => ({
  createCashPoolPayment: mockCreateCashPoolPayment,
}));

describe('Negative Debit Rate Override', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockCreateCashPoolPayment.mockResolvedValue({});
  });

  test('It should set interest to 0 for companies with overridden negative debit rates', async () => {
    // Mock accounts - one normal, one with overridden negative debit rate
    const mockAccounts = [
      {
        id: 1,
        adjustedDebitInterestRate: 0.05, // Normal positive debit rate
        participant: {
          id: 101,
          company: { id: 1001 }
        }
      },
      {
        id: 2,
        adjustedDebitInterestRate: 0, // Overridden negative debit rate (set to 0)
        participant: {
          id: 102,
          company: { id: 1002 }
        }
      },
      {
        id: 3,
        adjustedDebitInterestRate: 0.03, // Normal positive debit rate
        participant: {
          id: 103,
          company: { id: 1003 }
        }
      }
    ];

    mockGetCashPoolParticipantAccounts.mockResolvedValue(mockAccounts);

    // Solver results: company 1002 has negative interest (would normally pay)
    // but should be overridden to 0 because adjustedDebitInterestRate is 0
    const participantsPayableOrReceivable = {
      '1001': 150,  // Company 1001 receives interest
      '1002': -75,  // Company 1002 has negative interest but should be overridden to 0
      '1003': -25   // Company 1003 has negative interest and should pay (normal case)
    };

    await createBatchParticipantPayments({
      participantsPayableOrReceivable,
      cashPoolId: 1,
      batchId: 1,
      currency: 'EUR',
      cashPoolLeaderId: 999,
    });

    expect(mockCreateCashPoolPayment).toHaveBeenCalledTimes(3);

    // Company 1001: Normal positive interest (receives)
    expect(mockCreateCashPoolPayment).toHaveBeenNthCalledWith(1, {
      currency: 'EUR',
      cashPoolBatchId: 1,
      cashPoolParticipantAccountId: 1,
      interestPayable: null,
      interestReceivable: 150,
      creditorId: 101,
      debtorId: 999,
    });

    // Company 1002: Negative interest but overridden to 0 (adjustedDebitInterestRate is 0)
    expect(mockCreateCashPoolPayment).toHaveBeenNthCalledWith(2, {
      currency: 'EUR',
      cashPoolBatchId: 1,
      cashPoolParticipantAccountId: 2,
      interestPayable: 0,
      interestReceivable: null,
      creditorId: 999,
      debtorId: 102,
    });

    // Company 1003: Normal negative interest (pays)
    expect(mockCreateCashPoolPayment).toHaveBeenNthCalledWith(3, {
      currency: 'EUR',
      cashPoolBatchId: 1,
      cashPoolParticipantAccountId: 3,
      interestPayable: 25,
      interestReceivable: null,
      creditorId: 999,
      debtorId: 103,
    });
  });

  test('It should not override when adjustedDebitInterestRate is positive even with negative interest', async () => {
    const mockAccounts = [
      {
        id: 1,
        adjustedDebitInterestRate: 0.04, // Positive debit rate
        participant: {
          id: 101,
          company: { id: 1001 }
        }
      }
    ];

    mockGetCashPoolParticipantAccounts.mockResolvedValue(mockAccounts);

    const participantsPayableOrReceivable = {
      '1001': -50  // Negative interest, but adjustedDebitInterestRate is positive
    };

    await createBatchParticipantPayments({
      participantsPayableOrReceivable,
      cashPoolId: 1,
      batchId: 1,
      currency: 'EUR',
      cashPoolLeaderId: 999,
    });

    // Should NOT override - company should pay the negative interest
    expect(mockCreateCashPoolPayment).toHaveBeenCalledWith({
      currency: 'EUR',
      cashPoolBatchId: 1,
      cashPoolParticipantAccountId: 1,
      interestPayable: 50,
      interestReceivable: null,
      creditorId: 999,
      debtorId: 101,
    });
  });
});
