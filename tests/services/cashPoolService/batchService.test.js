const { createBatchParticipantPayments } = require('../../../services/cashPoolService/runBatch/batchService');
const { cashPoolParticipantAccountRepository, cashPoolBatchPaymentsRepository } = require('../../../repositories');

jest.mock('../../../repositories', () => ({
  cashPoolParticipantAccountRepository: {
    getCashPoolParticipantAccounts: jest.fn(),
  },
  cashPoolBatchPaymentsRepository: {
    createCashPoolPayment: jest.fn(),
  },
}));

describe('Cash Pool Batch Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createBatchParticipantPayments', () => {
    const mockAccounts = [
      {
        id: 1,
        participant: {
          id: 101,
          company: { id: 1001 }
        }
      },
      {
        id: 2,
        participant: {
          id: 102,
          company: { id: 1002 }
        }
      }
    ];

    beforeEach(() => {
      cashPoolParticipantAccountRepository.getCashPoolParticipantAccounts.mockResolvedValue(mockAccounts);
      cashPoolBatchPaymentsRepository.createCashPoolPayment.mockResolvedValue({});
    });

    test('It should create payments with zero interest for companies with negative interest', async () => {
      const participantsPayableOrReceivable = {
        '1001': 100, // Company 1001 has positive interest
        '1002': -50  // Company 1002 has negative interest and will be set to 0
      };

      await createBatchParticipantPayments({
        participantsPayableOrReceivable,
        cashPoolId: 1,
        batchId: 1,
        currency: 'EUR',
        cashPoolLeaderId: 999,
      });

      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenCalledTimes(2);

      // First call - company 1001 with positive interest (should receive)
      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenNthCalledWith(1, {
        currency: 'EUR',
        cashPoolBatchId: 1,
        cashPoolParticipantAccountId: 1,
        interestPayable: null,
        interestReceivable: 100,
        creditorId: 101,
        debtorId: 999,
      });

      // Second call - company 1002 with negative interest (should have zero interest)
      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenNthCalledWith(2, {
        currency: 'EUR',
        cashPoolBatchId: 1,
        cashPoolParticipantAccountId: 2,
        interestPayable: 0,
        interestReceivable: null,
        creditorId: 999,
        debtorId: 102,
      });
    });

    test('It should create normal payments when all companies have positive interest', async () => {
      const participantsPayableOrReceivable = {
        '1001': 100,  // Company 1001 receives interest
        '1002': 50    // Company 1002 also receives interest
      };

      await createBatchParticipantPayments({
        participantsPayableOrReceivable,
        cashPoolId: 1,
        batchId: 1,
        currency: 'EUR',
        cashPoolLeaderId: 999,
      });

      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenCalledTimes(2);

      // Both companies should receive interest
      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenNthCalledWith(1, {
        currency: 'EUR',
        cashPoolBatchId: 1,
        cashPoolParticipantAccountId: 1,
        interestPayable: null,
        interestReceivable: 100,
        creditorId: 101,
        debtorId: 999,
      });

      expect(cashPoolBatchPaymentsRepository.createCashPoolPayment).toHaveBeenNthCalledWith(2, {
        currency: 'EUR',
        cashPoolBatchId: 1,
        cashPoolParticipantAccountId: 2,
        interestPayable: null,
        interestReceivable: 50,
        creditorId: 102,
        debtorId: 999,
      });
    });
  });
});
