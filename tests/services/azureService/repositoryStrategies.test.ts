import * as azureService from '../../../services/azureService';
import {
  loanRepository,
  loanFileRepository,
  b2bLoanRepository,
  b2bLoanFileRepository,
  guaranteeRepository,
  guaranteeFileRepository,
  creditRatingRepository,
  creditRatingFileRepository,
  cashPoolRepository,
  cashPoolFileRepository,
} from '../../../repositories';
import { reportEnums } from '../../../enums';
import { BadRequestError } from '../../../utils/ErrorHandler';

const { LOAN, B2B_LOAN, GUARANTEE, CREDIT_RATING, CASH_POOL } = reportEnums.REPORT_TYPES;

describe('Repository strategies', () => {
  describe('Tests for getFileGetterAndCreator', () => {
    test('It should test getFileGetterAndCreator strategy for loan', () => {
      const [getLoan, createLoanFile] = azureService.repositoryStrategies.getFileGetterAndCreator(LOAN);

      expect(getLoan).toEqual(loanRepository.getLoan);
      expect(createLoanFile).toEqual(loanFileRepository.createLoanFile);
    });

    test('It should test getFileGetterAndCreator strategy for b2b loan', () => {
      const [getLoan, createLoanFile] = azureService.repositoryStrategies.getFileGetterAndCreator(B2B_LOAN);

      expect(getLoan).toEqual(b2bLoanRepository.getLoan);
      expect(createLoanFile).toEqual(b2bLoanFileRepository.createLoanFile);
    });

    test('It should test getFileGetterAndCreator strategy for guarantee', () => {
      const [getGuarantee, createGuaranteeFile] = azureService.repositoryStrategies.getFileGetterAndCreator(GUARANTEE);

      expect(getGuarantee).toEqual(guaranteeRepository.getGuarantee);
      expect(createGuaranteeFile).toEqual(guaranteeFileRepository.createGuaranteeFile);
    });

    test('It should test getFileGetterAndCreator strategy for credit rating', () => {
      const [getCreditRating, createCreditRatingFile] =
        azureService.repositoryStrategies.getFileGetterAndCreator(CREDIT_RATING);

      expect(getCreditRating).toEqual(creditRatingRepository.getCreditRating);
      expect(createCreditRatingFile).toEqual(creditRatingFileRepository.createCreditRatingFile);
    });

    test('It should test getFileGetterAndCreator strategy for cash pool', () => {
      const [getCashPoolById, createCashPoolFile] =
        azureService.repositoryStrategies.getFileGetterAndCreator(CASH_POOL);

      expect(getCashPoolById).toEqual(cashPoolRepository.getCashPoolById);
      expect(createCashPoolFile).toEqual(cashPoolFileRepository.createCashPoolFile);
    });

    test('It should test getFileGetterAndCreator strategy for cash pool', () => {
      expect(() => azureService.repositoryStrategies.getFileGetterAndCreator('random_string' as any)).toThrow(
        BadRequestError,
      );
    });
  });

  describe('Tests for getFileGetter', () => {
    test('It should test getFileGetter strategy', () => {
      expect(azureService.repositoryStrategies.getFileGetter(LOAN)).toEqual(loanFileRepository.getLoanFile);
      expect(azureService.repositoryStrategies.getFileGetter(B2B_LOAN)).toEqual(b2bLoanFileRepository.getLoanFile);
      expect(azureService.repositoryStrategies.getFileGetter(GUARANTEE)).toEqual(
        guaranteeFileRepository.getGuaranteeFile,
      );
      expect(azureService.repositoryStrategies.getFileGetter(CREDIT_RATING)).toEqual(
        creditRatingFileRepository.getCreditRatingFile,
      );
      expect(azureService.repositoryStrategies.getFileGetter(CASH_POOL)).toEqual(
        cashPoolFileRepository.getCashPoolFile,
      );
    });

    test('It should test getFileGetter strategy for cash pool', () => {
      expect(() => azureService.repositoryStrategies.getFileGetter('random_string' as any)).toThrow(BadRequestError);
    });
  });

  describe('Tests for getFileDelete', () => {
    test('It should test getFileDelete strategy', () => {
      expect(azureService.repositoryStrategies.getFileDelete(LOAN)).toEqual(loanFileRepository.deleteLoanFile);
      expect(azureService.repositoryStrategies.getFileDelete(B2B_LOAN)).toEqual(b2bLoanFileRepository.deleteLoanFile);
      expect(azureService.repositoryStrategies.getFileDelete(GUARANTEE)).toEqual(
        guaranteeFileRepository.deleteGuaranteeFile,
      );
      expect(azureService.repositoryStrategies.getFileDelete(CREDIT_RATING)).toEqual(
        creditRatingFileRepository.deleteCreditRatingFile,
      );
      expect(azureService.repositoryStrategies.getFileDelete(CASH_POOL)).toEqual(
        cashPoolFileRepository.deleteCashPoolFile,
      );
    });

    test('It should test getFileGetter strategy for cash pool', () => {
      expect(() => azureService.repositoryStrategies.getFileDelete('random_string' as any)).toThrow(BadRequestError);
    });
  });

  describe('Tests for getFileUpdate', () => {
    test('It should test getFileUpdate strategy', () => {
      expect(azureService.repositoryStrategies.getFileUpdate(LOAN)).toEqual(loanFileRepository.updateLoanFile);
      expect(azureService.repositoryStrategies.getFileUpdate(B2B_LOAN)).toEqual(b2bLoanFileRepository.updateLoanFile);
      expect(azureService.repositoryStrategies.getFileUpdate(GUARANTEE)).toEqual(
        guaranteeFileRepository.updateGuaranteeFile,
      );
      expect(azureService.repositoryStrategies.getFileUpdate(CREDIT_RATING)).toEqual(
        creditRatingFileRepository.updateCreditRatingFile,
      );
      expect(azureService.repositoryStrategies.getFileUpdate(CASH_POOL)).toEqual(
        cashPoolFileRepository.updateCashPoolFile,
      );
    });

    test('It should test getFileGetter strategy for cash pool', () => {
      expect(() => azureService.repositoryStrategies.getFileUpdate('random_string' as any)).toThrow(BadRequestError);
    });
  });
});
