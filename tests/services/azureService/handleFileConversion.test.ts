import { AxiosResponse } from 'axios';

import * as azureService from '../../../services/azureService';
import * as gotenbergService from '../../../services/gotenbergService';
import { FileContentTypeEnum } from '../../../enums/files';
import { BadRequestError } from '../../../utils/ErrorHandler';

jest.mock('../../../services/gotenbergService');

const mimeType = FileContentTypeEnum.DOCX;
const reportFile = { name: 'fileName', extension: '.docx', mimeType };

describe('Handle file conversion', () => {
  test('It should test default handle conversion (no conversion)', async () => {
    const result = await azureService.handleFileConversion(undefined, Buffer.from('random_string'), reportFile);

    expect(result.buffer).toEqual(Buffer.from('random_string'));
    expect(result.contentType).toEqual(mimeType);
    expect(result.contentDisposition).toEqual(`attachment; filename="${reportFile.name}${reportFile.extension}"`);
  });

  test('It should test handle conversion to pdf', async () => {
    const mockGotenbergCovert = jest.spyOn(gotenbergService, 'convertOfficeDocumentToPDF');
    mockGotenbergCovert.mockResolvedValue({ data: Buffer.from('converted_random_string') } as AxiosResponse<Buffer>);

    const result = await azureService.handleFileConversion('pdf', Buffer.from('random_string'), reportFile);

    expect(result.buffer).toEqual(Buffer.from('converted_random_string'));
    expect(result.contentType).toEqual(FileContentTypeEnum.PDF);
    expect(result.contentDisposition).toEqual(`attachment; filename="${reportFile.name}.pdf"`);
  });

  test('It should throw for an unsupported file type', async () => {
    await expect(
      azureService.handleFileConversion('random_string' as any, Buffer.from('random_string'), reportFile),
    ).rejects.toThrow(BadRequestError);
  });
});
