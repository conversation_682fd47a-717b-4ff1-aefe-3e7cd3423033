const { paymentFrequency } = require('../../../../enums');
const { calculateRepayments } = require('../../../../services/solverService');

describe('Fixed balloon repayments', () => {
  test(`${paymentFrequency.MONTHLY} with Tenor of 4 years`, async () => {
    const { data } = await calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-09-27',
      maturityDate: '2025-09-27',
      principalAmount: 345000,
      paymentFrequency: paymentFrequency.MONTHLY,
      finalInterestRate: 3.4,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.totalNumberOfCompoundingPeriods).toBe(48.0);
    expect(data.interestRatePerInterestRepaymentFrequency).toBeCloseTo(0.0028);
    expect(data.totalRepayment).toBeCloseTo(395184.25);
    expect(data.totalInterest).toBeCloseTo(50184.25);
    expect(data.compoundingSchedule[0].compoundedInterest).toBeCloseTo(977.4999);
    expect(data.compoundingSchedule[0].additionalInterest).toBeCloseTo(977.4999);
    expect(data.compoundingSchedule[2].compoundedInterest).toBeCloseTo(2940.82);
    expect(data.compoundingSchedule[2].additionalInterest).toBeCloseTo(983.05);
    expect(data.compoundingSchedule[47].compoundedInterest).toBeCloseTo(50184.25);
    expect(data.compoundingSchedule[47].additionalInterest).toBeCloseTo(1116.525);
  });

  test(`${paymentFrequency.QUARTERLY} with Tenor of 4 years`, async () => {
    const { data } = await calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-09-27',
      maturityDate: '2025-09-27',
      principalAmount: 345000,
      paymentFrequency: paymentFrequency.QUARTERLY,
      finalInterestRate: 3.4,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.totalNumberOfCompoundingPeriods).toBe(16.0);
    expect(data.interestRatePerInterestRepaymentFrequency).toBeCloseTo(0.0085);
    expect(data.totalRepayment).toBeCloseTo(395033.14);
    expect(data.totalInterest).toBeCloseTo(50033.14);
    expect(data.compoundingSchedule[0].compoundedInterest).toBeCloseTo(2932.5);
    expect(data.compoundingSchedule[0].additionalInterest).toBeCloseTo(2932.5);
    expect(data.compoundingSchedule[2].compoundedInterest).toBeCloseTo(8872.49);
    expect(data.compoundingSchedule[2].additionalInterest).toBeCloseTo(2982.56);
    expect(data.compoundingSchedule[15].compoundedInterest).toBeCloseTo(50033.144);
    expect(data.compoundingSchedule[15].additionalInterest).toBeCloseTo(3329.481);
  });

  test(`${paymentFrequency.SEMI_ANNUAL} with Tenor of 4 years`, async () => {
    const { data } = await calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-09-27',
      maturityDate: '2025-09-27',
      principalAmount: 345000,
      paymentFrequency: paymentFrequency.SEMI_ANNUAL,
      finalInterestRate: 3.4,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.totalNumberOfCompoundingPeriods).toBe(8.0);
    expect(data.interestRatePerInterestRepaymentFrequency).toBeCloseTo(0.017);
    expect(data.totalRepayment).toBeCloseTo(394808.7);
    expect(data.totalInterest).toBeCloseTo(49808.7);
    expect(data.compoundingSchedule[0].compoundedInterest).toBeCloseTo(5864.9999);
    expect(data.compoundingSchedule[0].additionalInterest).toBeCloseTo(5864.9999);
    expect(data.compoundingSchedule[1].compoundedInterest).toBeCloseTo(11829.704);
    expect(data.compoundingSchedule[1].additionalInterest).toBeCloseTo(5964.704);
    expect(data.compoundingSchedule[2].compoundedInterest).toBeCloseTo(17895.809);
    expect(data.compoundingSchedule[2].additionalInterest).toBeCloseTo(6066.104);
    expect(data.compoundingSchedule[6].compoundedInterest).toBeCloseTo(43209.148);
    expect(data.compoundingSchedule[6].additionalInterest).toBeCloseTo(6489.238);
    expect(data.compoundingSchedule[7].compoundedInterest).toBeCloseTo(49808.703);
    expect(data.compoundingSchedule[7].additionalInterest).toBeCloseTo(6599.555);
  });

  test(`${paymentFrequency.ANNUAL} with Tenor of 4 years`, async () => {
    const { data } = await calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-09-27',
      maturityDate: '2025-09-27',
      principalAmount: 345000,
      paymentFrequency: paymentFrequency.ANNUAL,
      finalInterestRate: 3.4,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.totalNumberOfCompoundingPeriods).toBe(4.0);
    expect(data.interestRatePerInterestRepaymentFrequency).toBeCloseTo(0.034);
    expect(data.totalRepayment).toBeCloseTo(394367.62);
    expect(data.totalInterest).toBeCloseTo(49367.62);
    expect(data.compoundingSchedule[2].compoundedInterest).toBeCloseTo(36400.019);
    expect(data.compoundingSchedule[2].additionalInterest).toBeCloseTo(12541.199);
  });

  test(`${paymentFrequency.MONTHLY} with Tenor of 8 months`, async () => {
    const { data } = await calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-01-30',
      maturityDate: '2021-09-30',
      principalAmount: 5200000,
      paymentFrequency: paymentFrequency.MONTHLY,
      finalInterestRate: 3.0,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.totalNumberOfCompoundingPeriods).toBe(8.0);
    expect(data.interestRatePerInterestRepaymentFrequency).toBeCloseTo(0.0025);
    expect(data.totalRepayment).toBeCloseTo(5304914.56);
    expect(data.totalInterest).toBeCloseTo(104914.56);
    expect(data.compoundingSchedule[0].compoundedInterest).toBeCloseTo(13000.0);
    expect(data.compoundingSchedule[0].additionalInterest).toBeCloseTo(13000.0);
    expect(data.compoundingSchedule[1].compoundedInterest).toBeCloseTo(26032.499);
    expect(data.compoundingSchedule[1].additionalInterest).toBeCloseTo(13032.499);
    expect(data.compoundingSchedule[2].compoundedInterest).toBeCloseTo(39097.581);
    expect(data.compoundingSchedule[2].additionalInterest).toBeCloseTo(13065.081);
    expect(data.compoundingSchedule[6].compoundedInterest).toBeCloseTo(91685.35);
    expect(data.compoundingSchedule[6].additionalInterest).toBeCloseTo(13196.222);
    expect(data.compoundingSchedule[7].compoundedInterest).toBeCloseTo(104914.564);
    expect(data.compoundingSchedule[7].additionalInterest).toBeCloseTo(13229.213);
  });

  test(`${paymentFrequency.QUARTERLY} with Tenor of 8 months`, async () => {
    const { data } = await calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-01-30',
      maturityDate: '2021-09-30',
      principalAmount: 5200000,
      paymentFrequency: paymentFrequency.QUARTERLY,
      finalInterestRate: 3.0,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.totalNumberOfCompoundingPeriods).toBeCloseTo(2.6739);
    expect(data.interestRatePerInterestRepaymentFrequency).toBeCloseTo(0.0075);
    expect(data.totalRepayment).toBeCloseTo(5304938.31);
    expect(data.totalInterest).toBeCloseTo(104938.31);
    expect(data.compoundingSchedule[0].compoundedInterest).toBeCloseTo(39000.0);
    expect(data.compoundingSchedule[0].additionalInterest).toBeCloseTo(39000.0);
    expect(data.compoundingSchedule[1].compoundedInterest).toBeCloseTo(78292.5);
    expect(data.compoundingSchedule[1].additionalInterest).toBeCloseTo(39292.5);
    expect(data.compoundingSchedule[2].compoundedInterest).toBeCloseTo(104938.31);
    expect(data.compoundingSchedule[2].additionalInterest).toBeCloseTo(26645.81);
  });

  test(`${paymentFrequency.MONTHLY} with Tenor of 3 months and 20 days`, async () => {
    const { data } = await calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-01-31',
      maturityDate: '2021-05-20',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.MONTHLY,
      finalInterestRate: 1.2,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.totalNumberOfCompoundingPeriods).toBeCloseTo(3.6451);
    expect(data.interestRatePerInterestRepaymentFrequency).toBeCloseTo(0.001);
    expect(data.totalRepayment).toBeCloseTo(351277.494);
    expect(data.totalInterest).toBeCloseTo(1277.494);
    expect(data.compoundingSchedule[0].compoundedInterest).toBeCloseTo(349.9999);
    expect(data.compoundingSchedule[0].additionalInterest).toBeCloseTo(349.9999);
    expect(data.compoundingSchedule[1].compoundedInterest).toBeCloseTo(700.3499);
    expect(data.compoundingSchedule[1].additionalInterest).toBeCloseTo(350.3499);
    expect(data.compoundingSchedule[2].compoundedInterest).toBeCloseTo(1051.05);
    expect(data.compoundingSchedule[2].additionalInterest).toBeCloseTo(350.7);
    expect(data.compoundingSchedule[3].compoundedInterest).toBeCloseTo(1277.494);
    expect(data.compoundingSchedule[3].additionalInterest).toBeCloseTo(226.444);
  });

  test(`${paymentFrequency.QUARTERLY} with Tenor of 3 months and 20 days`, async () => {
    const { data } = await calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-01-31',
      maturityDate: '2021-05-20',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.QUARTERLY,
      finalInterestRate: 1.2,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.totalNumberOfCompoundingPeriods).toBeCloseTo(1.2247);
    expect(data.interestRatePerInterestRepaymentFrequency).toBeCloseTo(0.003);
    expect(data.totalRepayment).toBeCloseTo(351286.388);
    expect(data.totalInterest).toBeCloseTo(1286.388);
    expect(data.compoundingSchedule[0].compoundedInterest).toBeCloseTo(1049.9999);
    expect(data.compoundingSchedule[0].additionalInterest).toBeCloseTo(1049.9999);
    expect(data.compoundingSchedule[1].compoundedInterest).toBeCloseTo(1286.388);
    expect(data.compoundingSchedule[1].additionalInterest).toBeCloseTo(236.388);
  });

  test(`${paymentFrequency.SEMI_ANNUAL} with Tenor of 3 months and 20 days`, () => {
    calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-01-31',
      maturityDate: '2021-05-20',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.SEMI_ANNUAL,
      finalInterestRate: 1.2,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    }).catch((err) => expect(err.response.status === 400));
  });

  test(`${paymentFrequency.ANNUAL} with Tenor of 3 months and 20 days`, () => {
    calculateRepayments('fixed', 'balloon', {
      issueDate: '2021-01-31',
      maturityDate: '2021-05-20',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.ANNUAL,
      finalInterestRate: 1.2,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    }).catch((err) => expect(err.response.status === 400));
  });
});
