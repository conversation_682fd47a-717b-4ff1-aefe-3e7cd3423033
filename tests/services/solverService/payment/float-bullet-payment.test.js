const { paymentFrequency } = require('../../../../enums');
const { calculateRepayments } = require('../../../../services/solverService');

/**
 * Tests are basically the same as in fixed bullet.
 * Difference is the interestRate <=> basisPoints and estimationOfReferenceRate
 * interestRate = 3.4
 * basisPoints (140) and estimationOfReferenceRate (2) => (2 + (140 / 100) = 3.4)
 */
describe('Float bullet repayments', () => {
  test(`${paymentFrequency.MONTHLY} with Tenor of 4 years`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-09-27',
      maturityDate: '2025-09-27',
      principalAmount: 5200000,
      paymentFrequency: paymentFrequency.MONTHLY,
      basisPoints: 100,
      estimationOfReferenceRate: 2,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.0025);
    expect(data.paymentAtMaturity).toBe(5213000.0);
    expect(data.totalInterest).toBe(624000.0);
    expect(data.totalNumberOfPayments).toBe(48.0);
    expect(data.totalRepayment).toBe(5824000.0);
    expect(data.paymentSchedule.length).toBe(48);
  });

  test(`${paymentFrequency.QUARTERLY} with Tenor of 4 years`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-09-27',
      maturityDate: '2025-09-27',
      principalAmount: 5200000,
      paymentFrequency: paymentFrequency.QUARTERLY,
      basisPoints: 100,
      estimationOfReferenceRate: 2,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.0075);
    expect(data.paymentAtMaturity).toBe(5239000.0);
    expect(data.totalInterest).toBe(624000.0);
    expect(data.totalNumberOfPayments).toBe(16.0);
    expect(data.totalRepayment).toBe(5824000.0);
    expect(data.paymentSchedule.length).toBe(16);
  });

  test(`${paymentFrequency.SEMI_ANNUAL} with Tenor of 4 years`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-09-27',
      maturityDate: '2025-09-27',
      principalAmount: 5200000,
      paymentFrequency: paymentFrequency.SEMI_ANNUAL,
      basisPoints: 100,
      estimationOfReferenceRate: 2,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.015);
    expect(data.paymentAtMaturity).toBe(5277999.999999999);
    expect(data.totalInterest).toBe(624000.0);
    expect(data.totalNumberOfPayments).toBe(8.0);
    expect(data.totalRepayment).toBe(5824000.0);
    expect(data.paymentSchedule.length).toBe(8);
  });

  test(`${paymentFrequency.ANNUAL} with Tenor of 4 years`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-09-27',
      maturityDate: '2025-09-27',
      principalAmount: 5200000,
      paymentFrequency: paymentFrequency.ANNUAL,
      basisPoints: 100,
      estimationOfReferenceRate: 2,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.03);
    expect(data.paymentAtMaturity).toBe(5356000.0);
    expect(data.totalInterest).toBe(624000.0);
    expect(data.totalNumberOfPayments).toBe(4.0);
    expect(data.totalRepayment).toBe(5824000.0);
    expect(data.paymentSchedule.length).toBe(4);
  });

  test(`${paymentFrequency.MONTHLY} with Tenor of 1 year`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-04-18',
      maturityDate: '2022-04-18',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.MONTHLY,
      basisPoints: 20,
      estimationOfReferenceRate: 1,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.001);
    expect(data.paymentAtMaturity).toBe(350349.99999999994);
    expect(data.totalInterest).toBe(4200.0);
    expect(data.totalNumberOfPayments).toBe(12.0);
    expect(data.totalRepayment).toBe(354200.0);
    expect(data.paymentSchedule.length).toBe(12);
  });

  test(`${paymentFrequency.SEMI_ANNUAL} with Tenor of 1 year`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-04-18',
      maturityDate: '2022-04-18',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.SEMI_ANNUAL,
      basisPoints: 20,
      estimationOfReferenceRate: 1,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.006);
    expect(data.paymentAtMaturity).toBe(352100.0);
    expect(data.totalInterest).toBe(4200.0);
    expect(data.totalNumberOfPayments).toBe(2.0);
    expect(data.totalRepayment).toBe(354200.0);
    expect(data.paymentSchedule.length).toBe(2);
  });

  test(`${paymentFrequency.MONTHLY} with Tenor of 5 months`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-01-30',
      maturityDate: '2021-06-30',
      principalAmount: 5200000,
      paymentFrequency: paymentFrequency.MONTHLY,
      basisPoints: 150,
      estimationOfReferenceRate: 1.5,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.0025);
    expect(data.paymentAtMaturity).toBe(5213000.0);
    expect(data.totalInterest).toBe(65000.0);
    expect(data.totalNumberOfPayments).toBe(5.0);
    expect(data.totalRepayment).toBe(5265000.0);
    expect(data.paymentSchedule.length).toBe(5);
  });

  test(`${paymentFrequency.QUARTERLY} with Tenor of 5 months`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-01-30',
      maturityDate: '2021-06-30',
      principalAmount: 5200000,
      paymentFrequency: paymentFrequency.QUARTERLY,
      basisPoints: 150,
      estimationOfReferenceRate: 1.5,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.0075);
    expect(data.paymentAtMaturity).toBe(5225858.695652174);
    expect(data.totalInterest).toBe(64858.69565217391);
    expect(data.totalNumberOfPayments).toBe(1.6630434782608696);
    expect(data.totalRepayment).toBe(5264858.695652174);
    expect(data.paymentSchedule.length).toBe(2);
  });

  test(`${paymentFrequency.MONTHLY} with Tenor of 3 months and 20 days`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-01-31',
      maturityDate: '2021-05-20',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.MONTHLY,
      basisPoints: 20,
      estimationOfReferenceRate: 1,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.001);
    expect(data.paymentAtMaturity).toBe(350225.8064516129);
    expect(data.totalInterest).toBe(1275.8064516129032);
    expect(data.totalNumberOfPayments).toBe(3.6451612903225805);
    expect(data.totalRepayment).toBe(351275.8064516129);
    expect(data.paymentSchedule[0].interestPayment).toBe(350);
    expect(data.paymentSchedule[1].interestPayment).toBe(350);
    expect(data.paymentSchedule[2].interestPayment).toBe(350);
    expect(data.paymentSchedule[3].interestPayment).toBeCloseTo(225.8064);
  });

  test(`${paymentFrequency.QUARTERLY} with Tenor of 3 months and 20 days`, async () => {
    const { data } = await calculateRepayments('float', 'bullet', {
      issueDate: '2021-01-31',
      maturityDate: '2021-05-20',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.QUARTERLY,
      basisPoints: 20,
      estimationOfReferenceRate: 1,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    });

    expect(data.interestRatePerInterestRepaymentFrequency).toBe(0.003);
    expect(data.paymentAtMaturity).toBe(350235.9550561798);
    expect(data.totalInterest).toBe(1285.9550561797753);
    expect(data.totalNumberOfPayments).toBe(1.2247191011235956);
    expect(data.totalRepayment).toBe(351285.9550561798);
    expect(data.paymentSchedule[0].interestPayment).toBe(1050);
    expect(data.paymentSchedule[1].interestPayment).toBeCloseTo(235.955);
  });

  test(`${paymentFrequency.SEMI_ANNUAL} with Tenor of 3 months and 20 days`, () => {
    calculateRepayments('float', 'bullet', {
      issueDate: '2021-01-31',
      maturityDate: '2021-05-20',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.SEMI_ANNUAL,
      basisPoints: 20,
      estimationOfReferenceRate: 1,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    }).catch((err) => expect(err.response.status === 400));
  });

  test(`${paymentFrequency.ANNUAL} with Tenor of 3 months and 20 days`, () => {
    calculateRepayments('float', 'bullet', {
      issueDate: '2021-01-31',
      maturityDate: '2021-05-20',
      principalAmount: 350000,
      paymentFrequency: paymentFrequency.ANNUAL,
      basisPoints: 20,
      estimationOfReferenceRate: 1,
      whtInterestRate: 0,
      approach: null,
      isWhtEnabled: false,
    }).catch((err) => expect(err.response.status === 400));
  });
});
