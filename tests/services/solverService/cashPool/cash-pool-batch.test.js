const { calculateCashPoolBatch } = require('../../../../services/solverService');

const cashPoolBatchDataFiveDays = [
  {
    date: '2021-07-20',
    totalRisk: 0.35,
    CPL: {
      name: 'LEADER',
      credit_IR: 2.25,
      debit_IR: 3.5,
      markup: 0.2,
      operating_expenses: 20000,
    },
    CPP: [
      {
        name: 'Apple',
        balance: -70000,
        credit_IR: 2,
        debit_IR: 3.75,
        accountId: 1,
      },
      {
        name: '<PERSON><PERSON>',
        balance: 30000,
        credit_IR: 1.75,
        debit_IR: 4,
        accountId: 2,
      },
      {
        name: '<PERSON>er<PERSON>',
        balance: -42000,
        credit_IR: 1.5,
        debit_IR: 4.25,
        accountId: 3,
      },
    ],
  },
  {
    date: '2021-07-21',
    totalRisk: 0.35,
    CPL: {
      name: 'LEADER',
      credit_IR: 2.25,
      debit_IR: 3.5,
      markup: 0.2,
      operating_expenses: 20000,
    },
    CPP: [
      {
        name: '<PERSON>',
        balance: 60000,
        credit_IR: 2,
        debit_IR: 3.75,
        accountId: 1,
      },
      {
        name: '<PERSON><PERSON>',
        balance: -40000,
        credit_IR: 1.75,
        debit_IR: 4,
        accountId: 2,
      },
      {
        name: 'Cherr',
        balance: -30000,
        credit_IR: 1.5,
        debit_IR: 4.25,
        accountId: 3,
      },
    ],
  },
  {
    date: '2021-07-22',
    totalRisk: 0.35,
    CPL: {
      name: 'LEADER',
      credit_IR: 2.25,
      debit_IR: 3.5,
      markup: 0.2,
      operating_expenses: 20000,
    },
    CPP: [
      {
        name: 'Apple',
        balance: -12000,
        credit_IR: 2,
        debit_IR: 3.75,
        accountId: 1,
      },
      {
        name: 'Banan',
        balance: 30000,
        credit_IR: 1.75,
        debit_IR: 4,
        accountId: 2,
      },
      {
        name: 'Cherr',
        balance: 30000,
        credit_IR: 1.5,
        debit_IR: 4.25,
        accountId: 3,
      },
    ],
  },
  {
    date: '2021-07-23',
    totalRisk: 0.35,
    CPL: {
      name: 'LEADER',
      credit_IR: 2.25,
      debit_IR: 3.5,
      markup: 0.2,
      operating_expenses: 20000,
    },
    CPP: [
      {
        name: 'Apple',
        balance: 50000,
        credit_IR: 2,
        debit_IR: 3.75,
        accountId: 1,
      },
      {
        name: 'Banan',
        balance: -20000,
        credit_IR: 1.75,
        debit_IR: 4,
        accountId: 2,
      },
      {
        name: 'Cherr',
        balance: -72000,
        credit_IR: 1.5,
        debit_IR: 4.25,
        accountId: 3,
      },
    ],
  },
  {
    date: '2021-07-24',
    totalRisk: 0.35,
    CPL: {
      name: 'LEADER',
      credit_IR: 2.25,
      debit_IR: 3.5,
      markup: 0.2,
      operating_expenses: 20000,
    },
    CPP: [
      {
        name: 'Apple',
        balance: 90000,
        credit_IR: 2,
        debit_IR: 3.75,
        accountId: 1,
      },
      {
        name: 'Banan',
        balance: 66000,
        credit_IR: 1.75,
        debit_IR: 4,
        accountId: 2,
      },
      {
        name: 'Cherr',
        balance: 30000,
        credit_IR: 1.5,
        debit_IR: 4.25,
        accountId: 3,
      },
    ],
  },
];

const cashPoolBatchDataOneDay = [
  {
    date: '2021-07-20',
    totalRisk: 0.2,
    CPL: {
      credit_IR: 3 / 360,
      debit_IR: 4 / 360,
      name: 'CPL',
      markup: 0,
      operating_expenses: 0,
    },
    CPP: [
      {
        balance: -1_300_000,
        credit_IR: 1.7 / 360,
        debit_IR: 6.3 / 360,
        name: 'Company A',
        accountId: 1,
      },
      {
        balance: 200_000,
        credit_IR: 0.9 / 360,
        debit_IR: 9 / 360,
        name: 'Company B',
        accountId: 2,
      },
      {
        balance: 800_000,
        credit_IR: 0.4 / 360,
        debit_IR: 4.9 / 360,
        name: 'Company C',
        accountId: 3,
      },
    ],
  },
];

describe('Cash Pool Batch', () => {
  test('Pools cash for five dates with three participants', async () => {
    const { data } = await calculateCashPoolBatch(cashPoolBatchDataFiveDays);

    expect(data.batchGrossBenefit).toBeCloseTo(5064.999);

    expect(data.leaderPayableReceivable.payableToLeader).toBeCloseTo(2948.337);
    expect(data.leaderPayableReceivable.receivableFromLeader).toBeCloseTo(3038.337);

    expect(data.participantsPayableOrReceivable['Apple']).toBeCloseTo(1897.427);
    expect(data.participantsPayableOrReceivable['Banan']).toBeCloseTo(1050.909);
    expect(data.participantsPayableOrReceivable['Cherr']).toBeCloseTo(-3038.337);

    const { companiesDataOverTime } = data;

    expect(companiesDataOverTime[0].cashPoolGrossBenefit).toBeCloseTo(1014.999);
    expect(companiesDataOverTime[0].totalOperatingCost).toBeCloseTo(20040);

    expect(companiesDataOverTime[0].companies[0].adjCir).toBeCloseTo(0.02);
    expect(companiesDataOverTime[0].companies[0].adjDir).toBeCloseTo(0.034);
    expect(companiesDataOverTime[0].companies[0].netInterestBenefit).toBeCloseTo(241.65);
    expect(companiesDataOverTime[0].companies[1].adjCir).toBeCloseTo(0.0244);
    expect(companiesDataOverTime[0].companies[1].adjDir).toBeCloseTo(0.04);
    expect(companiesDataOverTime[0].companies[1].netInterestBenefit).toBeCloseTo(207.128);
    expect(companiesDataOverTime[0].companies[2].adjCir).toBeCloseTo(0.015);
    expect(companiesDataOverTime[0].companies[2].adjDir).toBeCloseTo(0.0321);
    expect(companiesDataOverTime[0].companies[2].netInterestBenefit).toBeCloseTo(434.97);

    expect(companiesDataOverTime[1].cashPoolGrossBenefit).toBeCloseTo(1325);
    expect(companiesDataOverTime[1].totalOperatingCost).toBeCloseTo(20040);

    expect(companiesDataOverTime[1].companies[0].adjCir).toBeCloseTo(0.0246);
    expect(companiesDataOverTime[1].companies[0].adjDir).toBeCloseTo(0.0375);
    expect(companiesDataOverTime[1].companies[0].adjustedInterestPaid).toBeCloseTo(0);
    expect(companiesDataOverTime[1].companies[0].adjustedInterestReceived).toBeCloseTo(1477.173);
    expect(companiesDataOverTime[1].companies[0].balance).toBeCloseTo(60000);
    expect(companiesDataOverTime[1].companies[0].cir).toBeCloseTo(0.02);
    expect(companiesDataOverTime[1].companies[0].companyId).toBe('Apple');
    expect(companiesDataOverTime[1].companies[0].creditBalance).toBeCloseTo(60000);
    expect(companiesDataOverTime[1].companies[0].debitBalance).toBeCloseTo(0);
    expect(companiesDataOverTime[1].companies[0].dir).toBeCloseTo(0.0375);
    expect(companiesDataOverTime[1].companies[0].netInterestBenefit).toBeCloseTo(277.173);

    expect(companiesDataOverTime[1].companies[1].adjCir).toBeCloseTo(0.0175);
    expect(companiesDataOverTime[1].companies[1].adjDir).toBeCloseTo(0.03472);
    expect(companiesDataOverTime[1].companies[1].adjustedInterestPaid).toBeCloseTo(1230.434);
    expect(companiesDataOverTime[1].companies[1].adjustedInterestReceived).toBeCloseTo(0);
    expect(companiesDataOverTime[1].companies[1].balance).toBeCloseTo(-40000);
    expect(companiesDataOverTime[1].companies[1].cir).toBeCloseTo(0.0175);
    expect(companiesDataOverTime[1].companies[1].companyId).toBe('Banan');
    expect(companiesDataOverTime[1].companies[1].creditBalance).toBeCloseTo(0);
    expect(companiesDataOverTime[1].companies[1].debitBalance).toBeCloseTo(40000);
    expect(companiesDataOverTime[1].companies[1].dir).toBeCloseTo(0.04);
    expect(companiesDataOverTime[1].companies[1].netInterestBenefit).toBeCloseTo(369.565);

    expect(companiesDataOverTime[1].companies[2].adjCir).toBeCloseTo(0.015);
    expect(companiesDataOverTime[1].companies[2].adjDir).toBeCloseTo(0.028);
    expect(companiesDataOverTime[1].companies[2].adjustedInterestPaid).toBeCloseTo(859.239);
    expect(companiesDataOverTime[1].companies[2].adjustedInterestReceived).toBeCloseTo(0);
    expect(companiesDataOverTime[1].companies[2].balance).toBeCloseTo(-30000);
    expect(companiesDataOverTime[1].companies[2].cir).toBeCloseTo(0.015);
    expect(companiesDataOverTime[1].companies[2].companyId).toBe('Cherr');
    expect(companiesDataOverTime[1].companies[2].creditBalance).toBeCloseTo(0);
    expect(companiesDataOverTime[1].companies[2].debitBalance).toBeCloseTo(30000);
    expect(companiesDataOverTime[1].companies[2].dir).toBeCloseTo(0.0425);
    expect(companiesDataOverTime[1].companies[2].netInterestBenefit).toBeCloseTo(415.76);

    expect(companiesDataOverTime[2].companies[0].adjCir).toBeCloseTo(0.02);
    expect(companiesDataOverTime[2].companies[0].adjDir).toBeCloseTo(0.0343);
    expect(companiesDataOverTime[2].companies[0].netInterestBenefit).toBeCloseTo(37.222);
    expect(companiesDataOverTime[2].companies[1].adjCir).toBeCloseTo(0.023);
    expect(companiesDataOverTime[2].companies[1].adjDir).toBeCloseTo(0.04);
    expect(companiesDataOverTime[2].companies[1].netInterestBenefit).toBeCloseTo(186.111);
    expect(companiesDataOverTime[2].companies[2].adjCir).toBeCloseTo(0.0234);
    expect(companiesDataOverTime[2].companies[2].adjDir).toBeCloseTo(0.0425);
    expect(companiesDataOverTime[2].companies[2].netInterestBenefit).toBeCloseTo(279.1666);

    expect(companiesDataOverTime[3].companies[0].adjCir).toBeCloseTo(0.0238);
    expect(companiesDataOverTime[3].companies[0].adjDir).toBeCloseTo(0.0375);
    expect(companiesDataOverTime[3].companies[0].netInterestBenefit).toBeCloseTo(191.38);
    expect(companiesDataOverTime[3].companies[1].adjCir).toBeCloseTo(0.0175);
    expect(companiesDataOverTime[3].companies[1].adjDir).toBeCloseTo(0.0323);
    expect(companiesDataOverTime[3].companies[1].netInterestBenefit).toBeCloseTo(153.104);
    expect(companiesDataOverTime[3].companies[2].adjCir).toBeCloseTo(0.015);
    expect(companiesDataOverTime[3].companies[2].adjDir).toBeCloseTo(0.031);
    expect(companiesDataOverTime[3].companies[2].netInterestBenefit).toBeCloseTo(826.764);

    expect(companiesDataOverTime[4].companies[0].adjCir).toBeCloseTo(0.0225);
    expect(companiesDataOverTime[4].companies[0].adjDir).toBeCloseTo(0.0375);
    expect(companiesDataOverTime[4].companies[0].netInterestBenefit).toBeCloseTo(225);
    expect(companiesDataOverTime[4].companies[1].adjCir).toBeCloseTo(0.0225);
    expect(companiesDataOverTime[4].companies[1].adjDir).toBeCloseTo(0.04);
    expect(companiesDataOverTime[4].companies[1].netInterestBenefit).toBeCloseTo(330);
    expect(companiesDataOverTime[4].companies[2].adjCir).toBeCloseTo(0.0225);
    expect(companiesDataOverTime[4].companies[2].adjDir).toBeCloseTo(0.0425);
    expect(companiesDataOverTime[4].companies[2].netInterestBenefit).toBeCloseTo(225);
  });

  test('Pools cash for one date with three participants', async () => {
    const { data } = await calculateCashPoolBatch(cashPoolBatchDataOneDay);

    expect(data.batchGrossBenefit).toBeCloseTo(180.277);

    expect(data.leaderPayableReceivable.payableToLeader).toBeCloseTo(93.452);
    expect(data.leaderPayableReceivable.receivableFromLeader).toBeCloseTo(132.341);

    expect(data.participantsPayableOrReceivable['Company A']).toBeCloseTo(-132.341);
    expect(data.participantsPayableOrReceivable['Company B']).toBeCloseTo(18.366);
    expect(data.participantsPayableOrReceivable['Company C']).toBeCloseTo(75.086);

    const { companiesDataOverTime } = data;

    expect(companiesDataOverTime[0].companies[0].netInterestBenefit).toBeCloseTo(95.158);
    expect(companiesDataOverTime[0].companies[0].adjustedInterestPaid).toBeCloseTo(132.341);
    expect(companiesDataOverTime[0].companies[0].adjustedInterestReceived).toBeCloseTo(0);
    expect(companiesDataOverTime[0].companies[0].adjCir).toBeCloseTo(0.0000472);
    expect(companiesDataOverTime[0].companies[0].adjDir).toBeCloseTo(0.0001);

    expect(companiesDataOverTime[0].companies[1].netInterestBenefit).toBeCloseTo(13.366);
    expect(companiesDataOverTime[0].companies[1].adjustedInterestPaid).toBeCloseTo(0);
    expect(companiesDataOverTime[0].companies[1].adjustedInterestReceived).toBeCloseTo(18.366);
    expect(companiesDataOverTime[0].companies[1].adjCir).toBeCloseTo(0.0000918);
    expect(companiesDataOverTime[0].companies[1].adjDir).toBeCloseTo(0.00025);

    expect(companiesDataOverTime[0].companies[2].netInterestBenefit).toBeCloseTo(66.197);
    expect(companiesDataOverTime[0].companies[2].adjustedInterestPaid).toBeCloseTo(0);
    expect(companiesDataOverTime[0].companies[2].adjustedInterestReceived).toBeCloseTo(75.086);
    expect(companiesDataOverTime[0].companies[2].adjCir).toBeCloseTo(0.0000938);
    expect(companiesDataOverTime[0].companies[2].adjDir).toBeCloseTo(0.00013);
  });
});
