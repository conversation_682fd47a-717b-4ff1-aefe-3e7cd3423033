import * as solverService from '../../../../services/solverService';

describe('Combine Cash Pool Batches Data', () => {
  test('It should combine data from 3 cash pools for two dates', async () => {
    const layer1Data = {
      A1: [
        ['2021-12-31T23:00:00.000Z', 'Apricot Tree Inc.', 200],
        ['2021-12-31T23:00:00.000Z', 'Avocado Tree AS', -100],
        ['2022-01-01T23:00:00.000Z', 'Apricot Tree Inc', 300],
        ['2022-01-01T23:00:00.000Z', 'Avocado Tree AS', -300],
      ],
      B1: [
        ['2021-12-31T23:00:00.000Z', 'Bamboo Tree Ltd.', 200],
        ['2021-12-31T23:00:00.000Z', 'Banana Tree AS', -100],
        ['2022-01-01T23:00:00.000Z', 'Bamboo Tree Ltd.', 400],
        ['2022-01-01T23:00:00.000Z', 'Banana Tree AS', -100],
      ],
      C1: [
        ['2021-12-31T23:00:00.000Z', 'Cheese Tree B.V.', -200],
        ['2021-12-31T23:00:00.000Z', 'Cherry Tree Ltd.', 500],
        ['2022-01-01T23:00:00.000Z', 'Cheese Tree B.V.', 100],
        ['2022-01-01T23:00:00.000Z', 'Cherry Tree Ltd.', 100],
      ],
    };
    const { data } = await solverService.getCombinedCashPoolBatchData(layer1Data);

    expect(data).toEqual([
      ['2021-12-31T23:00:00.000Z', 'A1', 100],
      ['2021-12-31T23:00:00.000Z', 'B1', 100],
      ['2021-12-31T23:00:00.000Z', 'C1', 300],
      ['2022-01-01T23:00:00.000Z', 'A1', 0],
      ['2022-01-01T23:00:00.000Z', 'B1', 300],
      ['2022-01-01T23:00:00.000Z', 'C1', 200],
    ]);
  });

  test('It should combine data from 3 cash pools for a single date', async () => {
    const layer1Data = {
      A1: [
        ['2021-12-31T23:00:00.000Z', 'Apricot Tree Inc.', 200],
        ['2021-12-31T23:00:00.000Z', 'Avocado Tree AS', 200],
      ],
      B1: [
        ['2021-12-31T23:00:00.000Z', 'Bamboo Tree Ltd.', -300],
        ['2021-12-31T23:00:00.000Z', 'Banana Tree AS', 300],
      ],
      C1: [
        ['2021-12-31T23:00:00.000Z', 'Cheese Tree B.V.', -200],
        ['2021-12-31T23:00:00.000Z', 'Cherry Tree Ltd.', 100],
        ['2021-12-31T23:00:00.000Z', 'Orange Tree', -400],
        ['2021-12-31T23:00:00.000Z', 'Green Tree Group, Inc.', 300],
      ],
    };
    const { data } = await solverService.getCombinedCashPoolBatchData(layer1Data);

    expect(data).toEqual([
      ['2021-12-31T23:00:00.000Z', 'A1', 400],
      ['2021-12-31T23:00:00.000Z', 'B1', 0],
      ['2021-12-31T23:00:00.000Z', 'C1', -200],
    ]);
  });

  test('It should combine data from 3 cash pools for two dates', async () => {
    const layer1Data = {
      A1: [
        ['2021-12-31T23:00:00.000Z', 'Apricot Tree Inc.', 100],
        ['2021-12-31T23:00:00.000Z', 'Avocado Tree AS', 300],
        ['2021-12-31T23:00:00.000Z', 'Gum Tree Ltda.', -200],
        ['2022-01-01T23:00:00.000Z', 'Apricot Tree Inc', 200],
        ['2022-01-01T23:00:00.000Z', 'Avocado Tree AS', -100],
        ['2022-01-01T23:00:00.000Z', 'Gum Tree Ltda.', -100],
      ],
      B1: [
        ['2021-12-31T23:00:00.000Z', 'Bamboo Tree Ltd.', 400],
        ['2021-12-31T23:00:00.000Z', 'Banana Tree AS', -200],
        ['2021-12-31T23:00:00.000Z', 'Orange Tree', -200],
        ['2022-01-01T23:00:00.000Z', 'Bamboo Tree Ltd.', 300],
        ['2022-01-01T23:00:00.000Z', 'Banana Tree AS', -300],
        ['2022-01-01T23:00:00.000Z', 'Orange Tree', 100],
      ],
      C1: [
        ['2021-12-31T23:00:00.000Z', 'Cheese Tree B.V.', 200],
        ['2021-12-31T23:00:00.000Z', 'Cherry Tree Ltd.', -400],
        ['2021-12-31T23:00:00.000Z', 'Green Tree Group, Inc.', 300],
        ['2022-01-01T23:00:00.000Z', 'Cheese Tree B.V.', 300],
        ['2022-01-01T23:00:00.000Z', 'Cherry Tree Ltd.', 200],
        ['2022-01-01T23:00:00.000Z', 'Green Tree Group, Inc.', 100],
      ],
    };
    const { data } = await solverService.getCombinedCashPoolBatchData(layer1Data);

    expect(data).toEqual([
      ['2021-12-31T23:00:00.000Z', 'A1', 200],
      ['2021-12-31T23:00:00.000Z', 'B1', 0],
      ['2021-12-31T23:00:00.000Z', 'C1', 100],
      ['2022-01-01T23:00:00.000Z', 'A1', 0],
      ['2022-01-01T23:00:00.000Z', 'B1', 100],
      ['2022-01-01T23:00:00.000Z', 'C1', 600],
    ]);
  });

  test('It should combine data from 3 cash pools for two dates', async () => {
    const layer1Data = {
      A1: [
        ['2021-12-31T23:00:00.000Z', 'Apricot Tree Inc.', 100],
        ['2021-12-31T23:00:00.000Z', 'Avocado Tree AS', 300],
        ['2022-01-01T23:00:00.000Z', 'Apricot Tree Inc', 200],
        ['2022-01-01T23:00:00.000Z', 'Avocado Tree AS', -100],
      ],
      B1: [
        ['2021-12-31T23:00:00.000Z', 'Bamboo Tree Ltd.', 400],
        ['2021-12-31T23:00:00.000Z', 'Banana Tree AS', -200],
        ['2021-12-31T23:00:00.000Z', 'Orange Tree', -200],
        ['2022-01-01T23:00:00.000Z', 'Bamboo Tree Ltd.', 300],
        ['2022-01-01T23:00:00.000Z', 'Banana Tree AS', -300],
        ['2022-01-01T23:00:00.000Z', 'Orange Tree', 100],
      ],
      C1: [
        ['2021-12-31T23:00:00.000Z', 'Cheese Tree B.V.', 200],
        ['2021-12-31T23:00:00.000Z', 'Cherry Tree Ltd.', -400],
        ['2021-12-31T23:00:00.000Z', 'Green Tree Group, Inc.', 300],
        ['2021-12-31T23:00:00.000Z', 'Gum Tree Ltda.', -200],
        ['2022-01-01T23:00:00.000Z', 'Cheese Tree B.V.', 300],
        ['2022-01-01T23:00:00.000Z', 'Cherry Tree Ltd.', 200],
        ['2022-01-01T23:00:00.000Z', 'Green Tree Group, Inc.', 100],
        ['2022-01-01T23:00:00.000Z', 'Gum Tree Ltda.', -100],
      ],
    };
    const { data } = await solverService.getCombinedCashPoolBatchData(layer1Data);

    expect(data).toEqual([
      ['2021-12-31T23:00:00.000Z', 'A1', 400],
      ['2021-12-31T23:00:00.000Z', 'B1', 0],
      ['2021-12-31T23:00:00.000Z', 'C1', -100],
      ['2022-01-01T23:00:00.000Z', 'A1', 100],
      ['2022-01-01T23:00:00.000Z', 'B1', 100],
      ['2022-01-01T23:00:00.000Z', 'C1', 500],
    ]);
  });

  test('It should combine data from 4 cash pools for a single date', async () => {
    const layer1Data = {
      A1: [
        ['2021-12-31T23:00:00.000Z', 'Apricot Tree Inc.', 200],
        ['2021-12-31T23:00:00.000Z', 'Avocado Tree AS', 200],
      ],
      B1: [
        ['2021-12-31T23:00:00.000Z', 'Bamboo Tree Ltd.', -300],
        ['2021-12-31T23:00:00.000Z', 'Banana Tree AS', 300],
      ],
      C1: [
        ['2021-12-31T23:00:00.000Z', 'Cheese Tree B.V.', -200],
        ['2021-12-31T23:00:00.000Z', 'Cherry Tree Ltd.', 100],
        ['2021-12-31T23:00:00.000Z', 'Orange Tree', -400],
        ['2021-12-31T23:00:00.000Z', 'Green Tree Group, Inc.', 300],
      ],
      D1: [
        ['2021-12-31T23:00:00.000Z', 'Apple Tree B.V.', 100],
        ['2021-12-31T23:00:00.000Z', 'Tangerine Tree Ltd.', 100],
        ['2021-12-31T23:00:00.000Z', 'Pear Tree', 300],
        ['2021-12-31T23:00:00.000Z', 'Yellow Tree Group, Inc.', -500],
      ],
    };
    const { data } = await solverService.getCombinedCashPoolBatchData(layer1Data);

    expect(data).toEqual([
      ['2021-12-31T23:00:00.000Z', 'A1', 400],
      ['2021-12-31T23:00:00.000Z', 'B1', 0],
      ['2021-12-31T23:00:00.000Z', 'C1', -200],
      ['2021-12-31T23:00:00.000Z', 'D1', 0],
    ]);
  });
});
