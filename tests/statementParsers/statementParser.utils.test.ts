import * as mt940Extractor from '../../services/statementParsers/extractors/mt940Extractor';

const cashPoolAccountId = 300;
const companyId = 20000;
const transactionReferenceNumber = `BNP-L2-${companyId}-Random Company Name`;

describe('Statement Parser Utils', () => {
  test('It should correctly parse the Mt940 format', () => {
    const input = [
      `:20:${transactionReferenceNumber}`,
      `:25:${companyId}`,
      ':28C:5/1',
      ':60F:C230630EUR894,12',
      ':61:2307310731D1,55NCHG130117688200EUR//NONREF',
      '********************',
      ':86:806?00POSTG / SERVICE CHG?1099199?20Postage',
      ':61:2307310731D2,5NCHG130117688200EUR//NONREF',
      '********************',
      ':86:808?00CHARGES?1099199?20Electronic Statement',
      ':61:2307310731D5,NCHG130117688200EUR//NONREF',
      '********************',
      ':86:808?00CHARGES?1099199?20Electronic Statement',
      ':61:2307310731D5,NCHG130117688200EUR//NONREF',
      '********************',
      ':86:808?00CHARGES?1099199?20Electronic Statement',
      ':61:2307310731D22,5NCHG130117688200EUR//NONREF',
      '********************',
      ':86:808?00CHARGES?1099199?20Account Maintenance Fee',
      ':62F:C230822EUR300000,00',
      '-',
    ];

    const data = mt940Extractor.extract(input, { [companyId]: cashPoolAccountId });
    expect(data).toEqual({
      transactionReferenceNumber,
      cashPoolAccountId,
      statementNumber: '5/1',
      balanceChange: 300000,
      date: new Date('2023-08-21T22:00:00.000Z'),
    });
  });

  test('It should fail parsing the Mt940 format if transactionReferenceNumber is missing', () => {
    const input = [':60F:C230630EUR894,12'];
    const errorMessage = 'Missing key "transactionReferenceNumber" in block: {}';

    expect(() => mt940Extractor.extract(input, { [companyId]: cashPoolAccountId })).toThrow(errorMessage);
  });

  test('It should fail parsing the Mt940 format if position is missing', () => {
    const input = [`:20:${transactionReferenceNumber}`, `:25:${companyId}`, ':28C:5/1'];
    const errorMessage = `Missing key "balanceChange\" in block: {"transactionReferenceNumber":"${transactionReferenceNumber}","cashPoolAccountId":${cashPoolAccountId},"statementNumber":"5/1"}`;

    expect(() => mt940Extractor.extract(input, { [companyId]: cashPoolAccountId })).toThrow(errorMessage);
  });
});
