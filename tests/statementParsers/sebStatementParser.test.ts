import * as statementParsers from '../../services/statementParsers';

const cashPoolAccountId = 777;
const companyId = 30000;
const transactionReferenceNumber = `BNP-L2-${companyId}-Random Company Name`;

describe('SEB statement parser', () => {
  test('It should correctly read and parse the Mt940 file in Buffer format', async () => {
    const input = `
      :20:${transactionReferenceNumber}
      :25:${companyId}
      :28C:7/1
      :62F:D230324EUR750000,00
      -`;

    const data = await statementParsers.mt940Parser.parse(Buffer.from(input), { [companyId]: cashPoolAccountId });

    expect(data).toEqual([
      {
        transactionReferenceNumber,
        cashPoolAccountId,
        statementNumber: '7/1',
        balanceChange: -750000,
        date: new Date('2023-03-23T23:00:00.000Z'),
      },
    ]);
  });

  test('It should correctly read and parse the Mt940 file from disk', async () => {
    const filePath = './tests/statementParsers/__mocks__/testMt940.txt';
    const transactionReferenceNumber = 'BNP-L1-20006-General Logistics Systems Italy SpA'; // From testMt940.txt
    const companyId = 20006; // From testMt940.txt
    const cashPoolAccountId = 333; // Arbitrary

    const data = await statementParsers.mt940Parser.parse(filePath, { [companyId]: cashPoolAccountId });

    expect(data).toEqual([
      {
        transactionReferenceNumber,
        cashPoolAccountId,
        statementNumber: '5/1',
        balanceChange: -900000,
        date: new Date('2023-08-21T22:00:00.000Z'),
      },
    ]);
  });
});
