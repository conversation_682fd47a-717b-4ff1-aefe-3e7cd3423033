import { GetCashPoolReturnType, TopCurrencyAccountType } from '../../types';
import * as annumUtils from '../../utils/cashPoolBatch/getBatchSolverInput/annumUtils';

describe('Cash Pool Batch Annum utils', () => {
  describe('Test getAnnumDivider function', () => {
    test('It should return 360 as a default', () => {
      expect(annumUtils.getAnnumDivider(null as unknown as string)).toBe(360);
    });

    test('It should return 360 as a default', () => {
      expect(annumUtils.getAnnumDivider(undefined as unknown as string)).toBe(360);
    });

    test('It should return 360 as a default', () => {
      expect(annumUtils.getAnnumDivider('')).toBe(360);
    });

    test('It should return 360 for a random currency', () => {
      expect(annumUtils.getAnnumDivider('CHF')).toBe(360);
    });

    test('It should return 365 for GBP currency', () => {
      expect(annumUtils.getAnnumDivider('GBP')).toBe(365);
    });
  });

  describe('Test mutateRatesByDividingWithAnnum function', () => {
    test('It test the mutation of rates', () => {
      const annumDivider = 360;
      const cashPool = { creditInterestRate: 3.3, debitInterestRate: 8.4 } as GetCashPoolReturnType;
      const topCurrencyAccount = { creditInterestRate: 3.6, debitInterestRate: 9.4 } as TopCurrencyAccountType;

      annumUtils.mutateRatesByDividingWithAnnum(annumDivider, cashPool, topCurrencyAccount);

      expect(cashPool.creditInterestRate).toBe(3.3 / annumDivider);
      expect(cashPool.debitInterestRate).toBe(8.4 / annumDivider);
      expect(topCurrencyAccount.creditInterestRate).toBe(3.6 / annumDivider);
      expect(topCurrencyAccount.debitInterestRate).toBe(9.4 / annumDivider);
    });
  });
});
