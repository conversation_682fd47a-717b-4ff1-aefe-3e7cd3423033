import {
  CashPoolFileType,
  CashPoolParticipantType,
  CashPoolType,
  CompanyType,
  TopCurrencyAccountType,
} from '../../../types';

const physicalCashPoolMock: CashPoolType & {
  leader: CompanyType;
  topCurrencyAccounts: Array<TopCurrencyAccountType>;
  participants: Array<CashPoolParticipantType>;
  files: Array<CashPoolFileType>;
} = {
  id: 321,
  leaderId: 32,
  clientId: 1,
  name: 'PhysicalCashPool',
  country: 'Croatia',
  type: 'Physical',
  currencies: 'USD',
  operatingCost: 0,
  operatingCostMarkup: 0,
  riskAnalysisAnswers: {
    guarantee: false,
    liquidityRisk1: false,
    liquidityRisk2: false,
    creditRisk1: false,
    creditRisk2: false,
    functions1: false,
    functions2: false,
    functions3: false,
    functions4: false,
    functions5: false,
    functions6: false,
  },
  assessment: 'Low',
  totalRisk: 0,
  creditInterestRate: 4,
  debitInterestRate: 8,
  grossBenefit: null,
  interestType: 'fixed',
  overnightRate: null,
  note: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  leader: {
    id: 32,
    parentCompanyId: 26,
    name: 'Apple Tree AS',
    industry: 'Energy',
    country: 'Norway',
    note: '',
    creditRating: {
      rating: 'BB+/Ba1',
      ratingAdj: 'AA-/Aa3',
      probabilityOfDefault: 5.3,
      probabilityOfDefaultAdj: 0.48,
    },
    assessment: {
      answers: {
        ringFencing: false,
        question1: true,
        question2: true,
        question3: false,
        question4: true,
        question5: true,
        question6: true,
        question7: true,
        question8: true,
        question9: true,
        question10: false,
      },
      name: 'Central',
    },
    createdBy: 'nord-staging',
    updatedBy: 'admin',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  topCurrencyAccounts: [
    {
      id: 192,
      cashPoolId: 321,
      name: 'PhysicalCashPool---TOP_CURRENCY_ACCOUNT_OF_PHYSICAL_CASHPOOL',
      currency: 'USD',
      interestType: 'fixed',
      overnightRate: null,
      creditInterestRate: 4,
      debitInterestRate: 8,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
  participants: [
    {
      id: 1527,
      companyId: 26,
      cashPoolId: 321,
      isLeader: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
  files: [],
};

export default physicalCashPoolMock;
