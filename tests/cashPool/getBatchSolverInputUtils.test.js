const { runRowGuards, getRates, getFinalSolverInput } = require('../../utils/cashPoolBatch/getBatchSolverInput/getBatchSolverInputUtils');
const { BadRequestError } = require('../../utils/ErrorHandler');
const cashPoolLeaderMock = require('./__mocks__/cashPoolLeader.json');
const fixedParticipantsDataMock = require('./__mocks__/fixedParticipantsData.json');
const fixedSolverInputMock = require('./__mocks__/fixedSolverInput.json');
const floatParticipantsDataMock = require('./__mocks__/floatParticipantsData.json');
const floatSolverInputMock = require('./__mocks__/floatSolverInput.json');
const physicalTopCurrencyAccountMock = require('./__mocks__/physicalTopCurrencyAccount.json');
const physicalCashPoolMock = require('./__mocks__/physicalCashPool').default;

const accountsByName = {
  'Cheese Tree B.V.': {
    accountId: 8,
    companyId: 31,
    name: 'Cheese Tree B.V.',
    creditInterestRate: 3,
    debitInterestRate: 9,
  },
  'Peach Tree SARL': {
    accountId: 9,
    companyId: 30,
    name: 'Peach Tree SARL',
    creditInterestRate: 3,
    debitInterestRate: 9,
  },
};

describe('Get Cash Pool Batch Solver Input Utils', () => {
  describe('Test getRates function', () => {
    test('It should return unchanged credit and debit rate for fixed pool', () => {
      const arg = { isFixed: true, creditInterestRate: 4, debitInterestRate: 8 };

      const { creditInterestRate, debitInterestRate } = getRates(arg);

      expect(creditInterestRate).toBe(4);
      expect(debitInterestRate).toBe(8);
    });

    test('It should throw 400 for missing overnight rate for float pool', () => {
      const arg = { isFixed: false, creditInterestRate: 400, debitInterestRate: 800 };

      expect(() => getRates(arg)).toThrow(BadRequestError);
    });

    test('It should return new credit and debit rate depending on overnight rate for float pool', () => {
      const arg = { isFixed: false, creditInterestRate: 200, debitInterestRate: 750, overnightRate: 2.4 };

      const { creditInterestRate, debitInterestRate } = getRates(arg);

      expect(creditInterestRate).toBe(4.4);
      expect(debitInterestRate).toBe(9.9);
    });

    test('It should return new credit and debit rate depending on overnight rate for float pool', () => {
      const arg = { isFixed: false, creditInterestRate: -250, debitInterestRate: -400, overnightRate: 5 };

      const { creditInterestRate, debitInterestRate } = getRates(arg);

      expect(creditInterestRate).toBe(2.5);
      expect(debitInterestRate).toBe(1);
    });

    test('It should return 0 when absolute of debit rate is larger then overnight rate for float pool', () => {
      const arg = { isFixed: false, creditInterestRate: -250, debitInterestRate: -600, overnightRate: 5 };

      const { creditInterestRate, debitInterestRate } = getRates(arg);

      expect(creditInterestRate).toBe(2.5);
      expect(debitInterestRate).toBe(0.013);
    });

    test('It should return 0 when absolute of credit and debit rates are larger then overnight rate for float pool', () => {
      const arg = { isFixed: false, creditInterestRate: -550, debitInterestRate: -600, overnightRate: 5 };

      const { creditInterestRate, debitInterestRate } = getRates(arg);

      expect(creditInterestRate).toBe(0.011);
      expect(debitInterestRate).toBe(0.013);
    });
  });

  describe('Test runRowGuards function', () => {
    test('It should throw 400 for an invalid date', () => {
      const arg = { date: 'INVALID_DATE' };
      expect(() => runRowGuards(arg)).toThrow(/Invalid date/);
    });

    test('It should throw 400 for a nonexistent participant', () => {
      const arg = { date: new Date(), name: 'Green Tree Group, Inc.', accountsByName };
      expect(() => runRowGuards(arg)).toThrow(/Invalid participant name/);
    });

    test('It should throw 400 for a nonexistent participant', () => {
      const arg = { date: new Date(), name: 'Cheese Tree B.V.', accountsByName, balance: 'Invalid Balance' };
      expect(() => runRowGuards(arg)).toThrow(/Invalid balance number/);
    });

    test('It should throw 400 for an invalid overnight rate', () => {
      const arg = {
        date: new Date(),
        name: 'Cheese Tree B.V.',
        accountsByName,
        balance: 200000,
        isFixed: false,
        overnightRate: 'Invalid Overnight Rate',
      };
      expect(() => runRowGuards(arg)).toThrow(/Invalid overnight rate/);
    });

    test('It should throw 400 for a missing overnight rate', () => {
      const arg = {
        date: new Date(),
        name: 'Cheese Tree B.V.',
        accountsByName,
        balance: 200000,
        isFixed: false,
      };
      expect(() => runRowGuards(arg)).toThrow(/Invalid overnight rate/);
    });

    test('It should return undefined when all checks pass for fixed', () => {
      const arg = { date: new Date(), name: 'Cheese Tree B.V.', accountsByName, balance: 200000, isFixed: true };
      expect(runRowGuards(arg)).toBe(undefined);
    });

    test('It should return undefined when all checks pass for float', () => {
      const arg = {
        date: new Date(),
        name: 'Cheese Tree B.V.',
        accountsByName,
        balance: 200000,
        isFixed: false,
        overnightRate: 2,
      };
      expect(runRowGuards(arg)).toBe(undefined);
    });
  });

  describe('Test getFinalSolverInput function', () => {
    test('It should return the final solver input for fixed cash pool', () => {
      const arg = {
        participantsData: fixedParticipantsDataMock,
        cashPool: physicalCashPoolMock,
        topCurrencyAccount: physicalTopCurrencyAccountMock,
        isFixed: true,
        cashPoolLeader: cashPoolLeaderMock,
      };
      expect(getFinalSolverInput(arg)).toEqual(fixedSolverInputMock);
    });

    test('It should return the final solver input for float cash pool', () => {
      const arg = {
        participantsData: floatParticipantsDataMock,
        cashPool: physicalCashPoolMock,
        topCurrencyAccount: physicalTopCurrencyAccountMock,
        isFixed: false,
        cashPoolLeader: cashPoolLeaderMock,
      };
      expect(getFinalSolverInput(arg)).toEqual(floatSolverInputMock);
    });
  });
});
