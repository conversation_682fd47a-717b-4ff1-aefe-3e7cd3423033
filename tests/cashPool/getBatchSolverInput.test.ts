import _ from 'lodash';

import getBatchSolverInput from '../../utils/cashPoolBatch/getBatchSolverInput';
import * as physicalCashPoolBatchSolverInputMocks from './__mocks__/getBatchSolverInput/physical';

describe('getBatchSolverInput tests', () => {
  test('getBatchSolverInput for Physical Cash Pool (fixed)', () => {
    const solverInput = getBatchSolverInput({
      cashPool: physicalCashPoolBatchSolverInputMocks.cashPool as any,
      topCurrencyAccount: physicalCashPoolBatchSolverInputMocks.cashPoolTopCurrencyAccount as any,
      accounts: physicalCashPoolBatchSolverInputMocks.fixedAccounts as any,
      batchFileInJson: [
        [new Date('2021-12-31T23:00:00.000Z'), 'Apple Tree AS', -********],
        [new Date('2021-12-31T23:00:00.000Z'), 'Banana Tree AS', *********],
        [new Date('2021-12-31T23:00:00.000Z'), 'Cherry Tree Ltd.', -********],
      ],
      cashPoolLeader: physicalCashPoolBatchSolverInputMocks.cashPoolLeader as any,
    });

    expect(solverInput).toEqual([
      {
        CPL: {
          credit_IR: 0.011111111111111112,
          debit_IR: 0.022222222222222223,
          markup: 0,
          operating_expenses: 0,
          name: 26,
        },
        CPP: [
          {
            balance: -********,
            credit_IR: 0.008333333333333333,
            debit_IR: 0.025,
            name: 32,
            accountId: 463,
            overnightRate: NaN,
          },
          {
            balance: *********,
            credit_IR: 0.007500000000000001,
            debit_IR: 0.023333333333333334,
            name: 146,
            accountId: 464,
            overnightRate: NaN,
          },
          {
            balance: -********,
            credit_IR: 0.009166666666666667,
            debit_IR: 0.025833333333333337,
            name: 37,
            accountId: 465,
            overnightRate: NaN,
          },
        ],
        date: 'Sat Jan 01 2022 00:00:00 GMT+0100 (Central European Standard Time)',
        totalRisk: 0.275,
      },
    ]);
  });

  test('getBatchSolverInput for Physical Cash Pool (float)', () => {
    const floatCashPoolOverrides = {
      creditInterestRate: 400,
      debitInterestRate: 800,
      interestType: 'float',
      overnightRate: 'BUBRON',
    };
    const floatCashPool = { ...physicalCashPoolBatchSolverInputMocks.cashPool, ...floatCashPoolOverrides };
    const floatTopCurrencyAccount = {
      ...physicalCashPoolBatchSolverInputMocks.cashPoolTopCurrencyAccount,
      ...floatCashPoolOverrides,
    };

    const solverInput = getBatchSolverInput({
      cashPool: floatCashPool as any,
      topCurrencyAccount: floatTopCurrencyAccount as any,
      accounts: physicalCashPoolBatchSolverInputMocks.floatAccounts as any,
      batchFileInJson: [
        [new Date('2021-12-31T23:00:00.000Z'), 'Apple Tree AS', -********, 2],
        [new Date('2021-12-31T23:00:00.000Z'), 'Banana Tree AS', *********, 2.2],
        [new Date('2021-12-31T23:00:00.000Z'), 'Cherry Tree Ltd.', -********, 2.4],
      ],
      cashPoolLeader: physicalCashPoolBatchSolverInputMocks.cashPoolLeader as any,
    });

    expect(solverInput).toEqual([
      {
        CPL: {
          credit_IR: 0.016666666666666666,
          debit_IR: 0.*****************,
          markup: 0,
          operating_expenses: 0,
          name: 26,
        },
        CPP: [
          {
            balance: -********,
            credit_IR: 0.013888888888888888,
            debit_IR: 0.027857371212121212,
            name: 32,
            accountId: 466,
            overnightRate: 0.005555555555555556,
          },
          {
            balance: *********,
            credit_IR: 0.014444444444444444,
            debit_IR: 0.028666040404040402,
            name: 146,
            accountId: 467,
            overnightRate: 0.006111111111111111,
          },
          {
            balance: -********,
            credit_IR: 0.015,
            debit_IR: 0.052254777777777775,
            name: 37,
            accountId: 468,
            overnightRate: 0.006666666666666666,
          },
        ],
        date: 'Sat Jan 01 2022 00:00:00 GMT+0100 (Central European Standard Time)',
        totalRisk: 0.275,
      },
    ]);
  });
});
