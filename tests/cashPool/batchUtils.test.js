const { cashPoolEnums } = require('../../enums');
const { getBatchStatus, getRates } = require('../../utils/cashPoolBatch');

describe('Cash Pool Batch Utils', () => {
  describe('Test getBatchStatus function', () => {
    test('It should return unpaid status for zero payments paid', () => {
      const paidPaymentsCount = 0;
      const totalPaymentsCount = 20;
      const status = getBatchStatus(paidPaymentsCount, totalPaymentsCount);

      expect(status).toBe(cashPoolEnums.batchStatus.UNPAID);
    });

    test('It should return partially paid status for one or more but not all payments paid', () => {
      const paidPaymentsCount = 12;
      const totalPaymentsCount = 20;
      const status = getBatchStatus(paidPaymentsCount, totalPaymentsCount);

      expect(status).toBe(cashPoolEnums.batchStatus.PARTIALLY_PAID);
    });

    test('It should return paid status for all payments paid', () => {
      const paidPaymentsCount = 20;
      const totalPaymentsCount = 20;
      const status = getBatchStatus(paidPaymentsCount, totalPaymentsCount);

      expect(status).toBe(cashPoolEnums.batchStatus.PAID);
    });
  });
});
