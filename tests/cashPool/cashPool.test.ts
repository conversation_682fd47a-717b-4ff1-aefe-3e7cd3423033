import request from 'supertest';

import app from '../../app';
import { cashPoolEnums } from '../../enums';
import models from '../../models';
import {
  cashPoolRepository,
  cashPoolBatchFileRepository,
  topCurrencyAccountRepository,
  cashPoolParticipantAccountRepository,
} from '../../repositories';
import { authSetup, azureSetup, featureSetup, rateLimiterSetup, transactionSetup } from '../setup';
import redisService from '../../services/redis';
import cashPoolUtils from '../../utils/cashPool/cashPoolUtils';
import { NotFoundError } from '../../utils/ErrorHandler';
import featureUtils from '../../utils/featureUtils/featureUtils';
import cashPoolParticipantsMock from './__mocks__/cashPoolParticipants.json';
import physicalCreateCashPoolMock from './__mocks__/physicalCreateCashPool.json';
import physicalCashPoolMock from './__mocks__/physicalCashPool';
import { GetCashPoolReturnType } from '../../types';

jest.mock('../../repositories/cashPoolRepository');
jest.mock('../../repositories/cashPoolBatchFileRepository');
jest.mock('../../repositories/cashPoolParticipantTrailRepository');
jest.mock('../../repositories/featureRepository');
jest.mock('../../repositories/topCurrencyAccountRepository');
jest.mock('../../repositories/cashPoolParticipantAccountRepository');
jest.mock('../../services/azureService');
jest.mock('../../middlewares/authMethodMiddleware');
jest.mock('../../middlewares/rateLimiter');
jest.mock('../../models');

const mockRunCashPoolCreateUpdateGuards = jest.spyOn(cashPoolUtils, 'runCashPoolCreateUpdateGuards');
mockRunCashPoolCreateUpdateGuards.mockImplementation(() => {});

const mockCheckCreateLimits = jest.spyOn(featureUtils, 'checkCreateLimits');
mockCheckCreateLimits.mockImplementation(async () => {});

interface CreatedParticipant {
  cashPoolId: number;
  companyId: number;
  isLeader?: boolean;
  creditInterestRate?: number;
  debitInterestRate?: number;
}

beforeAll(() => {
  rateLimiterSetup.basicRateLimiterSetup();
  authSetup.superadminAuthSetup();
  azureSetup.basicAzureSetup();
  featureSetup.enabledFeatureSetup();
});

afterAll(async () => {
  await redisService.quit();
});

describe('Cash Pool Controller', () => {
  test('It should get the cash pool', async () => {
    const mockGetCashPool = jest.spyOn(cashPoolRepository, 'getCashPool');
    mockGetCashPool.mockResolvedValue({ dataValues: physicalCashPoolMock } as unknown as GetCashPoolReturnType);
    const response = await request(app).get('/api/cash-pool/:id');

    const cashPool = response.body.dataValues;
    expect(response.statusCode).toBe(200);
    const properties = [
      'id',
      'leaderId',
      'clientId',
      'name',
      'country',
      'type',
      'currencies',
      'operatingCost',
      'operatingCostMarkup',
      'riskAnalysisAnswers',
      'assessment',
      'totalRisk',
      'creditInterestRate',
      'debitInterestRate',
      'grossBenefit',
      'interestType',
      'overnightRate',
      'note',
      'leader',
      'topCurrencyAccounts',
      'participants',
      'files',
      'participantsTotalBenefit',
      'leaderTotalBenefit',
    ];
    for (const property of properties) {
      expect(cashPool).toHaveProperty(property);
    }
  });

  test('It should throw 404 when cash pool is not found', async () => {
    const mockGetCashPool = jest.spyOn(cashPoolRepository, 'getCashPool');
    mockGetCashPool.mockImplementation(() => {
      throw new NotFoundError();
    });

    const response = await request(app).get('/api/cash-pool/:id');

    expect(response.statusCode).toBe(404);
  });

  test('It should return an empty array or array of cash pools', async () => {
    const mockGetCashPools = jest.spyOn(cashPoolRepository, 'getCashPools');
    mockGetCashPools.mockImplementation(() => Promise.resolve([]));
    const response = await request(app).get('/api/cash-pool');

    expect(response.statusCode).toBe(200);
    expect(response.body).toBeInstanceOf(Array);
  });

  test('It should get all cash pool participants', async () => {
    const mockGetCashPool = jest.spyOn(cashPoolRepository, 'getCashPool');
    mockGetCashPool.mockResolvedValue(physicalCashPoolMock as unknown as GetCashPoolReturnType);

    const mockGetAllCashPoolParticipants = jest.spyOn(cashPoolRepository, 'getAllCashPoolParticipants');
    mockGetAllCashPoolParticipants.mockImplementation(() => cashPoolParticipantsMock);

    const response = await request(app).get('/api/cash-pool/:id/participants');

    expect(response.statusCode).toBe(200);
    expect(response.body).toBeInstanceOf(Array);

    const cashPoolParticipant = response.body[0];
    const participantProperties = ['companyId', 'companyName', 'isLeader', 'topCurrencyAccountIds'];
    for (const property of participantProperties) {
      expect(cashPoolParticipant).toHaveProperty(property);
    }
  });

  test('It should create a new Physical Cash Pool', async () => {
    transactionSetup.mockTransaction(models);

    const mockCreateCashPool = jest.spyOn(cashPoolRepository, 'createCashPool');
    mockCreateCashPool.mockResolvedValue(physicalCashPoolMock);

    const mockCreateCashPoolParticipants = jest.spyOn(cashPoolRepository, 'createCashPoolParticipants');
    mockCreateCashPoolParticipants.mockImplementation((participants) =>
      participants.map((p: CreatedParticipant) => ({ id: Math.floor(Math.random() * 10000), ...p })),
    );

    const mockGetCashPoolParticipants = jest.spyOn(cashPoolUtils, 'getCashPoolParticipants');
    mockGetCashPoolParticipants.mockImplementation((cashPool, cashPoolId) =>
      cashPool.accounts.map((a: CreatedParticipant) => ({ ...a, cashPoolId })),
    );

    const mockCreateTopCurrencyAccount = jest.spyOn(topCurrencyAccountRepository, 'createTopCurrencyAccounts');
    mockCreateTopCurrencyAccount.mockImplementation((created) => [{ id: 224, ...created }]);

    const mockCreateCashPoolParticipantAccount = jest.spyOn(
      cashPoolParticipantAccountRepository,
      'createCashPoolParticipantAccount',
    );
    mockCreateCashPoolParticipantAccount.mockImplementation(() => {});

    const response = await request(app).post('/api/cash-pool/physical').send(physicalCreateCashPoolMock);
    expect(response.statusCode).toBe(200);
    expect(response.body.type).toBe(cashPoolEnums.cashPoolTypes.PHYSICAL);
  });

  test('It should update the cash pool note', async () => {
    const NUMBER_OF_AFFECTED_ROWS = 1;
    const body = { note: 'Cash pool note' };
    const mockUpdateCashPool = jest.spyOn(cashPoolRepository, 'updateCashPool');
    mockUpdateCashPool.mockImplementation(() => [NUMBER_OF_AFFECTED_ROWS, [{ physicalCashPoolMock, ...body }]]);

    const response = await request(app).patch('/api/cash-pool/:id/note').send(body);
    expect(response.statusCode).toBe(200);
    expect(response.body.note).toBe(body.note);
  });

  test('It should throw 404 when cash pool note to update is not found', async () => {
    const NUMBER_OF_AFFECTED_ROWS = 0;
    const body = { note: 'Cash pool note' };
    const mockUpdateCashPool = jest.spyOn(cashPoolRepository, 'updateCashPool');
    mockUpdateCashPool.mockImplementation(() => [NUMBER_OF_AFFECTED_ROWS, []]);

    const response = await request(app).patch('/api/cash-pool/:id/note').send(body);
    expect(response.statusCode).toBe(404);
  });

  test('It should delete the cash pool', async () => {
    const NUMBER_OF_DELETED_ROWS = 1;
    const mockBatchFiles = [{ id: 1 }, { id: 2 }];

    transactionSetup.mockTransaction(models);

    const mockGetCashPool = jest.spyOn(cashPoolRepository, 'getCashPool');
    const mockGetBatchFiles = jest.spyOn(cashPoolBatchFileRepository, 'getAllCashPoolBatchFiles');
    const mockDeleteCashPool = jest.spyOn(cashPoolRepository, 'deleteCashPool');

    mockGetCashPool.mockResolvedValue(physicalCashPoolMock as any);
    mockGetBatchFiles.mockResolvedValue(mockBatchFiles);
    mockDeleteCashPool.mockResolvedValue(NUMBER_OF_DELETED_ROWS);

    const response = await request(app).delete('/api/cash-pool/:id');
    expect(response.statusCode).toBe(204);
  });

  test('It should throw 404 when cash pool to delete is not found', async () => {
    const mockDeleteCashPool = jest.spyOn(cashPoolRepository, 'getCashPool');
    mockDeleteCashPool.mockResolvedValue(null);

    const response = await request(app).delete('/api/cash-pool/:id');
    expect(response.statusCode).toBe(404);
  });
});
