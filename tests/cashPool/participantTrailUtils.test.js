const moment = require('moment');
const { Op } = require('sequelize');

const { getFilterBy: getTrailsFilterBy } = require('../../utils/cashPoolParticipantTrail');

const startDate = new Date('2022-07-15');
const endDate = new Date('2023-07-15');
const dateFormat = 'YYYY-MM-DD';

describe('Cash Pool Participant Trail Utils', () => {
  describe('Test getFilterBy function', () => {
    test('It should return start date sequelize Op gte', () => {
      const filter = { startDate };

      const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(filter);

      expect(companyFilter).toEqual({});
      expect(topCurrencyAccountFilter).toEqual({});

      expect(moment(trailsFilter.date[Op.gte]).format(dateFormat)).toBe(moment(startDate).format(dateFormat));
      expect(trailsFilter.date[Op.lte]).toBeUndefined();
      expect(trailsFilter.date[Op.between]).toBeUndefined();
    });

    test('It should return end date sequelize Op lte', () => {
      const filter = { endDate };

      const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(filter);

      expect(companyFilter).toEqual({});
      expect(topCurrencyAccountFilter).toEqual({});

      expect(moment(trailsFilter.date[Op.lte]).format(dateFormat)).toBe(moment(endDate).format(dateFormat));
      expect(trailsFilter.date[Op.gte]).toBeUndefined();
      expect(trailsFilter.date[Op.between]).toBeUndefined();
    });

    test('It should return start and end date in sequelize Op between', () => {
      const filter = { startDate, endDate };

      const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(filter);

      expect(companyFilter).toEqual({});
      expect(topCurrencyAccountFilter).toEqual({});

      expect(moment(trailsFilter.date[Op.between][0]).format(dateFormat)).toBe(moment(startDate).format(dateFormat));
      expect(moment(trailsFilter.date[Op.between][1]).format(dateFormat)).toBe(moment(endDate).format(dateFormat));
      expect(trailsFilter.date[Op.lte]).toBeUndefined();
      expect(trailsFilter.date[Op.gte]).toBeUndefined();
    });

    test('It should return object with company ids', () => {
      const companyIds = [26, 108];
      const filter = { companyIds };

      const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(filter);

      expect(trailsFilter).toEqual({});
      expect(topCurrencyAccountFilter).toEqual({});

      expect(companyFilter).toEqual({ id: companyIds });
    });

    test('It should return an empty object when companyIds are null', () => {
      const companyIds = null;
      const filter = { companyIds };

      const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(filter);

      expect(trailsFilter).toEqual({});
      expect(topCurrencyAccountFilter).toEqual({});

      expect(companyFilter).toEqual({});
    });

    test('It should return an empty object when companyIds are an empty array', () => {
      const companyIds = [];
      const filter = { companyIds };

      const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(filter);

      expect(trailsFilter).toEqual({});
      expect(topCurrencyAccountFilter).toEqual({});

      expect(companyFilter).toEqual({});
    });

    test('It should return object with top currency account id', () => {
      const topCurrencyAccountId = 200;
      const filter = { topCurrencyAccountId };

      const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(filter);

      expect(trailsFilter).toEqual({});
      expect(companyFilter).toEqual({});

      expect(topCurrencyAccountFilter).toEqual({ id: topCurrencyAccountId });
    });

    test('It should return an empty object when topCurrencyAccountId is null', () => {
      const topCurrencyAccountId = null;
      const filter = { topCurrencyAccountId };

      const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(filter);

      expect(trailsFilter).toEqual({});
      expect(companyFilter).toEqual({});

      expect(topCurrencyAccountFilter).toEqual({});
    });

    test('It should return correct filters when all are combined', () => {
      const topCurrencyAccountId = 200;
      const companyIds = [26, 108];
      const filter = { startDate, endDate, companyIds, topCurrencyAccountId };

      const [trailsFilter, companyFilter, topCurrencyAccountFilter] = getTrailsFilterBy(filter);

      expect(moment(trailsFilter.date[Op.between][0]).format(dateFormat)).toBe(moment(startDate).format(dateFormat));
      expect(moment(trailsFilter.date[Op.between][1]).format(dateFormat)).toBe(moment(endDate).format(dateFormat));
      expect(trailsFilter.date[Op.lte]).toBeUndefined();
      expect(trailsFilter.date[Op.gte]).toBeUndefined();

      expect(companyFilter).toEqual({ id: companyIds });

      expect(topCurrencyAccountFilter).toEqual({ id: topCurrencyAccountId });
    });
  });
});
